# 智慧化光伏场站软件设计方案

## 1. 软件架构设计

### 1.1 总体架构

#### 分层架构设计
```
┌─────────────────────────────────────────────────────────────┐
│                    应用展示层 (Presentation Layer)            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  Web控制台  │ │  移动APP    │ │  大屏展示   │ │ API网关 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    业务逻辑层 (Business Layer)               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  巡检服务   │ │  运维服务   │ │  安防服务   │ │ AI服务  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  调度服务   │ │  分析服务   │ │  告警服务   │ │ 报表服务│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    数据访问层 (Data Access Layer)            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  数据网关   │ │  缓存服务   │ │  消息队列   │ │ 文件存储│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    数据存储层 (Data Storage Layer)           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 时序数据库  │ │ 关系数据库  │ │ 文档数据库  │ │ 图数据库│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### 微服务架构
采用微服务架构模式，将系统拆分为多个独立的服务单元：

**核心服务**
- **设备管理服务**：设备注册、状态监控、配置管理
- **数据采集服务**：传感器数据采集、预处理、存储
- **巡检服务**：无人机/机器人任务调度、路径规划
- **AI分析服务**：图像识别、故障预测、智能优化
- **告警服务**：实时告警、事件处理、通知推送
- **用户管理服务**：用户认证、权限管理、角色控制

**支撑服务**
- **配置中心**：统一配置管理
- **服务注册中心**：服务发现与注册
- **API网关**：统一入口、路由、限流
- **监控服务**：系统监控、日志收集
- **消息中间件**：异步消息处理

### 1.2 技术栈选择

#### 后端技术栈
```yaml
开发语言: Java 17 + Spring Boot 3.0
微服务框架: Spring Cloud 2022
数据库:
  - 时序数据库: InfluxDB 2.0
  - 关系数据库: PostgreSQL 14
  - 缓存数据库: Redis 7.0
  - 搜索引擎: Elasticsearch 8.0
消息队列: Apache Kafka 3.0
容器化: Docker + Kubernetes
API文档: OpenAPI 3.0 + Swagger
```

#### 前端技术栈
```yaml
Web前端: Vue.js 3.0 + TypeScript
移动端: Flutter 3.0
UI组件库: Element Plus / Ant Design Vue
状态管理: Pinia
构建工具: Vite
地图组件: Leaflet.js / AMap
图表组件: ECharts / D3.js
```

#### AI/ML技术栈
```yaml
深度学习框架: PyTorch 2.0 / TensorFlow 2.0
计算机视觉: OpenCV 4.0
图像处理: PIL / Scikit-image
机器学习: Scikit-learn
模型服务: TorchServe / TensorFlow Serving
GPU计算: CUDA 11.8
```

## 2. 核心模块设计

### 2.1 设备管理模块

#### 功能架构
```
设备管理模块
├── 设备注册管理
│   ├── 设备信息录入
│   ├── 设备类型管理
│   ├── 设备状态监控
│   └── 设备配置管理
├── 通信协议适配
│   ├── Modbus协议适配
│   ├── IEC61850协议适配
│   ├── MQTT协议适配
│   └── 自定义协议适配
├── 设备控制指令
│   ├── 远程控制指令
│   ├── 参数配置指令
│   ├── 状态查询指令
│   └── 固件升级指令
└── 设备健康管理
    ├── 设备状态评估
    ├── 故障诊断分析
    ├── 维护计划管理
    └── 备件库存管理
```

#### 核心类设计
```java
// 设备基础信息
@Entity
@Table(name = "device_info")
public class DeviceInfo {
    @Id
    private String deviceId;
    private String deviceName;
    private DeviceType deviceType;
    private String manufacturer;
    private String model;
    private String serialNumber;
    private DeviceStatus status;
    private LocalDateTime installDate;
    private String location;
    private Map<String, Object> properties;
}

// 设备管理服务
@Service
public class DeviceManagementService {

    @Autowired
    private DeviceRepository deviceRepository;

    @Autowired
    private ProtocolAdapterFactory protocolAdapterFactory;

    // 设备注册
    public DeviceInfo registerDevice(DeviceRegistrationRequest request) {
        DeviceInfo device = new DeviceInfo();
        // 设备信息设置
        device.setDeviceId(generateDeviceId());
        device.setDeviceName(request.getDeviceName());
        device.setDeviceType(request.getDeviceType());
        // 保存到数据库
        return deviceRepository.save(device);
    }

    // 设备状态更新
    public void updateDeviceStatus(String deviceId, DeviceStatus status) {
        DeviceInfo device = deviceRepository.findById(deviceId)
            .orElseThrow(() -> new DeviceNotFoundException(deviceId));
        device.setStatus(status);
        device.setLastUpdateTime(LocalDateTime.now());
        deviceRepository.save(device);

        // 发送状态变更事件
        eventPublisher.publishEvent(new DeviceStatusChangedEvent(deviceId, status));
    }

    // 设备控制
    public CommandResult controlDevice(String deviceId, DeviceCommand command) {
        DeviceInfo device = getDeviceById(deviceId);
        ProtocolAdapter adapter = protocolAdapterFactory.getAdapter(device.getProtocol());
        return adapter.executeCommand(device, command);
    }
}
```

### 2.2 数据采集模块

#### 数据采集架构
```
数据采集模块
├── 数据源适配
│   ├── 传感器数据采集
│   ├── 设备状态数据
│   ├── 图像视频数据
│   └── 环境监测数据
├── 数据预处理
│   ├── 数据清洗过滤
│   ├── 数据格式转换
│   ├── 数据质量检查
│   └── 数据压缩存储
├── 实时数据流
│   ├── 流式数据处理
│   ├── 实时计算分析
│   ├── 告警阈值检测
│   └── 数据分发推送
└── 历史数据管理
    ├── 数据归档存储
    ├── 数据备份恢复
    ├── 数据生命周期
    └── 数据查询服务
```

#### 数据采集服务设计
```java
// 数据点定义
@Document(collection = "data_points")
public class DataPoint {
    @Id
    private String id;
    private String deviceId;
    private String tagName;
    private Object value;
    private DataType dataType;
    private DataQuality quality;
    private Instant timestamp;
    private Map<String, Object> attributes;
}

// 数据采集服务
@Service
public class DataCollectionService {

    @Autowired
    private InfluxDBTemplate influxDBTemplate;

    @Autowired
    private KafkaTemplate<String, Object> kafkaTemplate;

    // 批量数据采集
    @Scheduled(fixedRate = 1000) // 每秒执行
    public void collectData() {
        List<DeviceInfo> devices = deviceService.getActiveDevices();

        devices.parallelStream().forEach(device -> {
            try {
                List<DataPoint> dataPoints = collectDeviceData(device);
                processDataPoints(dataPoints);
            } catch (Exception e) {
                log.error("数据采集失败: deviceId={}", device.getDeviceId(), e);
            }
        });
    }

    // 实时数据处理
    @KafkaListener(topics = "sensor-data")
    public void processRealTimeData(DataPoint dataPoint) {
        // 数据质量检查
        if (validateDataQuality(dataPoint)) {
            // 存储到时序数据库
            influxDBTemplate.write(dataPoint);

            // 实时告警检测
            checkAlarmThreshold(dataPoint);

            // 推送到实时数据流
            webSocketService.pushRealTimeData(dataPoint);
        }
    }

    // 数据质量验证
    private boolean validateDataQuality(DataPoint dataPoint) {
        // 数值范围检查
        if (!isValueInRange(dataPoint)) {
            return false;
        }

        // 时间戳有效性检查
        if (!isTimestampValid(dataPoint)) {
            return false;
        }

        // 数据完整性检查
        return isDataComplete(dataPoint);
    }
}
```

### 2.3 AI分析模块

#### AI服务架构
```
AI分析模块
├── 图像识别服务
│   ├── 缺陷检测模型
│   ├── 热斑识别模型
│   ├── 污渍检测模型
│   └── 遮挡识别模型
├── 故障预测服务
│   ├── 时序预测模型
│   ├── 异常检测模型
│   ├── 设备寿命预测
│   └── 维护建议生成
├── 智能优化服务
│   ├── 路径规划优化
│   ├── 任务调度优化
│   ├── 资源配置优化
│   └── 能效优化分析
└── 模型管理服务
    ├── 模型版本管理
    ├── 模型训练监控
    ├── 模型性能评估
    └── 模型自动更新
```

#### AI服务实现
```python
# AI分析服务基类
class AIAnalysisService:
    def __init__(self, model_path: str):
        self.model = self.load_model(model_path)
        self.preprocessor = DataPreprocessor()
        self.postprocessor = ResultPostprocessor()

    def load_model(self, model_path: str):
        """加载AI模型"""
        return torch.load(model_path, map_location='cuda')

    def predict(self, input_data):
        """模型预测"""
        # 数据预处理
        processed_data = self.preprocessor.process(input_data)

        # 模型推理
        with torch.no_grad():
            output = self.model(processed_data)

        # 结果后处理
        result = self.postprocessor.process(output)
        return result

# 缺陷检测服务
class DefectDetectionService(AIAnalysisService):
    def __init__(self):
        super().__init__('models/defect_detection.pth')
        self.confidence_threshold = 0.8

    def detect_defects(self, image_data: bytes) -> List[DefectResult]:
        """检测光伏板缺陷"""
        # 图像预处理
        image = self.preprocess_image(image_data)

        # 缺陷检测
        detections = self.predict(image)

        # 结果过滤和格式化
        results = []
        for detection in detections:
            if detection.confidence > self.confidence_threshold:
                result = DefectResult(
                    defect_type=detection.class_name,
                    confidence=detection.confidence,
                    bbox=detection.bbox,
                    severity=self.calculate_severity(detection)
                )
                results.append(result)

        return results

    def preprocess_image(self, image_data: bytes):
        """图像预处理"""
        image = cv2.imdecode(np.frombuffer(image_data, np.uint8), cv2.IMREAD_COLOR)
        image = cv2.resize(image, (640, 640))
        image = image.astype(np.float32) / 255.0
        return torch.from_numpy(image).permute(2, 0, 1).unsqueeze(0)

# 故障预测服务
class FaultPredictionService:
    def __init__(self):
        self.lstm_model = self.load_lstm_model()
        self.feature_extractor = FeatureExtractor()

    def predict_fault(self, device_id: str, time_window: int = 30) -> FaultPrediction:
        """预测设备故障"""
        # 获取历史数据
        historical_data = self.get_historical_data(device_id, time_window)

        # 特征提取
        features = self.feature_extractor.extract(historical_data)

        # LSTM预测
        prediction = self.lstm_model.predict(features)

        # 结果解析
        fault_probability = prediction[0]
        predicted_fault_time = prediction[1]
        fault_type = prediction[2]

        return FaultPrediction(
            device_id=device_id,
            fault_probability=fault_probability,
            predicted_time=predicted_fault_time,
            fault_type=fault_type,
            confidence=prediction[3]
        )

# Java集成服务
@RestController
@RequestMapping("/api/ai")
public class AIAnalysisController {

    @Autowired
    private AIAnalysisService aiAnalysisService;

    @PostMapping("/detect-defects")
    public ResponseEntity<List<DefectResult>> detectDefects(
            @RequestParam("image") MultipartFile imageFile) {
        try {
            byte[] imageData = imageFile.getBytes();
            List<DefectResult> results = aiAnalysisService.detectDefects(imageData);
            return ResponseEntity.ok(results);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/predict-fault/{deviceId}")
    public ResponseEntity<FaultPrediction> predictFault(@PathVariable String deviceId) {
        FaultPrediction prediction = aiAnalysisService.predictFault(deviceId);
        return ResponseEntity.ok(prediction);
    }
}
```

### 2.4 任务调度模块

#### 调度系统架构
```
任务调度模块
├── 任务管理
│   ├── 任务创建与配置
│   ├── 任务优先级管理
│   ├── 任务状态跟踪
│   └── 任务执行历史
├── 调度引擎
│   ├── 调度算法实现
│   ├── 资源分配管理
│   ├── 负载均衡控制
│   └── 故障转移处理
├── 执行器管理
│   ├── 无人机执行器
│   ├── 机器人执行器
│   ├── 设备控制执行器
│   └── 数据处理执行器
└── 监控告警
    ├── 任务执行监控
    ├── 资源使用监控
    ├── 性能指标统计
    └── 异常告警处理
```

#### 任务调度实现
```java
// 任务定义
@Entity
@Table(name = "scheduled_tasks")
public class ScheduledTask {
    @Id
    private String taskId;
    private String taskName;
    private TaskType taskType;
    private TaskPriority priority;
    private TaskStatus status;
    private String cronExpression;
    private Map<String, Object> parameters;
    private LocalDateTime nextExecutionTime;
    private LocalDateTime lastExecutionTime;
    private String executorId;
}

// 调度服务
@Service
public class TaskSchedulingService {

    @Autowired
    private TaskRepository taskRepository;

    @Autowired
    private ExecutorManager executorManager;

    @Autowired
    private TaskQueue taskQueue;

    // 任务调度主循环
    @Scheduled(fixedRate = 5000) // 每5秒执行一次
    public void scheduleTask() {
        // 获取待执行任务
        List<ScheduledTask> pendingTasks = taskRepository.findPendingTasks();

        for (ScheduledTask task : pendingTasks) {
            if (shouldExecuteTask(task)) {
                // 分配执行器
                TaskExecutor executor = executorManager.allocateExecutor(task);
                if (executor != null) {
                    // 提交任务执行
                    submitTaskExecution(task, executor);
                } else {
                    // 加入等待队列
                    taskQueue.enqueue(task);
                }
            }
        }
    }

    // 任务执行提交
    private void submitTaskExecution(ScheduledTask task, TaskExecutor executor) {
        TaskExecution execution = new TaskExecution(task, executor);

        // 异步执行任务
        CompletableFuture.supplyAsync(() -> {
            try {
                TaskResult result = executor.execute(task);
                handleTaskCompletion(task, result);
                return result;
            } catch (Exception e) {
                handleTaskFailure(task, e);
                throw new RuntimeException(e);
            }
        });

        // 更新任务状态
        task.setStatus(TaskStatus.RUNNING);
        task.setLastExecutionTime(LocalDateTime.now());
        taskRepository.save(task);
    }

    // 智能调度算法
    public List<ScheduledTask> optimizeTaskSchedule(List<ScheduledTask> tasks) {
        // 使用遗传算法优化任务调度
        GeneticAlgorithm ga = new GeneticAlgorithm();
        ga.setPopulationSize(100);
        ga.setMaxGenerations(50);

        // 定义适应度函数
        ga.setFitnessFunction(schedule -> {
            double totalTime = calculateTotalExecutionTime(schedule);
            double resourceUtilization = calculateResourceUtilization(schedule);
            double priorityScore = calculatePriorityScore(schedule);

            // 多目标优化：时间、资源利用率、优先级
            return 1.0 / (totalTime * 0.4 + (1 - resourceUtilization) * 0.3 +
                         (1 - priorityScore) * 0.3);
        });

        return ga.optimize(tasks);
    }
}

// 执行器管理
@Component
public class ExecutorManager {

    private final Map<String, TaskExecutor> executors = new ConcurrentHashMap<>();
    private final LoadBalancer loadBalancer = new RoundRobinLoadBalancer();

    @PostConstruct
    public void initializeExecutors() {
        // 初始化无人机执行器
        registerExecutor(new DroneTaskExecutor("drone-1"));
        registerExecutor(new DroneTaskExecutor("drone-2"));

        // 初始化机器人执行器
        registerExecutor(new RobotTaskExecutor("robot-1"));
        registerExecutor(new RobotTaskExecutor("robot-2"));

        // 初始化设备控制执行器
        registerExecutor(new DeviceControlExecutor("device-ctrl-1"));
    }

    public TaskExecutor allocateExecutor(ScheduledTask task) {
        List<TaskExecutor> availableExecutors = getAvailableExecutors(task.getTaskType());

        if (availableExecutors.isEmpty()) {
            return null;
        }

        // 负载均衡选择执行器
        return loadBalancer.select(availableExecutors);
    }

    private List<TaskExecutor> getAvailableExecutors(TaskType taskType) {
        return executors.values().stream()
            .filter(executor -> executor.canHandle(taskType))
            .filter(executor -> executor.isAvailable())
            .collect(Collectors.toList());
    }
}
```

## 3. 数据库设计

### 3.1 数据库架构

#### 多数据库架构
```
数据存储架构
├── 时序数据库 (InfluxDB)
│   ├── 传感器实时数据
│   ├── 设备运行参数
│   ├── 环境监测数据
│   └── 性能指标数据
├── 关系数据库 (PostgreSQL)
│   ├── 用户权限数据
│   ├── 设备配置信息
│   ├── 任务调度数据
│   └── 系统配置数据
├── 文档数据库 (MongoDB)
│   ├── 图像视频文件
│   ├── 分析报告文档
│   ├── 日志记录数据
│   └── 非结构化数据
└── 图数据库 (Neo4j)
    ├── 设备拓扑关系
    ├── 故障传播路径
    ├── 依赖关系图谱
    └── 知识图谱数据
```

### 3.2 核心数据表设计

#### 设备管理相关表
```sql
-- 设备信息表
CREATE TABLE device_info (
    device_id VARCHAR(50) PRIMARY KEY,
    device_name VARCHAR(100) NOT NULL,
    device_type VARCHAR(50) NOT NULL,
    manufacturer VARCHAR(100),
    model VARCHAR(100),
    serial_number VARCHAR(100),
    install_date DATE,
    location_x DECIMAL(10,6),
    location_y DECIMAL(10,6),
    status VARCHAR(20) DEFAULT 'ACTIVE',
    properties JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 设备参数配置表
CREATE TABLE device_parameters (
    id BIGSERIAL PRIMARY KEY,
    device_id VARCHAR(50) REFERENCES device_info(device_id),
    parameter_name VARCHAR(100) NOT NULL,
    parameter_value TEXT,
    data_type VARCHAR(20),
    unit VARCHAR(20),
    min_value DECIMAL,
    max_value DECIMAL,
    is_readonly BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 设备状态历史表
CREATE TABLE device_status_history (
    id BIGSERIAL PRIMARY KEY,
    device_id VARCHAR(50) REFERENCES device_info(device_id),
    status VARCHAR(20) NOT NULL,
    previous_status VARCHAR(20),
    change_reason TEXT,
    changed_by VARCHAR(50),
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 任务调度相关表
```sql
-- 调度任务表
CREATE TABLE scheduled_tasks (
    task_id VARCHAR(50) PRIMARY KEY,
    task_name VARCHAR(200) NOT NULL,
    task_type VARCHAR(50) NOT NULL,
    priority INTEGER DEFAULT 5,
    status VARCHAR(20) DEFAULT 'PENDING',
    cron_expression VARCHAR(100),
    parameters JSONB,
    executor_id VARCHAR(50),
    next_execution_time TIMESTAMP,
    last_execution_time TIMESTAMP,
    created_by VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 任务执行历史表
CREATE TABLE task_execution_history (
    execution_id BIGSERIAL PRIMARY KEY,
    task_id VARCHAR(50) REFERENCES scheduled_tasks(task_id),
    executor_id VARCHAR(50),
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    status VARCHAR(20),
    result_data JSONB,
    error_message TEXT,
    execution_duration INTEGER, -- 执行时长(秒)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 执行器信息表
CREATE TABLE task_executors (
    executor_id VARCHAR(50) PRIMARY KEY,
    executor_name VARCHAR(100) NOT NULL,
    executor_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    capabilities JSONB,
    current_load INTEGER DEFAULT 0,
    max_concurrent_tasks INTEGER DEFAULT 1,
    last_heartbeat TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 用户权限相关表
```sql
-- 用户信息表
CREATE TABLE users (
    user_id VARCHAR(50) PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    full_name VARCHAR(100),
    status VARCHAR(20) DEFAULT 'ACTIVE',
    last_login_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 角色信息表
CREATE TABLE roles (
    role_id VARCHAR(50) PRIMARY KEY,
    role_name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    permissions JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户角色关联表
CREATE TABLE user_roles (
    user_id VARCHAR(50) REFERENCES users(user_id),
    role_id VARCHAR(50) REFERENCES roles(role_id),
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by VARCHAR(50),
    PRIMARY KEY (user_id, role_id)
);
```

### 3.3 时序数据设计

#### InfluxDB数据结构
```python
# 传感器数据点结构
sensor_data = {
    "measurement": "sensor_readings",
    "tags": {
        "device_id": "sensor_001",
        "device_type": "temperature_sensor",
        "location": "zone_a",
        "unit": "celsius"
    },
    "fields": {
        "value": 25.6,
        "quality": "good",
        "raw_value": 2560
    },
    "time": "2024-01-15T10:30:00Z"
}

# 设备运行数据结构
device_data = {
    "measurement": "device_metrics",
    "tags": {
        "device_id": "inverter_001",
        "device_type": "inverter",
        "manufacturer": "huawei"
    },
    "fields": {
        "input_voltage": 800.5,
        "output_voltage": 380.2,
        "current": 15.8,
        "power": 6000.0,
        "efficiency": 98.5,
        "temperature": 45.2
    },
    "time": "2024-01-15T10:30:00Z"
}

# 性能指标数据结构
performance_data = {
    "measurement": "system_performance",
    "tags": {
        "system": "solar_plant",
        "subsystem": "generation"
    },
    "fields": {
        "total_power": 50000.0,
        "efficiency": 85.2,
        "availability": 99.5,
        "capacity_factor": 23.8
    },
    "time": "2024-01-15T10:30:00Z"
}
```

## 4. 接口设计

### 4.1 RESTful API设计

#### API设计原则
- **RESTful风格**：遵循REST架构风格
- **统一响应格式**：标准化的响应数据结构
- **版本控制**：支持API版本管理
- **安全认证**：JWT Token认证机制
- **限流控制**：API调用频率限制
- **文档完整**：完整的API文档

#### 统一响应格式
```java
// 统一响应包装类
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiResponse<T> {
    private int code;           // 响应码
    private String message;     // 响应消息
    private T data;            // 响应数据
    private long timestamp;    // 时间戳
    private String requestId;  // 请求ID

    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(200, "Success", data,
            System.currentTimeMillis(), generateRequestId());
    }

    public static <T> ApiResponse<T> error(int code, String message) {
        return new ApiResponse<>(code, message, null,
            System.currentTimeMillis(), generateRequestId());
    }
}

// 分页响应类
@Data
public class PageResponse<T> {
    private List<T> items;      // 数据列表
    private long total;         // 总数量
    private int page;           // 当前页
    private int size;           // 页大小
    private int totalPages;     // 总页数
}
```

#### 核心API接口

**设备管理API**
```java
@RestController
@RequestMapping("/api/v1/devices")
@Api(tags = "设备管理")
public class DeviceController {

    @GetMapping
    @ApiOperation("获取设备列表")
    public ApiResponse<PageResponse<DeviceInfo>> getDevices(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String deviceType,
            @RequestParam(required = false) String status) {

        PageResponse<DeviceInfo> devices = deviceService.getDevices(page, size, deviceType, status);
        return ApiResponse.success(devices);
    }

    @GetMapping("/{deviceId}")
    @ApiOperation("获取设备详情")
    public ApiResponse<DeviceInfo> getDevice(@PathVariable String deviceId) {
        DeviceInfo device = deviceService.getDeviceById(deviceId);
        return ApiResponse.success(device);
    }

    @PostMapping
    @ApiOperation("创建设备")
    public ApiResponse<DeviceInfo> createDevice(@RequestBody @Valid DeviceCreateRequest request) {
        DeviceInfo device = deviceService.createDevice(request);
        return ApiResponse.success(device);
    }

    @PutMapping("/{deviceId}")
    @ApiOperation("更新设备")
    public ApiResponse<DeviceInfo> updateDevice(
            @PathVariable String deviceId,
            @RequestBody @Valid DeviceUpdateRequest request) {
        DeviceInfo device = deviceService.updateDevice(deviceId, request);
        return ApiResponse.success(device);
    }

    @DeleteMapping("/{deviceId}")
    @ApiOperation("删除设备")
    public ApiResponse<Void> deleteDevice(@PathVariable String deviceId) {
        deviceService.deleteDevice(deviceId);
        return ApiResponse.success(null);
    }

    @PostMapping("/{deviceId}/control")
    @ApiOperation("设备控制")
    public ApiResponse<CommandResult> controlDevice(
            @PathVariable String deviceId,
            @RequestBody @Valid DeviceControlRequest request) {
        CommandResult result = deviceService.controlDevice(deviceId, request);
        return ApiResponse.success(result);
    }

    @GetMapping("/{deviceId}/data")
    @ApiOperation("获取设备数据")
    public ApiResponse<List<DataPoint>> getDeviceData(
            @PathVariable String deviceId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @RequestParam(required = false) List<String> tags) {

        List<DataPoint> data = dataService.getDeviceData(deviceId, startTime, endTime, tags);
        return ApiResponse.success(data);
    }
}

// 任务管理API
@RestController
@RequestMapping("/api/v1/tasks")
@Api(tags = "任务管理")
public class TaskController {

    @GetMapping
    @ApiOperation("获取任务列表")
    public ApiResponse<PageResponse<ScheduledTask>> getTasks(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String taskType,
            @RequestParam(required = false) String status) {

        PageResponse<ScheduledTask> tasks = taskService.getTasks(page, size, taskType, status);
        return ApiResponse.success(tasks);
    }

    @PostMapping
    @ApiOperation("创建任务")
    public ApiResponse<ScheduledTask> createTask(@RequestBody @Valid TaskCreateRequest request) {
        ScheduledTask task = taskService.createTask(request);
        return ApiResponse.success(task);
    }

    @PostMapping("/{taskId}/execute")
    @ApiOperation("立即执行任务")
    public ApiResponse<TaskExecution> executeTask(@PathVariable String taskId) {
        TaskExecution execution = taskService.executeTask(taskId);
        return ApiResponse.success(execution);
    }

    @PutMapping("/{taskId}/pause")
    @ApiOperation("暂停任务")
    public ApiResponse<Void> pauseTask(@PathVariable String taskId) {
        taskService.pauseTask(taskId);
        return ApiResponse.success(null);
    }

    @PutMapping("/{taskId}/resume")
    @ApiOperation("恢复任务")
    public ApiResponse<Void> resumeTask(@PathVariable String taskId) {
        taskService.resumeTask(taskId);
        return ApiResponse.success(null);
    }

    @GetMapping("/{taskId}/executions")
    @ApiOperation("获取任务执行历史")
    public ApiResponse<PageResponse<TaskExecution>> getTaskExecutions(
            @PathVariable String taskId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {

        PageResponse<TaskExecution> executions = taskService.getTaskExecutions(taskId, page, size);
        return ApiResponse.success(executions);
    }
}

// AI分析API
@RestController
@RequestMapping("/api/v1/ai")
@Api(tags = "AI分析")
public class AIAnalysisController {

    @PostMapping("/detect-defects")
    @ApiOperation("缺陷检测")
    public ApiResponse<List<DefectResult>> detectDefects(
            @RequestParam("image") MultipartFile imageFile,
            @RequestParam(defaultValue = "0.8") double confidenceThreshold) {

        List<DefectResult> results = aiService.detectDefects(imageFile, confidenceThreshold);
        return ApiResponse.success(results);
    }

    @PostMapping("/predict-fault")
    @ApiOperation("故障预测")
    public ApiResponse<FaultPrediction> predictFault(
            @RequestBody @Valid FaultPredictionRequest request) {

        FaultPrediction prediction = aiService.predictFault(request);
        return ApiResponse.success(prediction);
    }

    @PostMapping("/optimize-route")
    @ApiOperation("路径优化")
    public ApiResponse<RouteOptimizationResult> optimizeRoute(
            @RequestBody @Valid RouteOptimizationRequest request) {

        RouteOptimizationResult result = aiService.optimizeRoute(request);
        return ApiResponse.success(result);
    }

    @GetMapping("/models")
    @ApiOperation("获取AI模型列表")
    public ApiResponse<List<AIModel>> getAIModels() {
        List<AIModel> models = aiService.getAvailableModels();
        return ApiResponse.success(models);
    }

    @PostMapping("/models/{modelId}/train")
    @ApiOperation("训练AI模型")
    public ApiResponse<TrainingJob> trainModel(
            @PathVariable String modelId,
            @RequestBody @Valid ModelTrainingRequest request) {

        TrainingJob job = aiService.trainModel(modelId, request);
        return ApiResponse.success(job);
    }
}
```

### 4.2 WebSocket实时通信

#### WebSocket配置
```java
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(new RealTimeDataHandler(), "/ws/realtime")
                .setAllowedOrigins("*")
                .withSockJS();

        registry.addHandler(new AlarmHandler(), "/ws/alarms")
                .setAllowedOrigins("*")
                .withSockJS();

        registry.addHandler(new TaskStatusHandler(), "/ws/tasks")
                .setAllowedOrigins("*")
                .withSockJS();
    }
}

// 实时数据推送处理器
@Component
public class RealTimeDataHandler extends TextWebSocketHandler {

    private final Set<WebSocketSession> sessions = ConcurrentHashMap.newKeySet();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        sessions.add(session);
        log.info("WebSocket连接建立: {}", session.getId());
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        sessions.remove(session);
        log.info("WebSocket连接关闭: {}", session.getId());
    }

    @EventListener
    public void handleRealTimeData(RealTimeDataEvent event) {
        String message = JSON.toJSONString(event.getData());

        sessions.parallelStream().forEach(session -> {
            try {
                if (session.isOpen()) {
                    session.sendMessage(new TextMessage(message));
                }
            } catch (Exception e) {
                log.error("发送实时数据失败", e);
            }
        });
    }

    // 推送设备状态更新
    public void pushDeviceStatus(String deviceId, DeviceStatus status) {
        DeviceStatusMessage message = new DeviceStatusMessage(deviceId, status, System.currentTimeMillis());
        broadcastMessage(message);
    }

    // 推送传感器数据
    public void pushSensorData(List<DataPoint> dataPoints) {
        SensorDataMessage message = new SensorDataMessage(dataPoints, System.currentTimeMillis());
        broadcastMessage(message);
    }

    private void broadcastMessage(Object message) {
        String json = JSON.toJSONString(message);
        sessions.parallelStream().forEach(session -> {
            try {
                if (session.isOpen()) {
                    session.sendMessage(new TextMessage(json));
                }
            } catch (Exception e) {
                log.error("广播消息失败", e);
                sessions.remove(session);
            }
        });
    }
}
```

### 4.3 消息队列集成

#### Kafka消息处理
```java
// 消息生产者配置
@Configuration
public class KafkaProducerConfig {

    @Bean
    public ProducerFactory<String, Object> producerFactory() {
        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, "localhost:9092");
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, JsonSerializer.class);
        props.put(ProducerConfig.ACKS_CONFIG, "all");
        props.put(ProducerConfig.RETRIES_CONFIG, 3);
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 16384);
        props.put(ProducerConfig.LINGER_MS_CONFIG, 1);
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 33554432);

        return new DefaultKafkaProducerFactory<>(props);
    }

    @Bean
    public KafkaTemplate<String, Object> kafkaTemplate() {
        return new KafkaTemplate<>(producerFactory());
    }
}

// 消息服务
@Service
public class MessageService {

    @Autowired
    private KafkaTemplate<String, Object> kafkaTemplate;

    // 发送设备数据
    public void sendDeviceData(String deviceId, DataPoint dataPoint) {
        DeviceDataMessage message = new DeviceDataMessage(deviceId, dataPoint);
        kafkaTemplate.send("device-data", deviceId, message);
    }

    // 发送告警消息
    public void sendAlarm(AlarmEvent alarm) {
        kafkaTemplate.send("alarms", alarm.getDeviceId(), alarm);
    }

    // 发送任务状态更新
    public void sendTaskStatusUpdate(String taskId, TaskStatus status) {
        TaskStatusMessage message = new TaskStatusMessage(taskId, status, System.currentTimeMillis());
        kafkaTemplate.send("task-status", taskId, message);
    }
}

// 消息消费者
@Component
public class MessageConsumer {

    @Autowired
    private DataProcessingService dataProcessingService;

    @Autowired
    private AlarmService alarmService;

    @KafkaListener(topics = "device-data", groupId = "data-processing-group")
    public void handleDeviceData(DeviceDataMessage message) {
        try {
            dataProcessingService.processDeviceData(message);
        } catch (Exception e) {
            log.error("处理设备数据失败: {}", message, e);
        }
    }

    @KafkaListener(topics = "alarms", groupId = "alarm-processing-group")
    public void handleAlarm(AlarmEvent alarm) {
        try {
            alarmService.processAlarm(alarm);
        } catch (Exception e) {
            log.error("处理告警失败: {}", alarm, e);
        }
    }

    @KafkaListener(topics = "ai-analysis-requests", groupId = "ai-processing-group")
    public void handleAIAnalysisRequest(AIAnalysisRequest request) {
        try {
            // 异步处理AI分析请求
            CompletableFuture.supplyAsync(() -> {
                return aiAnalysisService.processRequest(request);
            }).thenAccept(result -> {
                // 发送分析结果
                kafkaTemplate.send("ai-analysis-results", request.getRequestId(), result);
            });
        } catch (Exception e) {
            log.error("处理AI分析请求失败: {}", request, e);
        }
    }
}
```

## 5. 安全设计

### 5.1 认证授权

#### JWT认证实现
```java
// JWT工具类
@Component
public class JwtTokenUtil {

    private String secret = "mySecretKey";
    private int expiration = 86400; // 24小时

    public String generateToken(UserDetails userDetails) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("username", userDetails.getUsername());
        claims.put("authorities", userDetails.getAuthorities());
        return createToken(claims, userDetails.getUsername());
    }

    private String createToken(Map<String, Object> claims, String subject) {
        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(new Date(System.currentTimeMillis()))
                .setExpiration(new Date(System.currentTimeMillis() + expiration * 1000))
                .signWith(SignatureAlgorithm.HS512, secret)
                .compact();
    }

    public Boolean validateToken(String token, UserDetails userDetails) {
        final String username = getUsernameFromToken(token);
        return (username.equals(userDetails.getUsername()) && !isTokenExpired(token));
    }

    public String getUsernameFromToken(String token) {
        return getClaimFromToken(token, Claims::getSubject);
    }

    public Date getExpirationDateFromToken(String token) {
        return getClaimFromToken(token, Claims::getExpiration);
    }

    public <T> T getClaimFromToken(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = getAllClaimsFromToken(token);
        return claimsResolver.apply(claims);
    }

    private Claims getAllClaimsFromToken(String token) {
        return Jwts.parser().setSigningKey(secret).parseClaimsJws(token).getBody();
    }

    private Boolean isTokenExpired(String token) {
        final Date expiration = getExpirationDateFromToken(token);
        return expiration.before(new Date());
    }
}

// 认证过滤器
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    @Autowired
    private UserDetailsService userDetailsService;

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response,
            FilterChain chain) throws ServletException, IOException {

        final String requestTokenHeader = request.getHeader("Authorization");

        String username = null;
        String jwtToken = null;

        if (requestTokenHeader != null && requestTokenHeader.startsWith("Bearer ")) {
            jwtToken = requestTokenHeader.substring(7);
            try {
                username = jwtTokenUtil.getUsernameFromToken(jwtToken);
            } catch (IllegalArgumentException e) {
                logger.error("Unable to get JWT Token");
            } catch (ExpiredJwtException e) {
                logger.error("JWT Token has expired");
            }
        }

        if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
            UserDetails userDetails = this.userDetailsService.loadUserByUsername(username);

            if (jwtTokenUtil.validateToken(jwtToken, userDetails)) {
                UsernamePasswordAuthenticationToken authToken =
                    new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
                authToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(authToken);
            }
        }

        chain.doFilter(request, response);
    }
}
```

### 5.2 权限控制

#### RBAC权限模型
```java
// 权限注解
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@PreAuthorize("hasPermission(#root, #permission)")
public @interface RequirePermission {
    String value();
    String resource() default "";
}

// 权限检查器
@Component
public class PermissionEvaluator implements org.springframework.security.access.PermissionEvaluator {

    @Autowired
    private UserService userService;

    @Override
    public boolean hasPermission(Authentication authentication, Object targetDomainObject, Object permission) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }

        String username = authentication.getName();
        String permissionStr = permission.toString();

        return userService.hasPermission(username, permissionStr);
    }

    @Override
    public boolean hasPermission(Authentication authentication, Serializable targetId,
            String targetType, Object permission) {
        return hasPermission(authentication, null, permission);
    }
}

// 用户服务
@Service
public class UserService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    public boolean hasPermission(String username, String permission) {
        User user = userRepository.findByUsername(username);
        if (user == null) {
            return false;
        }

        // 获取用户所有角色
        List<Role> roles = roleRepository.findByUserId(user.getUserId());

        // 检查权限
        return roles.stream()
                .flatMap(role -> role.getPermissions().stream())
                .anyMatch(perm -> perm.equals(permission));
    }

    public List<String> getUserPermissions(String username) {
        User user = userRepository.findByUsername(username);
        if (user == null) {
            return Collections.emptyList();
        }

        List<Role> roles = roleRepository.findByUserId(user.getUserId());

        return roles.stream()
                .flatMap(role -> role.getPermissions().stream())
                .distinct()
                .collect(Collectors.toList());
    }
}
```

### 5.3 数据加密

#### 敏感数据加密
```java
// 加密工具类
@Component
public class EncryptionUtil {

    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/CBC/PKCS5Padding";

    @Value("${app.encryption.key}")
    private String encryptionKey;

    public String encrypt(String plainText) {
        try {
            SecretKeySpec secretKey = new SecretKeySpec(encryptionKey.getBytes(), ALGORITHM);
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);

            // 生成随机IV
            byte[] iv = new byte[16];
            new SecureRandom().nextBytes(iv);
            IvParameterSpec ivSpec = new IvParameterSpec(iv);

            cipher.init(Cipher.ENCRYPT_MODE, secretKey, ivSpec);
            byte[] encrypted = cipher.doFinal(plainText.getBytes());

            // 将IV和加密数据合并
            byte[] encryptedWithIv = new byte[iv.length + encrypted.length];
            System.arraycopy(iv, 0, encryptedWithIv, 0, iv.length);
            System.arraycopy(encrypted, 0, encryptedWithIv, iv.length, encrypted.length);

            return Base64.getEncoder().encodeToString(encryptedWithIv);
        } catch (Exception e) {
            throw new RuntimeException("加密失败", e);
        }
    }

    public String decrypt(String encryptedText) {
        try {
            byte[] encryptedWithIv = Base64.getDecoder().decode(encryptedText);

            // 提取IV
            byte[] iv = new byte[16];
            System.arraycopy(encryptedWithIv, 0, iv, 0, iv.length);

            // 提取加密数据
            byte[] encrypted = new byte[encryptedWithIv.length - 16];
            System.arraycopy(encryptedWithIv, 16, encrypted, 0, encrypted.length);

            SecretKeySpec secretKey = new SecretKeySpec(encryptionKey.getBytes(), ALGORITHM);
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            IvParameterSpec ivSpec = new IvParameterSpec(iv);

            cipher.init(Cipher.DECRYPT_MODE, secretKey, ivSpec);
            byte[] decrypted = cipher.doFinal(encrypted);

            return new String(decrypted);
        } catch (Exception e) {
            throw new RuntimeException("解密失败", e);
        }
    }
}

// 数据库字段加密
@Converter
public class EncryptedStringConverter implements AttributeConverter<String, String> {

    @Autowired
    private EncryptionUtil encryptionUtil;

    @Override
    public String convertToDatabaseColumn(String attribute) {
        if (attribute == null) {
            return null;
        }
        return encryptionUtil.encrypt(attribute);
    }

    @Override
    public String convertToEntityAttribute(String dbData) {
        if (dbData == null) {
            return null;
        }
        return encryptionUtil.decrypt(dbData);
    }
}
```

这个软件设计方案涵盖了智慧化光伏场站系统的核心软件架构、模块设计、数据库设计、接口设计和安全设计等关键方面，为系统开发提供了详细的技术指导和实现方案。
```
```