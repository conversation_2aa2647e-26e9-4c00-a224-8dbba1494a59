<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Java Web Start 演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .launch-section {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background-color: #e8f4fd;
            border-radius: 8px;
            border-left: 4px solid #2196F3;
        }
        
        .launch-button {
            display: inline-block;
            padding: 15px 30px;
            background-color: #2196F3;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-size: 18px;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        
        .launch-button:hover {
            background-color: #1976D2;
        }
        
        .info-section {
            margin: 20px 0;
        }
        
        .info-section h3 {
            color: #555;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        
        .requirements {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .warning {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        
        ul {
            padding-left: 20px;
        }
        
        li {
            margin: 8px 0;
        }
        
        code {
            background-color: #f4f4f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Java Web Start 演示应用</h1>
        
        <div class="launch-section">
            <h2>启动应用程序</h2>
            <p>点击下面的链接启动Java Web Start演示应用：</p>
            <a href="webstart-demo.jnlp" class="launch-button">🚀 启动应用程序</a>
        </div>
        
        <div class="info-section">
            <h3>应用程序功能</h3>
            <ul>
                <li><strong>系统信息显示</strong> - 查看Java和操作系统信息</li>
                <li><strong>Web Start环境检测</strong> - 检测是否在Web Start环境中运行</li>
                <li><strong>内存使用监控</strong> - 显示应用程序内存使用情况</li>
                <li><strong>网络连接测试</strong> - 测试网络连接功能</li>
                <li><strong>日志记录</strong> - 实时显示应用程序操作日志</li>
            </ul>
        </div>
        
        <div class="requirements">
            <h3>⚠️ 系统要求</h3>
            <ul>
                <li>Java 8 或更高版本</li>
                <li>启用Java Web Start支持</li>
                <li>浏览器支持JNLP文件类型</li>
                <li>网络连接（首次下载）</li>
            </ul>
        </div>
        
        <div class="warning">
            <h3>🚨 重要提示</h3>
            <p><strong>Java Web Start 已在 Java 11 中被移除！</strong></p>
            <p>此演示仅适用于 Java 8 或 Java 10 及以下版本。对于现代Java版本，请考虑以下替代方案：</p>
            <ul>
                <li>JavaFX + jpackage</li>
                <li>Web应用程序 (HTML5/JavaScript)</li>
                <li>Electron 应用程序</li>
                <li>原生应用程序打包</li>
            </ul>
        </div>
        
        <div class="info-section">
            <h3>技术说明</h3>
            <p>这个演示应用展示了Java Web Start的以下特性：</p>
            <ul>
                <li><strong>自动下载和缓存</strong> - 应用程序自动下载到本地缓存</li>
                <li><strong>版本管理</strong> - 支持自动更新检查</li>
                <li><strong>安全模型</strong> - 演示签名应用程序的权限</li>
                <li><strong>离线运行</strong> - 下载后可离线运行</li>
                <li><strong>桌面集成</strong> - 可创建桌面快捷方式</li>
            </ul>
        </div>
        
        <div class="info-section">
            <h3>故障排除</h3>
            <p>如果应用程序无法启动，请检查：</p>
            <ul>
                <li>Java版本是否支持Web Start (Java 8-10)</li>
                <li>浏览器是否正确配置JNLP文件关联</li>
                <li>Java控制面板中的安全设置</li>
                <li>防火墙和代理设置</li>
            </ul>
            
            <p>手动启动命令：</p>
            <code>javaws http://localhost:8080/webstart/webstart-demo.jnlp</code>
        </div>
        
        <div class="info-section">
            <h3>开发者信息</h3>
            <p>这是一个教学演示项目，展示Java Web Start的基本概念和实现方法。</p>
            <p>源代码包含完整的JNLP配置、应用程序代码和部署脚本。</p>
        </div>
    </div>
</body>
</html>
