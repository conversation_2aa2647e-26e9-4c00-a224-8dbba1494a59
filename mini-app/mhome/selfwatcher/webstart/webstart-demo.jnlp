<?xml version="1.0" encoding="utf-8"?>
<jnlp spec="1.0+" codebase="http://localhost:8080/webstart/" href="webstart-demo.jnlp">
    
    <!-- 应用程序信息 -->
    <information>
        <title>Java Web Start 演示应用</title>
        <vendor>开发团队</vendor>
        <description>演示Java Web Start功能的示例应用程序</description>
        <description kind="short">Web Start演示</description>
        <icon href="icon.png" width="32" height="32"/>
        <icon href="icon-large.png" width="64" height="64"/>
        <offline-allowed/>
        <shortcut online="false">
            <desktop/>
            <menu submenu="Java应用"/>
        </shortcut>
    </information>
    
    <!-- 安全设置 -->
    <security>
        <!-- 请求所有权限（需要签名的JAR） -->
        <all-permissions/>
    </security>
    
    <!-- 资源定义 -->
    <resources>
        <!-- Java运行时要求 -->
        <j2se version="1.8+" 
              initial-heap-size="64m" 
              max-heap-size="256m"/>
        
        <!-- 主JAR文件 -->
        <jar href="webstart-demo.jar" main="true"/>
        
        <!-- 依赖库（如果有） -->
        <!-- <jar href="lib/commons-lang.jar"/> -->
        
        <!-- 系统属性 -->
        <property name="app.name" value="WebStart Demo"/>
        <property name="app.version" value="1.0.0"/>
    </resources>
    
    <!-- 应用程序描述 -->
    <application-desc main-class="com.mhome.monitor.WebStartDemo">
        <!-- 命令行参数 -->
        <argument>--webstart</argument>
    </application-desc>
    
    <!-- 更新策略 -->
    <update check="always" policy="always"/>
    
</jnlp>
