#!/bin/bash

echo "=== 编译和测试脚本 ==="

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 创建输出目录
echo "创建输出目录..."
mkdir -p target/classes

# 编译Java代码
echo "编译Java代码..."
javac -d target/classes src/main/java/com/mhome/monitor/*.java

# 检查编译是否成功
if [ $? -eq 0 ]; then
    echo "✓ 编译成功！"
    
    # 列出编译后的类文件
    echo ""
    echo "编译后的类文件:"
    find target/classes -name "*.class" -type f
    
    echo ""
    echo "=== 可用的程序 ==="
    echo "1. 启动互相守护程序:"
    echo "   java -cp target/classes com.mhome.monitor.MutualGuardian"
    echo ""
    echo "2. 运行测试工具:"
    echo "   java -cp target/classes com.mhome.monitor.GuardianTester"
    echo ""
    echo "3. 使用启动脚本:"
    echo "   ./start_guardian.sh"
    echo ""
    echo "所有错误已修复，程序可以正常运行！"
    
else
    echo "✗ 编译失败！"
    echo "请检查错误信息并修复代码。"
    exit 1
fi
