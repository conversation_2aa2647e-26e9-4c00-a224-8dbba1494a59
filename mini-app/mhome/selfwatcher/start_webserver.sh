#!/bin/bash

echo "=== Java Web Start 测试服务器启动脚本 ==="

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 配置变量
PORT=8080
DIST_DIR="build/dist"

# 检查构建目录是否存在
if [ ! -d "$DIST_DIR" ]; then
    echo "❌ 构建目录不存在: $DIST_DIR"
    echo "请先运行构建脚本: ./build_webstart.sh"
    exit 1
fi

# 检查必要文件是否存在
REQUIRED_FILES=("webstart-demo.jar" "webstart-demo.jnlp" "index.html")
for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$DIST_DIR/$file" ]; then
        echo "❌ 缺少必要文件: $file"
        echo "请先运行构建脚本: ./build_webstart.sh"
        exit 1
    fi
done

echo "✅ 所有必要文件已就绪"

# 创建临时的.htaccess文件（如果使用Apache）
cat > "$DIST_DIR/.htaccess" << EOF
# Java Web Start MIME类型配置
AddType application/x-java-jnlp-file .jnlp
AddType application/x-java-archive .jar

# 缓存控制
<Files "*.jnlp">
    ExpiresActive On
    ExpiresDefault "access plus 0 seconds"
    Header set Cache-Control "no-cache, no-store, must-revalidate"
    Header set Pragma "no-cache"
    Header set Expires 0
</Files>

<Files "*.jar">
    ExpiresActive On
    ExpiresDefault "access plus 1 year"
</Files>
EOF

echo "已创建 .htaccess 配置文件"

# 检查可用的Web服务器
echo ""
echo "检测可用的Web服务器..."

# 检查Python
if command -v python3 &> /dev/null; then
    echo "✅ 找到 Python 3"
    WEBSERVER="python3"
elif command -v python &> /dev/null; then
    echo "✅ 找到 Python 2"
    WEBSERVER="python"
else
    echo "❌ 未找到 Python"
    WEBSERVER=""
fi

# 检查Node.js
if command -v node &> /dev/null && command -v npx &> /dev/null; then
    echo "✅ 找到 Node.js"
    if [ -z "$WEBSERVER" ]; then
        WEBSERVER="node"
    fi
fi

# 检查PHP
if command -v php &> /dev/null; then
    echo "✅ 找到 PHP"
    if [ -z "$WEBSERVER" ]; then
        WEBSERVER="php"
    fi
fi

if [ -z "$WEBSERVER" ]; then
    echo "❌ 未找到可用的Web服务器"
    echo "请安装 Python、Node.js 或 PHP"
    exit 1
fi

echo ""
echo "使用 $WEBSERVER 启动Web服务器..."
echo "端口: $PORT"
echo "目录: $DIST_DIR"
echo ""

# 切换到构建目录
cd "$DIST_DIR"

# 显示服务器信息
echo "=== 服务器信息 ==="
echo "访问地址: http://localhost:$PORT/"
echo "JNLP文件: http://localhost:$PORT/webstart-demo.jnlp"
echo ""
echo "=== 使用说明 ==="
echo "1. 在浏览器中访问: http://localhost:$PORT/"
echo "2. 点击页面上的启动链接"
echo "3. 或者直接运行: javaws http://localhost:$PORT/webstart-demo.jnlp"
echo ""
echo "=== 注意事项 ==="
echo "- 需要 Java 8-10 (Java 11+ 不支持 Web Start)"
echo "- 首次运行可能需要确认安全警告"
echo "- 应用程序会被缓存到本地"
echo ""
echo "按 Ctrl+C 停止服务器"
echo ""

# 启动相应的Web服务器
case $WEBSERVER in
    "python3")
        echo "启动 Python 3 HTTP 服务器..."
        python3 -m http.server $PORT
        ;;
    "python")
        echo "启动 Python 2 HTTP 服务器..."
        python -m SimpleHTTPServer $PORT
        ;;
    "node")
        echo "启动 Node.js HTTP 服务器..."
        npx http-server -p $PORT
        ;;
    "php")
        echo "启动 PHP 内置服务器..."
        php -S localhost:$PORT
        ;;
    *)
        echo "❌ 未知的Web服务器类型: $WEBSERVER"
        exit 1
        ;;
esac
