package com.mhome.monitor;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.lang.management.ManagementFactory;
import java.lang.management.ThreadInfo;
import java.lang.management.ThreadMXBean;

/**
 * 对比 ThreadMXBean 和系统级线程监控的区别
 * 证明 ThreadMXBean 只监控当前JVM进程，而不是整个系统
 */
public class ThreadScopeComparison {
    
    public static void main(String[] args) {
        System.out.println("=== ThreadMXBean 作用范围对比演示 ===\n");
        
        // 1. 使用 ThreadMXBean 获取当前JVM进程的线程信息
        showJVMThreadInfo();
        
        // 2. 使用系统命令获取当前进程的线程信息
        showCurrentProcessThreadsFromSystem();
        
        // 3. 使用系统命令获取系统所有线程信息（部分）
        showSystemThreadsInfo();
        
        // 4. 总结对比
        showComparison();
    }
    
    /**
     * 1. 使用 ThreadMXBean 获取JVM线程信息
     */
    private static void showJVMThreadInfo() {
        System.out.println("=== 1. ThreadMXBean 获取的线程信息 (仅当前JVM进程) ===");
        
        ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
        
        System.out.println("当前JVM进程信息:");
        System.out.println("  进程PID: " + ManagementFactory.getRuntimeMXBean().getName().split("@")[0]);
        System.out.println("  线程总数: " + threadBean.getThreadCount());
        System.out.println("  守护线程数: " + threadBean.getDaemonThreadCount());
        System.out.println("  峰值线程数: " + threadBean.getPeakThreadCount());
        
        // 显示前10个线程的名称
        long[] threadIds = threadBean.getAllThreadIds();
        System.out.println("  前10个线程名称:");
        for (int i = 0; i < Math.min(10, threadIds.length); i++) {
            ThreadInfo threadInfo = threadBean.getThreadInfo(threadIds[i]);
            if (threadInfo != null) {
                System.out.println("    [" + threadInfo.getThreadId() + "] " + threadInfo.getThreadName());
            }
        }
        System.out.println();
    }
    
    /**
     * 2. 使用系统命令获取当前进程的线程信息
     */
    private static void showCurrentProcessThreadsFromSystem() {
        System.out.println("=== 2. 系统命令获取当前进程线程信息 ===");
        
        try {
            String currentPid = ManagementFactory.getRuntimeMXBean().getName().split("@")[0];
            String os = System.getProperty("os.name").toLowerCase();
            
            Process process;
            if (os.contains("mac") || os.contains("linux")) {
                // macOS/Linux: 使用 ps 命令获取当前进程的线程
                process = Runtime.getRuntime().exec("ps -M " + currentPid);
            } else if (os.contains("win")) {
                // Windows: 使用 wmic 获取线程信息
                process = Runtime.getRuntime().exec("wmic process where processid=" + currentPid + " get ThreadCount /format:list");
            } else {
                System.out.println("  不支持的操作系统: " + os);
                return;
            }
            
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            int lineCount = 0;
            
            System.out.println("系统命令输出 (当前进程 " + currentPid + " 的线程):");
            while ((line = reader.readLine()) != null && lineCount < 15) {
                if (!line.trim().isEmpty()) {
                    System.out.println("  " + line);
                    lineCount++;
                }
            }
            
            process.waitFor();
            reader.close();
            
        } catch (Exception e) {
            System.out.println("  获取系统线程信息失败: " + e.getMessage());
        }
        System.out.println();
    }
    
    /**
     * 3. 使用系统命令获取系统所有进程的线程统计
     */
    private static void showSystemThreadsInfo() {
        System.out.println("=== 3. 系统级线程信息 (所有进程) ===");
        
        try {
            String os = System.getProperty("os.name").toLowerCase();
            Process process;
            
            if (os.contains("mac")) {
                // macOS: 获取系统线程统计
                process = Runtime.getRuntime().exec("ps -eo pid,comm,thcount | head -20");
            } else if (os.contains("linux")) {
                // Linux: 获取系统线程统计
                process = Runtime.getRuntime().exec("ps -eo pid,comm,nlwp | head -20");
            } else if (os.contains("win")) {
                // Windows: 获取进程和线程数
                process = Runtime.getRuntime().exec("wmic process get processid,name,threadcount | findstr /V \"^$\" | more +1");
            } else {
                System.out.println("  不支持的操作系统: " + os);
                return;
            }
            
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            int lineCount = 0;
            
            System.out.println("系统中所有进程的线程信息 (前20个):");
            System.out.println("  PID    进程名称                线程数");
            System.out.println("  ----------------------------------------");
            
            while ((line = reader.readLine()) != null && lineCount < 20) {
                if (!line.trim().isEmpty()) {
                    System.out.println("  " + line);
                    lineCount++;
                }
            }
            
            process.waitFor();
            reader.close();
            
        } catch (Exception e) {
            System.out.println("  获取系统线程信息失败: " + e.getMessage());
        }
        System.out.println();
    }
    
    /**
     * 4. 总结对比
     */
    private static void showComparison() {
        System.out.println("=== 4. 对比总结 ===");
        System.out.println();
        
        System.out.println("ThreadMXBean 的作用范围:");
        System.out.println("  ✓ 只监控当前JVM进程内的线程");
        System.out.println("  ✓ 包括主线程、用户创建的线程、JVM内部线程");
        System.out.println("  ✓ 提供详细的线程状态、CPU时间、死锁检测等功能");
        System.out.println("  ✓ 是Java标准API，跨平台兼容");
        System.out.println("  ✗ 无法获取其他进程的线程信息");
        System.out.println("  ✗ 无法获取系统级线程统计");
        System.out.println();
        
        System.out.println("系统命令的作用范围:");
        System.out.println("  ✓ 可以获取系统中所有进程的线程信息");
        System.out.println("  ✓ 可以获取指定进程的线程信息");
        System.out.println("  ✓ 提供系统级的线程统计");
        System.out.println("  ✗ 平台依赖，不同操作系统命令不同");
        System.out.println("  ✗ 功能相对简单，主要是统计信息");
        System.out.println("  ✗ 需要执行外部命令，有性能开销");
        System.out.println();
        
        System.out.println("使用建议:");
        System.out.println("  • 监控当前Java应用: 使用 ThreadMXBean");
        System.out.println("  • 系统级监控: 使用系统命令或专门的监控工具");
        System.out.println("  • 跨进程监控: 结合使用两种方式");
        System.out.println("  • 生产环境: 优先使用 ThreadMXBean，性能更好");
    }
}
