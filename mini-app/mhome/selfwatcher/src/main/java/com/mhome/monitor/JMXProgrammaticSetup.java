package com.mhome.monitor;

import java.io.IOException;
import java.lang.management.ManagementFactory;
import java.net.InetAddress;
import java.rmi.registry.LocateRegistry;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.management.MBeanServer;
import javax.management.ObjectName;
import javax.management.remote.JMXConnectorServer;
import javax.management.remote.JMXConnectorServerFactory;
import javax.management.remote.JMXServiceURL;

/**
 * 程序化设置JMX示例
 * 演示如何在不使用命令行参数的情况下启用JMX远程管理
 */
public class JMXProgrammaticSetup {
    
    // JMX配置
    private static final int JMX_PORT = 9010;
    private static final boolean JMX_AUTHENTICATE = false;
    private static final boolean JMX_SSL = false;
    
    // JMX连接器服务器
    private static JMXConnectorServer jmxConnectorServer;
    
    public static void main(String[] args) {
        System.out.println("=== JMX程序化设置示例 ===\n");
        
        try {
            // 1. 启用JMX远程管理
            enableJMXRemoting();
            
            // 2. 注册示例MBean
            registerSampleMBean();
            
            // 3. 显示连接信息
            displayConnectionInfo();
            
            // 4. 保持程序运行
            keepRunning();
            
        } catch (Exception e) {
            System.err.println("JMX设置失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 启用JMX远程管理
     */
    private static void enableJMXRemoting() throws Exception {
        System.out.println("正在启用JMX远程管理...");
        
        // 获取平台MBean服务器
        MBeanServer mbs = ManagementFactory.getPlatformMBeanServer();
        
        // 创建RMI注册表
        try {
            LocateRegistry.createRegistry(JMX_PORT);
            System.out.println("已创建RMI注册表，端口: " + JMX_PORT);
        } catch (Exception e) {
            System.out.println("RMI注册表可能已存在，端口: " + JMX_PORT);
        }
        
        // 构建JMX服务URL
        String hostname = InetAddress.getLocalHost().getHostName();
        JMXServiceURL url = new JMXServiceURL(
            "service:jmx:rmi:///jndi/rmi://" + hostname + ":" + JMX_PORT + "/jmxrmi");
        
        // 设置环境
        Map<String, Object> env = new HashMap<>();
        
        // 如果需要身份验证，可以添加凭据
        if (JMX_AUTHENTICATE) {
            // 设置身份验证凭据
            String[] credentials = {"admin", "password"};
            env.put("jmx.remote.credentials", credentials);
            
            // 可以设置自定义的身份验证器
            // env.put("jmx.remote.authenticator", new MyCustomAuthenticator());
        }
        
        // 如果需要SSL，可以设置SSL工厂
        if (JMX_SSL) {
            // 设置SSL相关属性
            // env.put("com.sun.management.jmxremote.ssl.need.client.auth", "true");
            // env.put("javax.net.ssl.keyStore", "/path/to/keystore");
            // env.put("javax.net.ssl.keyStorePassword", "password");
        }
        
        // 创建JMX连接器服务器
        jmxConnectorServer = JMXConnectorServerFactory.newJMXConnectorServer(url, env, mbs);
        
        // 启动连接器服务器
        jmxConnectorServer.start();
        
        System.out.println("✓ JMX远程管理已启用");
        System.out.println("  服务URL: " + url);
        
        // 添加关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                if (jmxConnectorServer != null) {
                    jmxConnectorServer.stop();
                    System.out.println("JMX连接器已停止");
                }
            } catch (IOException e) {
                System.err.println("停止JMX连接器时出错: " + e.getMessage());
            }
        }));
    }
    
    /**
     * 注册示例MBean
     */
    private static void registerSampleMBean() throws Exception {
        System.out.println("\n注册示例MBean...");
        
        // 创建示例MBean
        SampleMBean sampleMBean = new SampleMBean();
        
        // 注册MBean
        MBeanServer mbs = ManagementFactory.getPlatformMBeanServer();
        ObjectName name = new ObjectName("com.mhome.monitor:type=SampleMBean");
        mbs.registerMBean(sampleMBean, name);
        
        System.out.println("✓ 示例MBean已注册: " + name);
    }
    
    /**
     * 显示连接信息
     */
    private static void displayConnectionInfo() throws Exception {
        System.out.println("\n=== JMX连接信息 ===");
        System.out.println("主机: " + InetAddress.getLocalHost().getHostName());
        System.out.println("端口: " + JMX_PORT);
        System.out.println("身份验证: " + (JMX_AUTHENTICATE ? "启用" : "禁用"));
        System.out.println("SSL: " + (JMX_SSL ? "启用" : "禁用"));
        System.out.println("\n连接方式:");
        System.out.println("1. 使用JConsole:");
        System.out.println("   - 启动JConsole");
        System.out.println("   - 选择'远程进程'");
        System.out.println("   - 输入: service:jmx:rmi:///jndi/rmi://" + 
                          InetAddress.getLocalHost().getHostName() + ":" + JMX_PORT + "/jmxrmi");
        System.out.println("\n2. 使用VisualVM:");
        System.out.println("   - 启动VisualVM");
        System.out.println("   - 右键点击'本地'，选择'添加JMX连接'");
        System.out.println("   - 输入: " + InetAddress.getLocalHost().getHostName() + ":" + JMX_PORT);
        System.out.println("\n3. 使用Java代码:");
        System.out.println("   JMXServiceURL url = new JMXServiceURL(\"service:jmx:rmi:///jndi/rmi://" + 
                          InetAddress.getLocalHost().getHostName() + ":" + JMX_PORT + "/jmxrmi\");");
        System.out.println("   JMXConnector connector = JMXConnectorFactory.connect(url);");
    }
    
    /**
     * 保持程序运行
     */
    private static void keepRunning() throws Exception {
        System.out.println("\n程序正在运行，可以通过JMX连接...");
        System.out.println("按Ctrl+C停止");
        
        // 模拟一些活动
        int counter = 0;
        while (true) {
            counter++;
            System.out.println("程序运行中... (" + counter + ")");
            
            // 更新示例MBean的属性
            SampleMBean.setCounter(counter);
            SampleMBean.setLastUpdateTime(System.currentTimeMillis());
            
            try {
                TimeUnit.SECONDS.sleep(10);
            } catch (InterruptedException e) {
                break;
            }
        }
    }
    
    /**
     * 示例MBean类
     */
    public static class SampleMBean implements SampleMBeanMXBean {
        private static int counter = 0;
        private static long lastUpdateTime = System.currentTimeMillis();
        private static String status = "Running";
        
        @Override
        public int getCounter() {
            return counter;
        }
        
        @Override
        public long getLastUpdateTime() {
            return lastUpdateTime;
        }
        
        @Override
        public String getStatus() {
            return status;
        }
        
        @Override
        public void setStatus(String newStatus) {
            status = newStatus;
            System.out.println("MBean状态已更新: " + status);
        }
        
        @Override
        public String performOperation(String param) {
            System.out.println("MBean操作已执行，参数: " + param);
            return "操作完成，时间: " + System.currentTimeMillis();
        }
        
        public static void setCounter(int value) {
            counter = value;
        }
        
        public static void setLastUpdateTime(long time) {
            lastUpdateTime = time;
        }
    }
    
    /**
     * MBean接口
     */
    public interface SampleMBeanMXBean {
        int getCounter();
        long getLastUpdateTime();
        String getStatus();
        void setStatus(String status);
        String performOperation(String param);
    }
}
