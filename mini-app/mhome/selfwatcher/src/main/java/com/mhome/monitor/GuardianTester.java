package com.mhome.monitor;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Scanner;

/**
 * 互相守护程序测试工具
 * 用于测试守护程序的重启功能
 */
public class GuardianTester {
    
    private static final String PID_DIR = "pids";
    private static final String MAIN_PID_FILE = PID_DIR + "/main.pid";
    private static final String DAEMON_PID_FILE = PID_DIR + "/daemon.pid";
    
    public static void main(String[] args) {
        try (Scanner scanner = new Scanner(System.in)) {
            System.out.println("=== 互相守护程序测试工具 ===");
            System.out.println("此工具用于测试守护程序的重启功能");

            while (true) {
                showMenu();
                System.out.print("请选择操作 (1-6): ");

                try {
                    int choice = Integer.parseInt(scanner.nextLine().trim());

                    switch (choice) {
                        case 1:
                            showProcessStatus();
                            break;
                        case 2:
                            killMainProcess();
                            break;
                        case 3:
                            killDaemonProcess();
                            break;
                        case 4:
                            killAllProcesses();
                            break;
                        case 5:
                            cleanupPidFiles();
                            break;
                        case 6:
                            System.out.println("退出测试工具...");
                            return;
                        default:
                            System.out.println("无效选择，请重新输入！");
                    }
                } catch (NumberFormatException e) {
                    System.out.println("请输入有效的数字！");
                }

                System.out.println("\n按回车键继续...");
                scanner.nextLine();
            }
        }
    }
    
    private static void showMenu() {
        System.out.println("\n=== 测试菜单 ===");
        System.out.println("1. 查看进程状态");
        System.out.println("2. 终止主进程 (测试守护进程重启功能)");
        System.out.println("3. 终止守护进程 (测试主进程重启功能)");
        System.out.println("4. 终止所有进程");
        System.out.println("5. 清理PID文件");
        System.out.println("6. 退出");
    }
    
    private static void showProcessStatus() {
        System.out.println("\n=== 进程状态 ===");
        
        // 检查主进程
        String mainPid = readPidFromFile(MAIN_PID_FILE);
        if (mainPid != null) {
            boolean mainAlive = isProcessAlive(mainPid);
            System.out.println("主进程 PID: " + mainPid + " - 状态: " + (mainAlive ? "运行中" : "已停止"));
        } else {
            System.out.println("主进程: 未找到PID文件");
        }
        
        // 检查守护进程
        String daemonPid = readPidFromFile(DAEMON_PID_FILE);
        if (daemonPid != null) {
            boolean daemonAlive = isProcessAlive(daemonPid);
            System.out.println("守护进程 PID: " + daemonPid + " - 状态: " + (daemonAlive ? "运行中" : "已停止"));
        } else {
            System.out.println("守护进程: 未找到PID文件");
        }
        
        // 显示所有Java进程
        System.out.println("\n所有Java进程:");
        showAllJavaProcesses();
    }
    
    private static void killMainProcess() {
        System.out.println("\n=== 终止主进程 ===");
        String mainPid = readPidFromFile(MAIN_PID_FILE);
        
        if (mainPid != null) {
            if (isProcessAlive(mainPid)) {
                System.out.println("正在终止主进程 (PID: " + mainPid + ")...");
                killProcess(mainPid);
                System.out.println("主进程终止命令已发送");
                System.out.println("请观察守护进程是否会重启主进程");
            } else {
                System.out.println("主进程已经停止");
            }
        } else {
            System.out.println("未找到主进程PID文件");
        }
    }
    
    private static void killDaemonProcess() {
        System.out.println("\n=== 终止守护进程 ===");
        String daemonPid = readPidFromFile(DAEMON_PID_FILE);
        
        if (daemonPid != null) {
            if (isProcessAlive(daemonPid)) {
                System.out.println("正在终止守护进程 (PID: " + daemonPid + ")...");
                killProcess(daemonPid);
                System.out.println("守护进程终止命令已发送");
                System.out.println("请观察主进程是否会重启守护进程");
            } else {
                System.out.println("守护进程已经停止");
            }
        } else {
            System.out.println("未找到守护进程PID文件");
        }
    }
    
    private static void killAllProcesses() {
        System.out.println("\n=== 终止所有进程 ===");
        
        String mainPid = readPidFromFile(MAIN_PID_FILE);
        String daemonPid = readPidFromFile(DAEMON_PID_FILE);
        
        if (mainPid != null && isProcessAlive(mainPid)) {
            System.out.println("终止主进程 (PID: " + mainPid + ")");
            killProcess(mainPid);
        }
        
        if (daemonPid != null && isProcessAlive(daemonPid)) {
            System.out.println("终止守护进程 (PID: " + daemonPid + ")");
            killProcess(daemonPid);
        }
        
        System.out.println("所有进程终止命令已发送");
    }
    
    private static void cleanupPidFiles() {
        System.out.println("\n=== 清理PID文件 ===");
        
        try {
            if (Files.exists(Paths.get(MAIN_PID_FILE))) {
                Files.delete(Paths.get(MAIN_PID_FILE));
                System.out.println("已删除主进程PID文件");
            }
            
            if (Files.exists(Paths.get(DAEMON_PID_FILE))) {
                Files.delete(Paths.get(DAEMON_PID_FILE));
                System.out.println("已删除守护进程PID文件");
            }
            
            System.out.println("PID文件清理完成");
            
        } catch (IOException e) {
            System.err.println("清理PID文件失败: " + e.getMessage());
        }
    }
    
    private static String readPidFromFile(String pidFile) {
        try {
            if (Files.exists(Paths.get(pidFile))) {
                return new String(Files.readAllBytes(Paths.get(pidFile))).trim();
            }
        } catch (IOException e) {
            System.err.println("读取PID文件失败: " + e.getMessage());
        }
        return null;
    }
    
    private static boolean isProcessAlive(String pid) {
        try {
            String os = System.getProperty("os.name").toLowerCase();
            Process process;
            
            if (os.contains("win")) {
                process = Runtime.getRuntime().exec("tasklist /FI \"PID eq " + pid + "\"");
            } else {
                process = Runtime.getRuntime().exec("kill -0 " + pid);
            }
            
            int exitCode = process.waitFor();
            return exitCode == 0;
            
        } catch (Exception e) {
            return false;
        }
    }
    
    private static void killProcess(String pid) {
        try {
            String os = System.getProperty("os.name").toLowerCase();
            Process process;
            
            if (os.contains("win")) {
                process = Runtime.getRuntime().exec("taskkill /F /PID " + pid);
            } else {
                process = Runtime.getRuntime().exec("kill -9 " + pid);
            }
            
            process.waitFor();
            
        } catch (Exception e) {
            System.err.println("终止进程失败: " + e.getMessage());
        }
    }
    
    private static void showAllJavaProcesses() {
        try {
            String os = System.getProperty("os.name").toLowerCase();
            Process process;
            
            if (os.contains("win")) {
                process = Runtime.getRuntime().exec("wmic process where \"name='java.exe'\" get processid,commandline /format:list");
            } else {
                process = Runtime.getRuntime().exec("pgrep -f java");
            }
            
            java.io.BufferedReader reader = new java.io.BufferedReader(
                new java.io.InputStreamReader(process.getInputStream())
            );
            
            String line;
            while ((line = reader.readLine()) != null) {
                if (!line.trim().isEmpty()) {
                    System.out.println("  " + line);
                }
            }
            
            process.waitFor();
            reader.close();
            
        } catch (Exception e) {
            System.err.println("获取Java进程列表失败: " + e.getMessage());
        }
    }
}
