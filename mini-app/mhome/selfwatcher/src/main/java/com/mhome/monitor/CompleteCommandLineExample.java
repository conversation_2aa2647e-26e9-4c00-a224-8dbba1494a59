package com.mhome.monitor;

import java.util.*;

/**
 * 完整的命令行参数解析示例
 * 展示如何处理各种复杂的命令行参数情况，包括位置参数
 */
public class CompleteCommandLineExample {
    
    public static void main(String[] args) {
        System.out.println("=== 完整命令行参数解析示例 ===\n");
        
        // 如果没有参数，运行测试用例
        if (args.length == 0) {
            runTestCases();
            return;
        }
        
        // 解析实际参数
        parseAndDisplay(args);
    }
    
    /**
     * 运行各种测试用例
     */
    private static void runTestCases() {
        String[] testCases = {
            // 1. 基本长选项 + 位置参数
            "--host localhost --port 8080 --verbose input.txt output.txt",
            
            // 2. 短选项 + 位置参数
            "-h localhost -p 8080 -v input.txt output.txt",
            
            // 3. 组合短选项
            "-vd -h localhost -p 8080 file1.txt file2.txt",
            
            // 4. 等号格式
            "--host=************* --port=9000 --config=/path/to/config input.txt",
            
            // 5. 混合格式
            "-v --host=localhost -p 8080 --debug file1.txt file2.txt",
            
            // 6. 使用 -- 分隔符
            "--host localhost -v -- --not-an-option -file.txt file1.txt",
            
            // 7. 只有位置参数
            "input.txt output.txt backup.txt",
            
            // 8. 复杂组合
            "-vd --host=************* --port 9000 --threads=4 -- file1.txt --file2.txt -file3.txt",
            
            // 9. 空参数测试
            "",
            
            // 10. 只有选项，没有位置参数
            "--host localhost --port 8080 --verbose --debug"
        };
        
        for (int i = 0; i < testCases.length; i++) {
            System.out.println("=== 测试用例 " + (i + 1) + " ===");
            System.out.println("输入: \"" + testCases[i] + "\"");
            
            if (testCases[i].isEmpty()) {
                System.out.println("空参数测试");
                parseAndDisplay(new String[0]);
            } else {
                String[] testArgs = testCases[i].split("\\s+");
                parseAndDisplay(testArgs);
            }
            System.out.println();
        }
    }
    
    /**
     * 解析并显示结果
     */
    private static void parseAndDisplay(String[] args) {
        try {
            // 使用完整的解析器
            CompleteArgs result = CompleteArgsParser.create()
                .option("--host", "-h").withValue().defaultValue("localhost").description("服务器主机")
                .option("--port", "-p").withValue().asInt().defaultValue(8080).description("服务器端口")
                .option("--verbose", "-v").flag().description("启用详细输出")
                .option("--debug", "-d").flag().description("启用调试模式")
                .option("--config", "-c").withValue().description("配置文件路径")
                .option("--threads", "-t").withValue().asInt().defaultValue(1).description("线程数")
                .option("--timeout").withValue().asInt().defaultValue(30).description("超时时间(秒)")
                .option("--help").flag().description("显示帮助信息")
                .parse(args);
            
            // 如果请求帮助，显示帮助信息
            if (result.getBoolean("help")) {
                showHelp();
                return;
            }
            
            // 显示解析结果
            System.out.println("解析结果:");
            System.out.println("  host: " + result.getString("host"));
            System.out.println("  port: " + result.getInt("port"));
            System.out.println("  verbose: " + result.getBoolean("verbose"));
            System.out.println("  debug: " + result.getBoolean("debug"));
            System.out.println("  config: " + result.getString("config"));
            System.out.println("  threads: " + result.getInt("threads"));
            System.out.println("  timeout: " + result.getInt("timeout"));
            System.out.println("  位置参数: " + result.getPositionalArgs());
            System.out.println("  参数数量: " + result.getPositionalArgCount());
            
            // 处理特定位置的参数
            if (result.getPositionalArgCount() > 0) {
                System.out.println("  第一个文件: " + result.getPositionalArg(0));
                if (result.getPositionalArgCount() > 1) {
                    System.out.println("  第二个文件: " + result.getPositionalArg(1));
                }
                if (result.getPositionalArgCount() > 2) {
                    System.out.println("  其他文件: " + result.getPositionalArgs().subList(2, result.getPositionalArgCount()));
                }
            }
            
        } catch (Exception e) {
            System.err.println("解析错误: " + e.getMessage());
        }
    }
    
    /**
     * 显示帮助信息
     */
    private static void showHelp() {
        System.out.println("用法: myapp [选项] [文件...]");
        System.out.println();
        System.out.println("选项:");
        System.out.println("  -h, --host HOST        服务器主机 (默认: localhost)");
        System.out.println("  -p, --port PORT        服务器端口 (默认: 8080)");
        System.out.println("  -v, --verbose          启用详细输出");
        System.out.println("  -d, --debug            启用调试模式");
        System.out.println("  -c, --config FILE      配置文件路径");
        System.out.println("  -t, --threads NUM      线程数 (默认: 1)");
        System.out.println("      --timeout SEC      超时时间秒数 (默认: 30)");
        System.out.println("      --help             显示此帮助信息");
        System.out.println();
        System.out.println("示例:");
        System.out.println("  myapp --host localhost --port 8080 input.txt output.txt");
        System.out.println("  myapp -vd -h ************* -p 9000 file1.txt file2.txt");
        System.out.println("  myapp --host=localhost --config=/path/to/config input.txt");
        System.out.println("  myapp -v -- --not-an-option file.txt");
    }
    
    /**
     * 完整的命令行参数解析器
     */
    static class CompleteArgsParser {
        private final Map<String, OptionDefinition> options = new HashMap<>();
        private final Map<String, String> aliases = new HashMap<>();
        
        public static CompleteArgsParser create() {
            return new CompleteArgsParser();
        }
        
        public OptionBuilder option(String longName, String shortName) {
            OptionDefinition def = new OptionDefinition(longName, shortName);
            options.put(longName, def);
            if (shortName != null) {
                aliases.put(shortName, longName);
            }
            return new OptionBuilder(this, def);
        }
        
        public OptionBuilder option(String longName) {
            return option(longName, null);
        }
        
        public CompleteArgs parse(String[] args) throws ParseException {
            Map<String, Object> values = new HashMap<>();
            List<String> positionalArgs = new ArrayList<>();
            boolean endOfOptions = false;
            
            // 设置默认值
            for (OptionDefinition def : options.values()) {
                String key = def.longName.replaceFirst("--", "");
                if (def.defaultValue != null) {
                    values.put(key, def.defaultValue);
                } else if (def.isFlag) {
                    values.put(key, false);
                }
            }
            
            for (int i = 0; i < args.length; i++) {
                String arg = args[i];
                
                // 处理 -- 分隔符
                if ("--".equals(arg)) {
                    endOfOptions = true;
                    continue;
                }
                
                if (!endOfOptions && arg.startsWith("--")) {
                    // 长选项
                    i = parseLongOption(args, i, values);
                } else if (!endOfOptions && arg.startsWith("-") && arg.length() > 1) {
                    // 短选项
                    i = parseShortOptions(args, i, values);
                } else {
                    // 位置参数
                    positionalArgs.add(arg);
                }
            }
            
            return new CompleteArgs(values, positionalArgs);
        }
        
        private int parseLongOption(String[] args, int index, Map<String, Object> values) throws ParseException {
            String arg = args[index];
            String optionName;
            String optionValue = null;
            
            // 处理 --option=value 格式
            if (arg.contains("=")) {
                String[] parts = arg.split("=", 2);
                optionName = parts[0];
                optionValue = parts[1];
            } else {
                optionName = arg;
            }
            
            OptionDefinition def = options.get(optionName);
            if (def == null) {
                throw new ParseException("未知选项: " + optionName);
            }
            
            String key = def.longName.replaceFirst("--", "");
            
            if (def.isFlag) {
                values.put(key, true);
            } else if (def.hasValue) {
                if (optionValue != null) {
                    values.put(key, convertValue(optionValue, def.type));
                } else if (index + 1 < args.length && !args[index + 1].startsWith("-")) {
                    values.put(key, convertValue(args[index + 1], def.type));
                    return index + 1;
                } else {
                    throw new ParseException("选项 " + optionName + " 需要一个值");
                }
            }
            
            return index;
        }
        
        private int parseShortOptions(String[] args, int index, Map<String, Object> values) throws ParseException {
            String arg = args[index];
            String shortOpts = arg.substring(1);
            
            for (int j = 0; j < shortOpts.length(); j++) {
                String shortOpt = "-" + shortOpts.charAt(j);
                String longName = aliases.get(shortOpt);
                
                if (longName == null) {
                    throw new ParseException("未知短选项: " + shortOpt);
                }
                
                OptionDefinition def = options.get(longName);
                String key = def.longName.replaceFirst("--", "");
                
                if (def.isFlag) {
                    values.put(key, true);
                } else if (def.hasValue) {
                    if (j == shortOpts.length() - 1) {
                        // 最后一个短选项，可以有值
                        if (index + 1 < args.length && !args[index + 1].startsWith("-")) {
                            values.put(key, convertValue(args[index + 1], def.type));
                            return index + 1;
                        } else {
                            throw new ParseException("选项 " + shortOpt + " 需要一个值");
                        }
                    } else {
                        throw new ParseException("带值的短选项 " + shortOpt + " 必须是最后一个");
                    }
                }
            }
            
            return index;
        }
        
        private Object convertValue(String value, Class<?> type) throws ParseException {
            try {
                if (type == Integer.class) {
                    return Integer.parseInt(value);
                } else if (type == Double.class) {
                    return Double.parseDouble(value);
                } else if (type == Boolean.class) {
                    return Boolean.parseBoolean(value);
                }
                return value;
            } catch (NumberFormatException e) {
                throw new ParseException("无法转换值 '" + value + "' 为 " + type.getSimpleName());
            }
        }
    }
    
    // 其他类定义（OptionBuilder, OptionDefinition, CompleteArgs, ParseException）与之前相同
    static class OptionBuilder {
        private final CompleteArgsParser parser;
        private final OptionDefinition definition;
        
        OptionBuilder(CompleteArgsParser parser, OptionDefinition definition) {
            this.parser = parser;
            this.definition = definition;
        }
        
        public OptionBuilder withValue() {
            definition.hasValue = true;
            return this;
        }
        
        public OptionBuilder flag() {
            definition.isFlag = true;
            return this;
        }
        
        public OptionBuilder asInt() {
            definition.type = Integer.class;
            return this;
        }
        
        public OptionBuilder defaultValue(Object value) {
            definition.defaultValue = value;
            return this;
        }
        
        public OptionBuilder description(String desc) {
            definition.description = desc;
            return this;
        }
        
        public OptionBuilder option(String longName, String shortName) {
            return parser.option(longName, shortName);
        }
        
        public OptionBuilder option(String longName) {
            return parser.option(longName);
        }
        
        public CompleteArgs parse(String[] args) throws ParseException {
            return parser.parse(args);
        }
    }
    
    static class OptionDefinition {
        String longName;
        String shortName;
        boolean hasValue = false;
        boolean isFlag = false;
        Class<?> type = String.class;
        Object defaultValue;
        String description;
        
        OptionDefinition(String longName, String shortName) {
            this.longName = longName;
            this.shortName = shortName;
        }
    }
    
    static class CompleteArgs {
        private final Map<String, Object> values;
        private final List<String> positionalArgs;
        
        CompleteArgs(Map<String, Object> values, List<String> positionalArgs) {
            this.values = values;
            this.positionalArgs = positionalArgs;
        }
        
        public String getString(String key) {
            Object value = values.get(key);
            return value != null ? value.toString() : null;
        }
        
        public int getInt(String key) {
            Object value = values.get(key);
            return value instanceof Integer ? (Integer) value : 0;
        }
        
        public boolean getBoolean(String key) {
            Object value = values.get(key);
            return value instanceof Boolean ? (Boolean) value : false;
        }
        
        public List<String> getPositionalArgs() {
            return new ArrayList<>(positionalArgs);
        }
        
        public String getPositionalArg(int index) {
            return index < positionalArgs.size() ? positionalArgs.get(index) : null;
        }
        
        public int getPositionalArgCount() {
            return positionalArgs.size();
        }
    }
    
    static class ParseException extends Exception {
        ParseException(String message) {
            super(message);
        }
    }
}
