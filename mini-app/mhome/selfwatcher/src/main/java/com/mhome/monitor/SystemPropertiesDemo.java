package com.mhome.monitor;

import java.lang.management.ManagementFactory;
import java.util.Properties;
import javax.management.MBeanServer;
import javax.management.remote.JMXConnectorServer;
import javax.management.remote.JMXConnectorServerFactory;
import javax.management.remote.JMXServiceURL;

/**
 * 系统属性设置演示
 * 展示如何在程序内部设置系统属性，包括JMX相关属性
 */
public class SystemPropertiesDemo {
    
    public static void main(String[] args) {
        System.out.println("=== 系统属性设置演示 ===\n");
        
        // 1. 演示基本的系统属性设置
        demonstrateBasicProperties();
        
        // 2. 演示JMX属性设置的限制
        demonstrateJMXProperties();
        
        // 3. 演示程序化启用JMX
        demonstrateProgrammaticJMX();
        
        // 4. 演示其他常用系统属性
        demonstrateOtherProperties();
        
        // 5. 演示属性的读取和验证
        demonstratePropertyReading();
    }
    
    /**
     * 1. 演示基本的系统属性设置
     */
    private static void demonstrateBasicProperties() {
        System.out.println("=== 1. 基本系统属性设置 ===");
        
        // 在程序运行时设置系统属性
        System.setProperty("app.name", "SystemPropertiesDemo");
        System.setProperty("app.version", "1.0.0");
        System.setProperty("app.environment", "development");
        
        // 读取并验证
        System.out.println("设置的属性:");
        System.out.println("  app.name = " + System.getProperty("app.name"));
        System.out.println("  app.version = " + System.getProperty("app.version"));
        System.out.println("  app.environment = " + System.getProperty("app.environment"));
        
        // 演示属性的覆盖
        System.out.println("\n演示属性覆盖:");
        String originalValue = System.getProperty("app.environment");
        System.out.println("  原始值: " + originalValue);
        
        System.setProperty("app.environment", "production");
        String newValue = System.getProperty("app.environment");
        System.out.println("  新值: " + newValue);
        
        System.out.println();
    }
    
    /**
     * 2. 演示JMX属性设置的限制
     */
    private static void demonstrateJMXProperties() {
        System.out.println("=== 2. JMX属性设置限制演示 ===");
        
        // 检查JMX是否已经启用
        String jmxPort = System.getProperty("com.sun.management.jmxremote.port");
        System.out.println("当前JMX端口设置: " + jmxPort);
        
        if (jmxPort == null) {
            System.out.println("JMX远程管理未在启动时启用");
            System.out.println("尝试在运行时设置JMX属性...");
            
            // 尝试在运行时设置JMX属性
            System.setProperty("com.sun.management.jmxremote.port", "9010");
            System.setProperty("com.sun.management.jmxremote.authenticate", "false");
            System.setProperty("com.sun.management.jmxremote.ssl", "false");
            
            // 验证属性是否设置成功
            System.out.println("设置后的JMX属性:");
            System.out.println("  port = " + System.getProperty("com.sun.management.jmxremote.port"));
            System.out.println("  authenticate = " + System.getProperty("com.sun.management.jmxremote.authenticate"));
            System.out.println("  ssl = " + System.getProperty("com.sun.management.jmxremote.ssl"));
            
            System.out.println("\n⚠️ 注意: 运行时设置的JMX属性通常不会生效！");
            System.out.println("   JMX代理在JVM启动时初始化，之后无法通过系统属性重新配置。");
        } else {
            System.out.println("JMX远程管理已在启动时启用，端口: " + jmxPort);
        }
        
        System.out.println();
    }
    
    /**
     * 3. 演示程序化启用JMX
     */
    private static void demonstrateProgrammaticJMX() {
        System.out.println("=== 3. 程序化启用JMX ===");
        
        try {
            // 获取平台MBean服务器
            MBeanServer server = ManagementFactory.getPlatformMBeanServer();
            System.out.println("平台MBean服务器: " + server);
            
            // 检查是否已有JMX连接器
            String jmxPort = System.getProperty("com.sun.management.jmxremote.port");
            if (jmxPort == null) {
                System.out.println("尝试程序化创建JMX连接器...");
                
                // 创建JMX服务URL
                int port = 9011; // 使用不同的端口避免冲突
                JMXServiceURL url = new JMXServiceURL(
                    "service:jmx:rmi:///jndi/rmi://localhost:" + port + "/jmxrmi");
                
                // 创建JMX连接器服务器
                JMXConnectorServer connectorServer = JMXConnectorServerFactory.newJMXConnectorServer(
                    url, null, server);
                
                // 启动连接器服务器
                connectorServer.start();
                
                System.out.println("✓ JMX连接器已启动");
                System.out.println("  连接URL: " + url);
                System.out.println("  可以使用JConsole连接: localhost:" + port);
                
                // 注册关闭钩子来停止连接器
                Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                    try {
                        connectorServer.stop();
                        System.out.println("JMX连接器已停止");
                    } catch (Exception e) {
                        System.err.println("停止JMX连接器时出错: " + e.getMessage());
                    }
                }));
                
            } else {
                System.out.println("JMX远程管理已通过系统属性启用");
            }
            
        } catch (Exception e) {
            System.err.println("程序化启用JMX失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println();
    }
    
    /**
     * 4. 演示其他常用系统属性
     */
    private static void demonstrateOtherProperties() {
        System.out.println("=== 4. 其他系统属性设置 ===");
        
        // 日志相关属性
        System.setProperty("java.util.logging.config.file", "logging.properties");
        System.setProperty("log4j.configuration", "log4j.properties");
        
        // 网络相关属性
        System.setProperty("http.proxyHost", "proxy.example.com");
        System.setProperty("http.proxyPort", "8080");
        System.setProperty("java.net.useSystemProxies", "true");
        
        // 安全相关属性
        System.setProperty("javax.net.ssl.trustStore", "truststore.jks");
        System.setProperty("javax.net.ssl.trustStorePassword", "changeit");
        
        // 文件编码
        System.setProperty("file.encoding", "UTF-8");
        
        // 临时目录
        System.setProperty("java.io.tmpdir", "/tmp/myapp");
        
        // 显示设置的属性
        String[] propertiesToShow = {
            "java.util.logging.config.file",
            "log4j.configuration",
            "http.proxyHost",
            "http.proxyPort",
            "java.net.useSystemProxies",
            "javax.net.ssl.trustStore",
            "file.encoding",
            "java.io.tmpdir"
        };
        
        System.out.println("设置的其他系统属性:");
        for (String prop : propertiesToShow) {
            System.out.println("  " + prop + " = " + System.getProperty(prop));
        }
        
        System.out.println();
    }
    
    /**
     * 5. 演示属性的读取和验证
     */
    private static void demonstratePropertyReading() {
        System.out.println("=== 5. 属性读取和验证 ===");
        
        // 读取所有系统属性
        Properties props = System.getProperties();
        System.out.println("系统属性总数: " + props.size());
        
        // 显示一些重要的系统属性
        String[] importantProps = {
            "java.version",
            "java.vendor",
            "java.home",
            "os.name",
            "os.version",
            "user.name",
            "user.home",
            "user.dir"
        };
        
        System.out.println("\n重要的系统属性:");
        for (String prop : importantProps) {
            System.out.println("  " + prop + " = " + System.getProperty(prop));
        }
        
        // 演示属性的默认值
        System.out.println("\n使用默认值的属性读取:");
        String customProp = System.getProperty("custom.property", "default-value");
        System.out.println("  custom.property = " + customProp + " (使用默认值)");
        
        // 演示属性的条件设置
        System.out.println("\n条件设置属性:");
        String existingProp = "java.version";
        String newProp = "custom.java.info";
        
        if (System.getProperty(newProp) == null) {
            System.setProperty(newProp, "Java " + System.getProperty(existingProp));
            System.out.println("  设置 " + newProp + " = " + System.getProperty(newProp));
        }
        
        // 演示环境变量与系统属性的关系
        System.out.println("\n环境变量示例:");
        String javaHome = System.getenv("JAVA_HOME");
        System.out.println("  JAVA_HOME 环境变量 = " + javaHome);
        System.out.println("  java.home 系统属性 = " + System.getProperty("java.home"));
        
        System.out.println();
    }
    
    /**
     * 演示早期属性设置的重要性
     */
    static {
        // 静态初始化块中设置属性（在main方法之前执行）
        System.setProperty("app.init.time", String.valueOf(System.currentTimeMillis()));
        System.setProperty("app.early.config", "true");
        
        System.out.println("静态初始化块: 早期属性已设置");
    }
}
