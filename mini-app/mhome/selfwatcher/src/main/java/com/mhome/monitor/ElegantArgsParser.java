package com.mhome.monitor;

import java.util.*;

/**
 * 最优雅的通用命令行参数解析器
 * 简洁的API，强大的功能，无需特定配置类
 */
public class ElegantArgsParser {
    
    public static void main(String[] args) {
        System.out.println("=== 最优雅的通用命令行解析器 ===\n");
        
        // 演示用法
        demonstrateElegantUsage(args.length > 0 ? args : 
            new String[]{"--host", "localhost", "-p", "8080", "-vd", "file1.txt", "file2.txt"});
    }
    
    /**
     * 演示优雅的用法
     */
    private static void demonstrateElegantUsage(String[] args) {
        System.out.println("输入参数: " + Arrays.toString(args));
        
        try {
            // 最优雅的链式调用
            Args result = Args.parse(args)
                .option("--host", "-h").defaultValue("localhost")
                .option("--port", "-p").asInt().defaultValue(8080)
                .option("--verbose", "-v").flag()
                .option("--debug", "-d").flag()
                .option("--config", "-c")
                .option("--threads", "-t").asInt().defaultValue(1)
                .build();
            
            // 简洁的结果访问
            System.out.println("\n解析结果:");
            System.out.println("  host: " + result.get("host"));
            System.out.println("  port: " + result.getInt("port"));
            System.out.println("  verbose: " + result.flag("verbose"));
            System.out.println("  debug: " + result.flag("debug"));
            System.out.println("  config: " + result.get("config"));
            System.out.println("  threads: " + result.getInt("threads"));
            System.out.println("  files: " + result.files());
            
            // 演示便捷方法
            System.out.println("\n便捷访问:");
            System.out.println("  是否启用详细模式: " + result.flag("verbose"));
            System.out.println("  第一个文件: " + result.file(0));
            System.out.println("  文件数量: " + result.fileCount());
            System.out.println("  所有选项: " + result.options());
            
        } catch (Exception e) {
            System.err.println("解析错误: " + e.getMessage());
        }
    }
    
    /**
     * 最优雅的参数解析器
     */
    public static class Args {
        private final Map<String, Object> values = new HashMap<>();
        private final List<String> positionalArgs = new ArrayList<>();
        private final Map<String, OptionConfig> configs = new HashMap<>();
        private final Map<String, String> aliases = new HashMap<>();
        
        private Args() {}
        
        public static ArgsBuilder parse(String[] args) {
            return new ArgsBuilder(args);
        }
        
        // 简洁的访问方法
        public String get(String key) {
            Object value = values.get(key);
            return value != null ? value.toString() : null;
        }
        
        public int getInt(String key) {
            Object value = values.get(key);
            return value instanceof Integer ? (Integer) value : 0;
        }
        
        public double getDouble(String key) {
            Object value = values.get(key);
            return value instanceof Double ? (Double) value : 0.0;
        }
        
        public boolean flag(String key) {
            Object value = values.get(key);
            return value instanceof Boolean ? (Boolean) value : false;
        }
        
        public List<String> files() {
            return new ArrayList<>(positionalArgs);
        }
        
        public String file(int index) {
            return index < positionalArgs.size() ? positionalArgs.get(index) : null;
        }
        
        public int fileCount() {
            return positionalArgs.size();
        }
        
        public boolean has(String key) {
            return values.containsKey(key);
        }
        
        public Map<String, Object> options() {
            return new HashMap<>(values);
        }
        
        @Override
        public String toString() {
            return String.format("Args{options=%s, files=%s}", values, positionalArgs);
        }
    }
    
    /**
     * 参数构建器
     */
    public static class ArgsBuilder {
        private final String[] args;
        private final Args result = new Args();
        private String currentOption;
        
        ArgsBuilder(String[] args) {
            this.args = args;
        }
        
        public OptionBuilder option(String longName, String shortName) {
            currentOption = longName;
            OptionConfig config = new OptionConfig();
            result.configs.put(longName, config);
            if (shortName != null) {
                result.aliases.put(shortName, longName);
            }
            return new OptionBuilder(this, config);
        }
        
        public OptionBuilder option(String longName) {
            return option(longName, null);
        }
        
        public Args build() {
            try {
                parseArguments();
                return result;
            } catch (Exception e) {
                throw new RuntimeException("解析失败: " + e.getMessage(), e);
            }
        }
        
        private void parseArguments() {
            // 设置默认值
            for (Map.Entry<String, OptionConfig> entry : result.configs.entrySet()) {
                String key = entry.getKey().replaceFirst("--", "");
                OptionConfig config = entry.getValue();
                if (config.defaultValue != null) {
                    result.values.put(key, config.defaultValue);
                } else if (config.isFlag) {
                    result.values.put(key, false);
                }
            }
            
            boolean endOfOptions = false;
            
            for (int i = 0; i < args.length; i++) {
                String arg = args[i];
                
                if ("--".equals(arg)) {
                    endOfOptions = true;
                    continue;
                }
                
                if (!endOfOptions && arg.startsWith("--")) {
                    i = parseLongOption(i);
                } else if (!endOfOptions && arg.startsWith("-") && arg.length() > 1) {
                    i = parseShortOptions(i);
                } else {
                    result.positionalArgs.add(arg);
                }
            }
        }
        
        private int parseLongOption(int index) {
            String arg = args[index];
            String optionName;
            String optionValue = null;
            
            if (arg.contains("=")) {
                String[] parts = arg.split("=", 2);
                optionName = parts[0];
                optionValue = parts[1];
            } else {
                optionName = arg;
            }
            
            OptionConfig config = result.configs.get(optionName);
            if (config == null) {
                System.err.println("未知选项: " + optionName);
                return index;
            }
            
            String key = optionName.replaceFirst("--", "");
            
            if (config.isFlag) {
                result.values.put(key, true);
            } else {
                if (optionValue != null) {
                    result.values.put(key, convertValue(optionValue, config.type));
                } else if (index + 1 < args.length && !args[index + 1].startsWith("-")) {
                    result.values.put(key, convertValue(args[index + 1], config.type));
                    return index + 1;
                }
            }
            
            return index;
        }
        
        private int parseShortOptions(int index) {
            String arg = args[index];
            String shortOpts = arg.substring(1);
            
            for (int j = 0; j < shortOpts.length(); j++) {
                String shortOpt = "-" + shortOpts.charAt(j);
                String longName = result.aliases.get(shortOpt);
                
                if (longName == null) {
                    System.err.println("未知短选项: " + shortOpt);
                    continue;
                }
                
                OptionConfig config = result.configs.get(longName);
                String key = longName.replaceFirst("--", "");
                
                if (config.isFlag) {
                    result.values.put(key, true);
                } else if (j == shortOpts.length() - 1) {
                    if (index + 1 < args.length && !args[index + 1].startsWith("-")) {
                        result.values.put(key, convertValue(args[index + 1], config.type));
                        return index + 1;
                    }
                }
            }
            
            return index;
        }
        
        private Object convertValue(String value, Class<?> type) {
            try {
                if (type == Integer.class) return Integer.parseInt(value);
                if (type == Double.class) return Double.parseDouble(value);
                if (type == Boolean.class) return Boolean.parseBoolean(value);
                return value;
            } catch (NumberFormatException e) {
                System.err.println("无法转换值: " + value);
                return value;
            }
        }
    }
    
    /**
     * 选项构建器
     */
    public static class OptionBuilder {
        private final ArgsBuilder parent;
        private final OptionConfig config;
        
        OptionBuilder(ArgsBuilder parent, OptionConfig config) {
            this.parent = parent;
            this.config = config;
        }
        
        public OptionBuilder defaultValue(Object value) {
            config.defaultValue = value;
            return this;
        }
        
        public OptionBuilder asInt() {
            config.type = Integer.class;
            return this;
        }
        
        public OptionBuilder asDouble() {
            config.type = Double.class;
            return this;
        }
        
        public OptionBuilder flag() {
            config.isFlag = true;
            return this;
        }
        
        public OptionBuilder option(String longName, String shortName) {
            return parent.option(longName, shortName);
        }
        
        public OptionBuilder option(String longName) {
            return parent.option(longName);
        }
        
        public Args build() {
            return parent.build();
        }
    }
    
    /**
     * 选项配置
     */
    static class OptionConfig {
        Object defaultValue;
        Class<?> type = String.class;
        boolean isFlag = false;
    }
}
