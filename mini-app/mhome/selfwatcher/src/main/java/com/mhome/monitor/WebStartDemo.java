package com.mhome.monitor;

import javax.swing.*;
import java.awt.*;
import java.io.IOException;
import java.net.URL;

/**
 * Java Web Start 演示应用程序
 * 展示Web Start应用的基本功能和特性
 */
public class WebStartDemo extends J<PERSON>rame {
    
    private JTextArea logArea;
    private JLabel statusLabel;
    
    public WebStartDemo() {
        initializeGUI();
        logMessage("应用程序启动完成");
        checkWebStartEnvironment();
    }
    
    /**
     * 初始化GUI界面
     */
    private void initializeGUI() {
        setTitle("Java Web Start 演示应用");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(600, 400);
        setLocationRelativeTo(null);
        
        // 创建菜单栏
        createMenuBar();
        
        // 创建主面板
        JPanel mainPanel = new JPanel(new BorderLayout());
        
        // 创建工具栏
        JToolBar toolBar = createToolBar();
        mainPanel.add(toolBar, BorderLayout.NORTH);
        
        // 创建中央面板
        JPanel centerPanel = new JPanel(new BorderLayout());
        
        // 信息面板
        JPanel infoPanel = createInfoPanel();
        centerPanel.add(infoPanel, BorderLayout.NORTH);
        
        // 日志区域
        logArea = new JTextArea();
        logArea.setEditable(false);
        logArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        JScrollPane scrollPane = new JScrollPane(logArea);
        scrollPane.setBorder(BorderFactory.createTitledBorder("应用日志"));
        centerPanel.add(scrollPane, BorderLayout.CENTER);
        
        mainPanel.add(centerPanel, BorderLayout.CENTER);
        
        // 状态栏
        statusLabel = new JLabel("就绪");
        statusLabel.setBorder(BorderFactory.createLoweredBevelBorder());
        mainPanel.add(statusLabel, BorderLayout.SOUTH);
        
        add(mainPanel);
    }
    
    /**
     * 创建菜单栏
     */
    private void createMenuBar() {
        JMenuBar menuBar = new JMenuBar();
        
        // 文件菜单
        JMenu fileMenu = new JMenu("文件");
        JMenuItem exitItem = new JMenuItem("退出");
        exitItem.addActionListener(e -> System.exit(0));
        fileMenu.add(exitItem);
        
        // 工具菜单
        JMenu toolsMenu = new JMenu("工具");
        
        JMenuItem systemInfoItem = new JMenuItem("系统信息");
        systemInfoItem.addActionListener(e -> showSystemInfo());
        toolsMenu.add(systemInfoItem);
        
        JMenuItem webStartInfoItem = new JMenuItem("Web Start信息");
        webStartInfoItem.addActionListener(e -> showWebStartInfo());
        toolsMenu.add(webStartInfoItem);
        
        JMenuItem clearLogItem = new JMenuItem("清除日志");
        clearLogItem.addActionListener(e -> clearLog());
        toolsMenu.add(clearLogItem);
        
        // 帮助菜单
        JMenu helpMenu = new JMenu("帮助");
        JMenuItem aboutItem = new JMenuItem("关于");
        aboutItem.addActionListener(e -> showAbout());
        helpMenu.add(aboutItem);
        
        menuBar.add(fileMenu);
        menuBar.add(toolsMenu);
        menuBar.add(helpMenu);
        
        setJMenuBar(menuBar);
    }
    
    /**
     * 创建工具栏
     */
    private JToolBar createToolBar() {
        JToolBar toolBar = new JToolBar();
        toolBar.setFloatable(false);
        
        JButton refreshButton = new JButton("刷新");
        refreshButton.addActionListener(e -> refreshData());
        toolBar.add(refreshButton);
        
        toolBar.addSeparator();
        
        JButton testButton = new JButton("测试功能");
        testButton.addActionListener(e -> testFeatures());
        toolBar.add(testButton);
        
        toolBar.addSeparator();
        
        JButton helpButton = new JButton("帮助");
        helpButton.addActionListener(e -> showHelp());
        toolBar.add(helpButton);
        
        return toolBar;
    }
    
    /**
     * 创建信息面板
     */
    private JPanel createInfoPanel() {
        JPanel panel = new JPanel(new GridLayout(2, 2, 10, 5));
        panel.setBorder(BorderFactory.createTitledBorder("应用信息"));
        
        panel.add(new JLabel("应用名称:"));
        panel.add(new JLabel("Java Web Start 演示"));
        
        panel.add(new JLabel("版本:"));
        panel.add(new JLabel("1.0.0"));
        
        return panel;
    }
    
    /**
     * 检查Web Start环境
     */
    private void checkWebStartEnvironment() {
        try {
            // 检查是否在Web Start环境中运行
            String jnlpProperty = System.getProperty("jnlp.codebase");
            if (jnlpProperty != null) {
                logMessage("运行在Web Start环境中");
                logMessage("代码库: " + jnlpProperty);
                
                // 尝试获取JNLP服务
                try {
                    Class.forName("javax.jnlp.ServiceManager");
                    logMessage("JNLP服务可用");
                } catch (ClassNotFoundException e) {
                    logMessage("JNLP服务不可用");
                }
            } else {
                logMessage("运行在普通Java环境中");
            }
        } catch (Exception e) {
            logMessage("检查Web Start环境时出错: " + e.getMessage());
        }
    }
    
    /**
     * 显示系统信息
     */
    private void showSystemInfo() {
        StringBuilder info = new StringBuilder();
        info.append("Java版本: ").append(System.getProperty("java.version")).append("\n");
        info.append("Java供应商: ").append(System.getProperty("java.vendor")).append("\n");
        info.append("操作系统: ").append(System.getProperty("os.name")).append("\n");
        info.append("系统架构: ").append(System.getProperty("os.arch")).append("\n");
        info.append("用户目录: ").append(System.getProperty("user.home")).append("\n");
        info.append("工作目录: ").append(System.getProperty("user.dir")).append("\n");
        
        JTextArea textArea = new JTextArea(info.toString());
        textArea.setEditable(false);
        textArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        
        JScrollPane scrollPane = new JScrollPane(textArea);
        scrollPane.setPreferredSize(new Dimension(400, 300));
        
        JOptionPane.showMessageDialog(this, scrollPane, "系统信息", JOptionPane.INFORMATION_MESSAGE);
        
        logMessage("显示系统信息");
    }
    
    /**
     * 显示Web Start信息
     */
    private void showWebStartInfo() {
        StringBuilder info = new StringBuilder();
        
        // Web Start相关属性
        String[] webStartProperties = {
            "jnlp.codebase",
            "jnlp.application.href",
            "deployment.user.cachedir",
            "deployment.system.cachedir",
            "deployment.javaws.cache.update.timeout"
        };
        
        for (String prop : webStartProperties) {
            String value = System.getProperty(prop);
            info.append(prop).append(": ").append(value != null ? value : "未设置").append("\n");
        }
        
        JTextArea textArea = new JTextArea(info.toString());
        textArea.setEditable(false);
        textArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        
        JScrollPane scrollPane = new JScrollPane(textArea);
        scrollPane.setPreferredSize(new Dimension(500, 300));
        
        JOptionPane.showMessageDialog(this, scrollPane, "Web Start信息", JOptionPane.INFORMATION_MESSAGE);
        
        logMessage("显示Web Start信息");
    }
    
    /**
     * 刷新数据
     */
    private void refreshData() {
        statusLabel.setText("正在刷新...");
        logMessage("开始刷新数据");
        
        // 模拟数据刷新
        SwingWorker<Void, Void> worker = new SwingWorker<Void, Void>() {
            @Override
            protected Void doInBackground() throws Exception {
                Thread.sleep(2000); // 模拟耗时操作
                return null;
            }
            
            @Override
            protected void done() {
                statusLabel.setText("刷新完成");
                logMessage("数据刷新完成");
            }
        };
        
        worker.execute();
    }
    
    /**
     * 测试功能
     */
    private void testFeatures() {
        logMessage("开始测试应用功能");
        
        // 测试内存使用
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        logMessage("内存使用情况:");
        logMessage("  总内存: " + formatBytes(totalMemory));
        logMessage("  已用内存: " + formatBytes(usedMemory));
        logMessage("  空闲内存: " + formatBytes(freeMemory));
        
        // 测试线程
        logMessage("当前线程数: " + Thread.activeCount());
        
        // 测试网络连接（如果允许）
        testNetworkConnection();
        
        logMessage("功能测试完成");
    }
    
    /**
     * 测试网络连接
     */
    private void testNetworkConnection() {
        try {
            URL url = new URL("http://www.google.com");
            url.openConnection().connect();
            logMessage("网络连接测试: 成功");
        } catch (IOException e) {
            logMessage("网络连接测试: 失败 - " + e.getMessage());
        } catch (SecurityException e) {
            logMessage("网络连接测试: 被安全策略阻止");
        }
    }
    
    /**
     * 显示帮助
     */
    private void showHelp() {
        String helpText = "Java Web Start 演示应用\n\n" +
                         "功能说明:\n" +
                         "- 系统信息: 显示Java和操作系统信息\n" +
                         "- Web Start信息: 显示Web Start相关属性\n" +
                         "- 刷新: 模拟数据刷新操作\n" +
                         "- 测试功能: 测试应用的各种功能\n\n" +
                         "这个应用演示了Java Web Start的基本特性。";
        
        JOptionPane.showMessageDialog(this, helpText, "帮助", JOptionPane.INFORMATION_MESSAGE);
        logMessage("显示帮助信息");
    }
    
    /**
     * 显示关于对话框
     */
    private void showAbout() {
        String aboutText = "Java Web Start 演示应用\n" +
                          "版本: 1.0.0\n" +
                          "作者: 开发团队\n\n" +
                          "这是一个演示Java Web Start功能的示例应用程序。";
        
        JOptionPane.showMessageDialog(this, aboutText, "关于", JOptionPane.INFORMATION_MESSAGE);
        logMessage("显示关于信息");
    }
    
    /**
     * 清除日志
     */
    private void clearLog() {
        logArea.setText("");
        logMessage("日志已清除");
    }
    
    /**
     * 记录日志消息
     */
    private void logMessage(String message) {
        String timestamp = java.time.LocalTime.now().toString();
        String logEntry = "[" + timestamp + "] " + message + "\n";
        logArea.append(logEntry);
        logArea.setCaretPosition(logArea.getDocument().getLength());
    }
    
    /**
     * 格式化字节数
     */
    private String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.2f KB", bytes / 1024.0);
        if (bytes < 1024 * 1024 * 1024) return String.format("%.2f MB", bytes / (1024.0 * 1024));
        return String.format("%.2f GB", bytes / (1024.0 * 1024 * 1024));
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        // 设置系统外观
        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
        } catch (Exception e) {
            // 使用默认外观
        }
        
        // 在事件分发线程中创建和显示GUI
        SwingUtilities.invokeLater(() -> {
            new WebStartDemo().setVisible(true);
        });
    }
}
