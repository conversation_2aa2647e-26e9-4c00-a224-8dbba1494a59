package com.mhome.monitor;

import java.util.concurrent.BlockingDeque;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.TimeUnit;

/**
 * BlockingDeque 详细使用示例
 * 演示双端阻塞队列的各种操作和应用场景
 */
public class BlockingDequeDemo {
    
    public static void main(String[] args) {
        System.out.println("=== BlockingDeque 使用示例 ===\n");
        
        // 1. 基本操作演示
        basicOperationsDemo();
        
        // 2. 阻塞操作演示
        blockingOperationsDemo();
        
        // 3. 超时操作演示
        timeoutOperationsDemo();
        
        // 4. 生产者-消费者模式演示
        producerConsumerDemo();
        
        // 5. 工作窃取模式演示
        workStealingDemo();
    }
    
    /**
     * 1. 基本操作演示
     */
    private static void basicOperationsDemo() {
        System.out.println("=== 1. 基本操作演示 ===");
        
        // 创建一个容量为5的双端阻塞队列
        BlockingDeque<String> deque = new LinkedBlockingDeque<>(5);
        
        try {
            // 头部操作
            System.out.println("头部操作:");
            deque.addFirst("First-1");
            deque.addFirst("First-2");
            System.out.println("  添加到头部: First-1, First-2");
            System.out.println("  当前队列: " + deque);
            
            // 尾部操作
            System.out.println("\n尾部操作:");
            deque.addLast("Last-1");
            deque.addLast("Last-2");
            System.out.println("  添加到尾部: Last-1, Last-2");
            System.out.println("  当前队列: " + deque);
            
            // 检查操作
            System.out.println("\n检查操作:");
            System.out.println("  队列大小: " + deque.size());
            System.out.println("  是否为空: " + deque.isEmpty());
            System.out.println("  剩余容量: " + deque.remainingCapacity());
            System.out.println("  头部元素: " + deque.peekFirst());
            System.out.println("  尾部元素: " + deque.peekLast());
            
            // 移除操作
            System.out.println("\n移除操作:");
            String first = deque.removeFirst();
            String last = deque.removeLast();
            System.out.println("  从头部移除: " + first);
            System.out.println("  从尾部移除: " + last);
            System.out.println("  当前队列: " + deque);
            
        } catch (Exception e) {
            System.err.println("基本操作异常: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 2. 阻塞操作演示
     */
    private static void blockingOperationsDemo() {
        System.out.println("=== 2. 阻塞操作演示 ===");
        
        BlockingDeque<Integer> deque = new LinkedBlockingDeque<>(3);
        
        // 生产者线程 - 向头部添加元素
        Thread producer1 = new Thread(() -> {
            try {
                for (int i = 1; i <= 5; i++) {
                    System.out.println("生产者1尝试向头部添加: " + i);
                    deque.putFirst(i);  // 阻塞方法
                    System.out.println("生产者1成功添加到头部: " + i + ", 队列: " + deque);
                    Thread.sleep(1000);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                System.out.println("生产者1被中断");
            }
        }, "Producer-1");
        
        // 生产者线程 - 向尾部添加元素
        Thread producer2 = new Thread(() -> {
            try {
                for (int i = 10; i <= 14; i++) {
                    System.out.println("生产者2尝试向尾部添加: " + i);
                    deque.putLast(i);  // 阻塞方法
                    System.out.println("生产者2成功添加到尾部: " + i + ", 队列: " + deque);
                    Thread.sleep(1500);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                System.out.println("生产者2被中断");
            }
        }, "Producer-2");
        
        // 消费者线程 - 从头部取元素
        Thread consumer1 = new Thread(() -> {
            try {
                for (int i = 0; i < 4; i++) {
                    System.out.println("消费者1尝试从头部取元素...");
                    Integer item = deque.takeFirst();  // 阻塞方法
                    System.out.println("消费者1从头部取到: " + item + ", 队列: " + deque);
                    Thread.sleep(2000);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                System.out.println("消费者1被中断");
            }
        }, "Consumer-1");
        
        // 消费者线程 - 从尾部取元素
        Thread consumer2 = new Thread(() -> {
            try {
                for (int i = 0; i < 4; i++) {
                    System.out.println("消费者2尝试从尾部取元素...");
                    Integer item = deque.takeLast();  // 阻塞方法
                    System.out.println("消费者2从尾部取到: " + item + ", 队列: " + deque);
                    Thread.sleep(2500);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                System.out.println("消费者2被中断");
            }
        }, "Consumer-2");
        
        // 启动所有线程
        producer1.start();
        producer2.start();
        consumer1.start();
        consumer2.start();
        
        try {
            // 等待所有线程完成
            producer1.join();
            producer2.join();
            consumer1.join();
            consumer2.join();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        System.out.println("阻塞操作演示完成\n");
    }
    
    /**
     * 3. 超时操作演示
     */
    private static void timeoutOperationsDemo() {
        System.out.println("=== 3. 超时操作演示 ===");
        
        BlockingDeque<String> deque = new LinkedBlockingDeque<>(2);
        
        try {
            // 填满队列
            deque.putFirst("Item1");
            deque.putLast("Item2");
            System.out.println("队列已满: " + deque);
            
            // 尝试超时添加
            System.out.println("尝试在2秒内向头部添加元素...");
            boolean added = deque.offerFirst("NewItem", 2, TimeUnit.SECONDS);
            System.out.println("添加结果: " + added);
            
            // 尝试超时获取
            System.out.println("尝试在1秒内从尾部获取元素...");
            String item = deque.pollLast(1, TimeUnit.SECONDS);
            System.out.println("获取到: " + item);
            System.out.println("当前队列: " + deque);
            
            // 再次尝试添加
            System.out.println("再次尝试在1秒内向尾部添加元素...");
            added = deque.offerLast("NewItem2", 1, TimeUnit.SECONDS);
            System.out.println("添加结果: " + added);
            System.out.println("最终队列: " + deque);
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            System.err.println("超时操作被中断");
        }
        
        System.out.println();
    }
    
    /**
     * 任务类 - 移到类级别
     */
    static class Task {
        private final int id;
        private final String description;
        private final int priority; // 优先级，数字越小优先级越高

        public Task(int id, String description, int priority) {
            this.id = id;
            this.description = description;
            this.priority = priority;
        }

        public int getPriority() { return priority; }

        @Override
        public String toString() {
            return "Task{id=" + id + ", desc='" + description + "', priority=" + priority + "}";
        }
    }

    /**
     * 4. 生产者-消费者模式演示
     */
    private static void producerConsumerDemo() {
        System.out.println("=== 4. 生产者-消费者模式演示 ===");

        BlockingDeque<Task> taskQueue = new LinkedBlockingDeque<>(10);
        
        // 生产者 - 生产高优先级任务（添加到头部）
        Thread highPriorityProducer = new Thread(() -> {
            try {
                for (int i = 1; i <= 3; i++) {
                    Task task = new Task(i, "高优先级任务", 1);
                    taskQueue.putFirst(task);  // 高优先级任务放到头部
                    System.out.println("生产高优先级任务: " + task);
                    Thread.sleep(1000);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }, "HighPriorityProducer");
        
        // 生产者 - 生产普通任务（添加到尾部）
        Thread normalProducer = new Thread(() -> {
            try {
                for (int i = 10; i <= 15; i++) {
                    Task task = new Task(i, "普通任务", 5);
                    taskQueue.putLast(task);  // 普通任务放到尾部
                    System.out.println("生产普通任务: " + task);
                    Thread.sleep(800);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }, "NormalProducer");
        
        // 消费者 - 优先处理高优先级任务（从头部取）
        Thread consumer = new Thread(() -> {
            try {
                for (int i = 0; i < 8; i++) {
                    Task task = taskQueue.takeFirst();  // 从头部取，优先处理高优先级
                    System.out.println("处理任务: " + task);
                    Thread.sleep(1200);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }, "TaskConsumer");
        
        // 启动线程
        highPriorityProducer.start();
        normalProducer.start();
        consumer.start();
        
        try {
            highPriorityProducer.join();
            normalProducer.join();
            consumer.join();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        System.out.println("生产者-消费者演示完成\n");
    }
    
    /**
     * 5. 工作窃取模式演示
     */
    private static void workStealingDemo() {
        System.out.println("=== 5. 工作窃取模式演示 ===");
        
        // 每个工作线程都有自己的任务队列
        BlockingDeque<String> worker1Queue = new LinkedBlockingDeque<>();
        BlockingDeque<String> worker2Queue = new LinkedBlockingDeque<>();
        
        // 初始化任务
        try {
            // Worker1 有很多任务
            for (int i = 1; i <= 8; i++) {
                worker1Queue.putLast("Worker1-Task-" + i);
            }
            
            // Worker2 只有少量任务
            for (int i = 1; i <= 2; i++) {
                worker2Queue.putLast("Worker2-Task-" + i);
            }
            
            System.out.println("初始状态:");
            System.out.println("Worker1队列: " + worker1Queue.size() + " 个任务");
            System.out.println("Worker2队列: " + worker2Queue.size() + " 个任务");
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // Worker1 - 正常处理自己的任务
        Thread worker1 = new Thread(() -> {
            try {
                while (!worker1Queue.isEmpty()) {
                    String task = worker1Queue.takeFirst();  // 从自己队列头部取任务
                    System.out.println("Worker1处理自己的任务: " + task);
                    Thread.sleep(1000);
                }
                System.out.println("Worker1完成所有任务");
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }, "Worker1");
        
        // Worker2 - 处理完自己的任务后，从Worker1队列尾部"窃取"任务
        Thread worker2 = new Thread(() -> {
            try {
                // 先处理自己的任务
                while (!worker2Queue.isEmpty()) {
                    String task = worker2Queue.takeFirst();
                    System.out.println("Worker2处理自己的任务: " + task);
                    Thread.sleep(800);
                }
                
                System.out.println("Worker2完成自己的任务，开始窃取Worker1的任务...");
                
                // 窃取Worker1的任务（从尾部取，避免冲突）
                while (!worker1Queue.isEmpty()) {
                    String stolenTask = worker1Queue.pollLast(100, TimeUnit.MILLISECONDS);
                    if (stolenTask != null) {
                        System.out.println("Worker2窃取并处理: " + stolenTask);
                        Thread.sleep(800);
                    } else {
                        break; // 没有更多任务可窃取
                    }
                }
                
                System.out.println("Worker2完成所有任务（包括窃取的）");
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }, "Worker2");
        
        // 启动工作线程
        worker1.start();
        worker2.start();
        
        try {
            worker1.join();
            worker2.join();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        System.out.println("工作窃取演示完成");
        System.out.println("最终状态:");
        System.out.println("Worker1队列剩余: " + worker1Queue.size() + " 个任务");
        System.out.println("Worker2队列剩余: " + worker2Queue.size() + " 个任务");
    }
}
