package com.mhome.sw;

import java.net.Inet4Address;
import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.SocketAddress;
import java.net.URI;
import java.net.URL;

/**
 * Hello world!
 */
public final class App {
    private App() {
    }

    /**
     * Says hello to the world.
     * @param args The arguments of the program.
     */
    public static void main(String[] args) {
        System.out.println("Hello World!");

        InetAddress address = InetAddress.getLocalHost();
        System.out.println("Host Name: " + address.getHostName());
        System.out.println("IP Address: " + address.getHostAddress());

        Inet4Address b;

        Inet6Address c;

        SocketAddress d;

        InetSocketAddress e;

        URI u1;

        Thread.sleep(0, 0);

        
    }
}
