package com.mhome.monitor;

import java.util.*;

/**
 * 动态命令行参数解析器
 * 自动识别和解析所有命令行参数，无需预先定义选项
 */
public class UniversalCommandLineParser {

    public static void main(String[] args) {
        System.out.println("=== 动态命令行参数解析器演示 ===\n");

        // 如果没有参数，运行测试用例
        if (args.length == 0) {
            runTestCases();
            return;
        }

        // 解析实际参数
        demonstrateDynamicParsing(args);
    }

    /**
     * 运行测试用例
     */
    private static void runTestCases() {
        String[] testCases = {
            "--host localhost --port 8080 --verbose file1.txt file2.txt",
            "-h ************* -p 9000 -vd file1.txt file2.txt",
            "--host=localhost --port=8080 --config=/path/to/config input.txt",
            "-v --host=localhost -p 8080 file1.txt file2.txt",
            "--host localhost -v -- --not-an-option file.txt",
            "input.txt output.txt backup.txt",
            "--enable-ssl --timeout=30 --threads 4 -x -y -z file.txt",
            "--database-url ****************************** --user admin --password secret",
            "-abc --long-option-name value --another=test file1 file2 file3"
        };

        for (int i = 0; i < testCases.length; i++) {
            System.out.println("=== 测试用例 " + (i + 1) + " ===");
            System.out.println("输入: " + testCases[i]);
            String[] testArgs = testCases[i].split("\\s+");
            demonstrateDynamicParsing(testArgs);
            System.out.println();
        }
    }

    /**
     * 演示动态解析
     */
    private static void demonstrateDynamicParsing(String[] args) {
        try {
            // 动态解析所有参数
            DynamicArgs result = DynamicArgsParser.parse(args);

            // 显示解析结果
            System.out.println("解析结果:");
            System.out.println("  长选项: " + result.getLongOptions());
            System.out.println("  短选项: " + result.getShortOptions());
            System.out.println("  标志选项: " + result.getFlags());
            System.out.println("  位置参数: " + result.getPositionalArgs());
            System.out.println("  参数数量: " + result.getPositionalArgCount());

            // 演示智能类型推断
            System.out.println("\n智能类型推断:");
            for (String option : result.getAllOptionNames()) {
                Object value = result.getValue(option);
                String type = result.getInferredType(option);
                System.out.println("  " + option + " = " + value + " (推断类型: " + type + ")");
            }

            // 演示便捷访问方法
            System.out.println("\n便捷访问:");
            System.out.println("  所有选项: " + result.getAllOptions());
            System.out.println("  是否有标志选项: " + !result.getFlags().isEmpty());
            System.out.println("  第一个文件: " + result.getPositionalArg(0));

        } catch (Exception e) {
            System.err.println("解析错误: " + e.getMessage());
        }
    }
    
    /**
     * 动态命令行参数解析器
     */
    static class DynamicArgsParser {

        public static DynamicArgs parse(String[] args) {
            Map<String, Object> longOptions = new HashMap<>();
            Map<String, Object> shortOptions = new HashMap<>();
            Set<String> flags = new HashSet<>();
            List<String> positionalArgs = new ArrayList<>();
            boolean endOfOptions = false;

            for (int i = 0; i < args.length; i++) {
                String arg = args[i];

                // 处理 -- 分隔符
                if ("--".equals(arg)) {
                    endOfOptions = true;
                    continue;
                }

                if (!endOfOptions && arg.startsWith("--")) {
                    // 长选项
                    i = parseLongOption(args, i, longOptions, flags);
                } else if (!endOfOptions && arg.startsWith("-") && arg.length() > 1) {
                    // 短选项
                    i = parseShortOptions(args, i, shortOptions, flags);
                } else {
                    // 位置参数
                    positionalArgs.add(arg);
                }
            }

            return new DynamicArgs(longOptions, shortOptions, flags, positionalArgs);
        }

        private static int parseLongOption(String[] args, int index, Map<String, Object> longOptions, Set<String> flags) {
            String arg = args[index];
            String optionName;
            String optionValue = null;

            // 处理 --option=value 格式
            if (arg.contains("=")) {
                String[] parts = arg.split("=", 2);
                optionName = parts[0].substring(2); // 去掉 --
                optionValue = parts[1];
            } else {
                optionName = arg.substring(2); // 去掉 --
            }

            if (optionValue != null) {
                // 有值的选项
                longOptions.put(optionName, inferType(optionValue));
            } else if (index + 1 < args.length && !args[index + 1].startsWith("-")) {
                // 下一个参数作为值
                String value = args[index + 1];
                longOptions.put(optionName, inferType(value));
                return index + 1;
            } else {
                // 标志选项
                flags.add(optionName);
            }

            return index;
        }

        private static int parseShortOptions(String[] args, int index, Map<String, Object> shortOptions, Set<String> flags) {
            String arg = args[index];
            String shortOpts = arg.substring(1);

            for (int j = 0; j < shortOpts.length(); j++) {
                String shortOpt = String.valueOf(shortOpts.charAt(j));

                if (j == shortOpts.length() - 1 && index + 1 < args.length && !args[index + 1].startsWith("-")) {
                    // 最后一个短选项，且后面有值
                    String value = args[index + 1];
                    shortOptions.put(shortOpt, inferType(value));
                    return index + 1;
                } else {
                    // 标志选项
                    flags.add(shortOpt);
                }
            }

            return index;
        }

        /**
         * 智能类型推断
         */
        private static Object inferType(String value) {
            // 尝试推断为整数
            try {
                return Integer.parseInt(value);
            } catch (NumberFormatException ignored) {}

            // 尝试推断为浮点数
            try {
                return Double.parseDouble(value);
            } catch (NumberFormatException ignored) {}

            // 尝试推断为布尔值
            if ("true".equalsIgnoreCase(value) || "false".equalsIgnoreCase(value)) {
                return Boolean.parseBoolean(value);
            }

            // 默认为字符串
            return value;
        }
    }
        
    }

    /**
     * 动态解析结果
     */
    class DynamicArgs {
        private final Map<String, Object> longOptions;
        private final Map<String, Object> shortOptions;
        private final Set<String> flags;
        private final List<String> positionalArgs;

        DynamicArgs(Map<String, Object> longOptions, Map<String, Object> shortOptions,
                   Set<String> flags, List<String> positionalArgs) {
            this.longOptions = longOptions;
            this.shortOptions = shortOptions;
            this.flags = flags;
            this.positionalArgs = positionalArgs;
        }

        // 获取长选项
        public Map<String, Object> getLongOptions() {
            return new HashMap<>(longOptions);
        }

        // 获取短选项
        public Map<String, Object> getShortOptions() {
            return new HashMap<>(shortOptions);
        }

        // 获取所有标志选项
        public Set<String> getFlags() {
            return new HashSet<>(flags);
        }

        // 获取位置参数
        public List<String> getPositionalArgs() {
            return new ArrayList<>(positionalArgs);
        }

        public String getPositionalArg(int index) {
            return index < positionalArgs.size() ? positionalArgs.get(index) : null;
        }

        public int getPositionalArgCount() {
            return positionalArgs.size();
        }

        // 通用获取方法
        public Object getValue(String key) {
            // 先查找长选项
            if (longOptions.containsKey(key)) {
                return longOptions.get(key);
            }
            // 再查找短选项
            if (shortOptions.containsKey(key)) {
                return shortOptions.get(key);
            }
            // 检查是否为标志
            if (flags.contains(key)) {
                return true;
            }
            return null;
        }

        // 类型安全的获取方法
        public String getString(String key) {
            Object value = getValue(key);
            return value != null ? value.toString() : null;
        }

        public int getInt(String key) {
            Object value = getValue(key);
            return value instanceof Integer ? (Integer) value : 0;
        }

        public double getDouble(String key) {
            Object value = getValue(key);
            return value instanceof Double ? (Double) value : 0.0;
        }

        public boolean getBoolean(String key) {
            Object value = getValue(key);
            return value instanceof Boolean ? (Boolean) value : flags.contains(key);
        }

        // 获取所有选项名称
        public Set<String> getAllOptionNames() {
            Set<String> allNames = new HashSet<>();
            allNames.addAll(longOptions.keySet());
            allNames.addAll(shortOptions.keySet());
            allNames.addAll(flags);
            return allNames;
        }

        // 获取所有选项（合并长选项、短选项和标志）
        public Map<String, Object> getAllOptions() {
            Map<String, Object> allOptions = new HashMap<>();
            allOptions.putAll(longOptions);
            allOptions.putAll(shortOptions);
            for (String flag : flags) {
                allOptions.put(flag, true);
            }
            return allOptions;
        }

        // 智能类型推断
        public String getInferredType(String key) {
            Object value = getValue(key);
            if (value == null) return "null";
            if (value instanceof Integer) return "Integer";
            if (value instanceof Double) return "Double";
            if (value instanceof Boolean) return "Boolean";
            return "String";
        }

        // 检查是否存在选项
        public boolean hasOption(String key) {
            return longOptions.containsKey(key) || shortOptions.containsKey(key) || flags.contains(key);
        }

        // 检查是否为标志选项
        public boolean isFlag(String key) {
            return flags.contains(key);
        }

        @Override
        public String toString() {
            return String.format("DynamicArgs{longOptions=%s, shortOptions=%s, flags=%s, positionalArgs=%s}",
                               longOptions, shortOptions, flags, positionalArgs);
        }
    }

