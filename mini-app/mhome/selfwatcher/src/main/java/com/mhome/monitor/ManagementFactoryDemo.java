package com.mhome.monitor;

import java.lang.management.*;
import java.util.List;

/**
 * ManagementFactory 详细使用示例
 * 展示如何使用 ManagementFactory 获取 JVM 的各种管理和监控信息
 */
public class ManagementFactoryDemo {
    
    public static void main(String[] args) {
        System.out.println("=== ManagementFactory 功能演示 ===\n");
        
        // 1. 运行时信息
        showRuntimeInfo();
        
        // 2. 内存信息
        showMemoryInfo();
        
        // 3. 垃圾收集器信息
        showGarbageCollectorInfo();
        
        // 4. 线程信息
        showThreadInfo();
        
        // 5. 类加载信息
        showClassLoadingInfo();
        
        // 6. 编译信息
        showCompilationInfo();
        
        // 7. 操作系统信息
        showOperatingSystemInfo();
        
        // 8. 内存池信息
        showMemoryPoolInfo();
    }
    
    /**
     * 1. 运行时信息 - RuntimeMXBean
     */
    private static void showRuntimeInfo() {
        System.out.println("=== 1. 运行时信息 (RuntimeMXBean) ===");
        
        RuntimeMXBean runtimeBean = ManagementFactory.getRuntimeMXBean();
        
        System.out.println("JVM 名称: " + runtimeBean.getName());
        System.out.println("JVM 规范名称: " + runtimeBean.getSpecName());
        System.out.println("JVM 规范版本: " + runtimeBean.getSpecVersion());
        System.out.println("JVM 规范供应商: " + runtimeBean.getSpecVendor());
        System.out.println("JVM 实现名称: " + runtimeBean.getVmName());
        System.out.println("JVM 实现版本: " + runtimeBean.getVmVersion());
        System.out.println("JVM 实现供应商: " + runtimeBean.getVmVendor());
        System.out.println("管理规范版本: " + runtimeBean.getManagementSpecVersion());
        System.out.println("启动时间: " + new java.util.Date(runtimeBean.getStartTime()));
        System.out.println("运行时间: " + runtimeBean.getUptime() + " 毫秒");
        System.out.println("类路径: " + runtimeBean.getClassPath());
        System.out.println("库路径: " + runtimeBean.getLibraryPath());
        System.out.println("引导类路径: " + (runtimeBean.isBootClassPathSupported() ? 
            runtimeBean.getBootClassPath() : "不支持"));
        
        System.out.println("JVM 输入参数:");
        List<String> inputArgs = runtimeBean.getInputArguments();
        for (int i = 0; i < inputArgs.size(); i++) {
            System.out.println("  [" + i + "] " + inputArgs.get(i));
        }
        
        System.out.println("系统属性:");
        runtimeBean.getSystemProperties().forEach((key, value) -> {
            if (key.toString().startsWith("java.") || key.toString().startsWith("os.")) {
                System.out.println("  " + key + " = " + value);
            }
        });
        
        System.out.println();
    }
    
    /**
     * 2. 内存信息 - MemoryMXBean
     */
    private static void showMemoryInfo() {
        System.out.println("=== 2. 内存信息 (MemoryMXBean) ===");
        
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        
        // 堆内存使用情况
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        System.out.println("堆内存使用情况:");
        System.out.println("  初始大小: " + formatBytes(heapUsage.getInit()));
        System.out.println("  已使用: " + formatBytes(heapUsage.getUsed()));
        System.out.println("  已提交: " + formatBytes(heapUsage.getCommitted()));
        System.out.println("  最大大小: " + formatBytes(heapUsage.getMax()));
        
        // 非堆内存使用情况
        MemoryUsage nonHeapUsage = memoryBean.getNonHeapMemoryUsage();
        System.out.println("非堆内存使用情况:");
        System.out.println("  初始大小: " + formatBytes(nonHeapUsage.getInit()));
        System.out.println("  已使用: " + formatBytes(nonHeapUsage.getUsed()));
        System.out.println("  已提交: " + formatBytes(nonHeapUsage.getCommitted()));
        System.out.println("  最大大小: " + formatBytes(nonHeapUsage.getMax()));
        
        System.out.println("待回收对象数量: " + memoryBean.getObjectPendingFinalizationCount());
        System.out.println("是否支持详细内存使用: " + memoryBean.isVerbose());
        
        System.out.println();
    }
    
    /**
     * 3. 垃圾收集器信息 - GarbageCollectorMXBean
     */
    private static void showGarbageCollectorInfo() {
        System.out.println("=== 3. 垃圾收集器信息 (GarbageCollectorMXBean) ===");
        
        List<GarbageCollectorMXBean> gcBeans = ManagementFactory.getGarbageCollectorMXBeans();
        
        for (GarbageCollectorMXBean gcBean : gcBeans) {
            System.out.println("垃圾收集器: " + gcBean.getName());
            System.out.println("  收集次数: " + gcBean.getCollectionCount());
            System.out.println("  收集时间: " + gcBean.getCollectionTime() + " 毫秒");
            System.out.println("  管理的内存池: " + java.util.Arrays.toString(gcBean.getMemoryPoolNames()));
            System.out.println("  是否有效: " + gcBean.isValid());
            System.out.println();
        }
    }
    
    /**
     * 4. 线程信息 - ThreadMXBean
     */
    private static void showThreadInfo() {
        System.out.println("=== 4. 线程信息 (ThreadMXBean) ===");
        
        ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
        
        System.out.println("当前线程数: " + threadBean.getThreadCount());
        System.out.println("守护线程数: " + threadBean.getDaemonThreadCount());
        System.out.println("峰值线程数: " + threadBean.getPeakThreadCount());
        System.out.println("启动的线程总数: " + threadBean.getTotalStartedThreadCount());
        
        System.out.println("是否支持线程竞争监控: " + threadBean.isThreadContentionMonitoringSupported());
        System.out.println("是否支持线程CPU时间: " + threadBean.isThreadCpuTimeSupported());
        
        // 获取所有线程ID
        long[] threadIds = threadBean.getAllThreadIds();
        System.out.println("所有线程ID: " + java.util.Arrays.toString(threadIds));
        
        System.out.println();
    }
    
    /**
     * 5. 类加载信息 - ClassLoadingMXBean
     */
    private static void showClassLoadingInfo() {
        System.out.println("=== 5. 类加载信息 (ClassLoadingMXBean) ===");
        
        ClassLoadingMXBean classBean = ManagementFactory.getClassLoadingMXBean();
        
        System.out.println("当前加载的类数量: " + classBean.getLoadedClassCount());
        System.out.println("总共加载的类数量: " + classBean.getTotalLoadedClassCount());
        System.out.println("卸载的类数量: " + classBean.getUnloadedClassCount());
        System.out.println("是否启用详细类加载: " + classBean.isVerbose());
        
        System.out.println();
    }
    
    /**
     * 6. 编译信息 - CompilationMXBean
     */
    private static void showCompilationInfo() {
        System.out.println("=== 6. 编译信息 (CompilationMXBean) ===");
        
        CompilationMXBean compilationBean = ManagementFactory.getCompilationMXBean();
        
        if (compilationBean != null) {
            System.out.println("JIT 编译器名称: " + compilationBean.getName());
            System.out.println("是否支持编译时间监控: " + compilationBean.isCompilationTimeMonitoringSupported());
            if (compilationBean.isCompilationTimeMonitoringSupported()) {
                System.out.println("编译时间: " + compilationBean.getTotalCompilationTime() + " 毫秒");
            }
        } else {
            System.out.println("JIT 编译器不可用");
        }
        
        System.out.println();
    }
    
    /**
     * 7. 操作系统信息 - OperatingSystemMXBean
     */
    private static void showOperatingSystemInfo() {
        System.out.println("=== 7. 操作系统信息 (OperatingSystemMXBean) ===");
        
        OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
        
        System.out.println("操作系统名称: " + osBean.getName());
        System.out.println("操作系统架构: " + osBean.getArch());
        System.out.println("操作系统版本: " + osBean.getVersion());
        System.out.println("可用处理器数: " + osBean.getAvailableProcessors());
        System.out.println("系统平均负载: " + osBean.getSystemLoadAverage());
        
        System.out.println();
    }
    
    /**
     * 8. 内存池信息 - MemoryPoolMXBean
     */
    private static void showMemoryPoolInfo() {
        System.out.println("=== 8. 内存池信息 (MemoryPoolMXBean) ===");
        
        List<MemoryPoolMXBean> poolBeans = ManagementFactory.getMemoryPoolMXBeans();
        
        for (MemoryPoolMXBean poolBean : poolBeans) {
            System.out.println("内存池: " + poolBean.getName());
            System.out.println("  类型: " + poolBean.getType());
            
            MemoryUsage usage = poolBean.getUsage();
            if (usage != null) {
                System.out.println("  当前使用情况:");
                System.out.println("    初始: " + formatBytes(usage.getInit()));
                System.out.println("    已使用: " + formatBytes(usage.getUsed()));
                System.out.println("    已提交: " + formatBytes(usage.getCommitted()));
                System.out.println("    最大: " + formatBytes(usage.getMax()));
            }
            
            System.out.println("  是否有效: " + poolBean.isValid());
            System.out.println("  管理器名称: " + java.util.Arrays.toString(poolBean.getMemoryManagerNames()));
            System.out.println();
        }
    }
    
    /**
     * 格式化字节数为可读格式
     */
    private static String formatBytes(long bytes) {
        if (bytes == -1) return "未定义";
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.2f KB", bytes / 1024.0);
        if (bytes < 1024 * 1024 * 1024) return String.format("%.2f MB", bytes / (1024.0 * 1024));
        return String.format("%.2f GB", bytes / (1024.0 * 1024 * 1024));
    }
}
