package com.mhome.monitor;

import java.util.List;

/**
 * 测试获取进程PID的功能
 */
public class PidTest {

    public static void main(String[] args) {
        System.out.println("=== 进程监控工具测试 ===");

        // 测试1: 获取当前进程PID
        testCurrentPid();

        // 测试2: 获取指定进程PID
        testSpecificProcess();

        // 测试3: 获取命令行参数
        testCommandLineArguments();

        System.out.println("=== 测试完成 ===");
    }

    private static void testCurrentPid() {
        System.out.println("\n--- 当前进程PID测试 ---");

        // 获取当前进程PID
        String pid = MonitorUtil.getCurrentPid();
        System.out.println("当前进程PID: " + pid);

        // 显示Java版本信息
        String javaVersion = System.getProperty("java.version");
        System.out.println("Java版本: " + javaVersion);

        // 显示JVM名称（通常包含PID信息）
        String jvmName = java.lang.management.ManagementFactory.getRuntimeMXBean().getName();
        System.out.println("JVM名称: " + jvmName);

        // 验证PID是否为数字
        try {
            Long.parseLong(pid);
            System.out.println("PID格式验证: ✓ 有效的数字格式");
        } catch (NumberFormatException e) {
            System.out.println("PID格式验证: ✗ 无效的数字格式");
        }
    }

    private static void testSpecificProcess() {
        System.out.println("\n--- 指定进程PID测试 ---");

        // 测试常见的进程名称
        String[] testProcesses = {"java", "chrome", "firefox", "code", "Terminal"};

        for (String processName : testProcesses) {
            System.out.println("\n查找进程: " + processName);

            // 获取单个PID
            String pid = MonitorUtil.getPid(processName);
            if (pid != null) {
                System.out.println("  找到PID: " + pid);
            } else {
                System.out.println("  未找到进程");
            }

            // 获取所有匹配的PID
            List<String> allPids = MonitorUtil.getAllPids(processName);
            if (!allPids.isEmpty()) {
                System.out.println("  所有匹配的PID: " + allPids);
                System.out.println("  进程数量: " + allPids.size());
            } else {
                System.out.println("  未找到任何匹配的进程");
            }
        }

        // 测试边界情况
        System.out.println("\n--- 边界情况测试 ---");
        System.out.println("空字符串测试: " + MonitorUtil.getPid(""));
        System.out.println("null测试: " + MonitorUtil.getPid(null));
        System.out.println("不存在的进程测试: " + MonitorUtil.getPid("nonexistent_process_12345"));
    }

    private static void testCommandLineArguments() {
        System.out.println("\n--- 命令行参数测试 ---");

        // 测试1: 获取当前进程的JVM参数
        System.out.println("当前进程JVM参数:");
        List<String> currentArgs = MonitorUtil.getCurrentProcessArguments();
        for (int i = 0; i < currentArgs.size(); i++) {
            System.out.println("  [" + i + "] " + currentArgs.get(i));
        }

        // 测试2: 获取当前进程的完整信息
        System.out.println("\n当前进程完整信息:");
        String processInfo = MonitorUtil.getCurrentProcessInfo();
        System.out.println(processInfo);

        // 测试3: 获取指定进程的命令行参数
        System.out.println("\n--- 指定进程命令行参数测试 ---");
        String currentPid = MonitorUtil.getCurrentPid();
        System.out.println("获取当前进程(" + currentPid + ")的命令行参数:");
        String cmdLine = MonitorUtil.getProcessArguments(currentPid);
        if (cmdLine != null && !cmdLine.isEmpty()) {
            System.out.println("命令行: " + cmdLine);
        } else {
            System.out.println("无法获取命令行参数");
        }

        // 测试4: 尝试获取其他Java进程的命令行参数
        List<String> javaPids = MonitorUtil.getAllPids("java");
        if (javaPids.size() > 1) {
            System.out.println("\n其他Java进程的命令行参数:");
            for (String pid : javaPids) {
                if (!pid.equals(currentPid)) {
                    System.out.println("PID " + pid + ":");
                    String otherCmdLine = MonitorUtil.getProcessArguments(pid);
                    if (otherCmdLine != null && !otherCmdLine.isEmpty()) {
                        System.out.println("  " + otherCmdLine);
                    } else {
                        System.out.println("  无法获取命令行参数");
                    }
                    break; // 只测试一个其他进程
                }
            }
        }

        // 测试5: 边界情况
        System.out.println("\n--- 命令行参数边界情况测试 ---");
        System.out.println("空PID测试: " + MonitorUtil.getProcessArguments(""));
        System.out.println("null PID测试: " + MonitorUtil.getProcessArguments(null));
        System.out.println("不存在的PID测试: " + MonitorUtil.getProcessArguments("999999"));
    }
}
