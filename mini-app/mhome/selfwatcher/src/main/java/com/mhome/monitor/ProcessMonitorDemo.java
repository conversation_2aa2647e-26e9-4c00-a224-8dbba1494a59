package com.mhome.monitor;

import java.util.List;
import java.util.Scanner;

/**
 * 进程监控演示程序
 * 演示如何使用MonitorUtil类的各种功能
 */
public class ProcessMonitorDemo {
    
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.println("=== 进程监控工具演示 ===");
        System.out.println("这个程序演示了如何获取进程PID和命令行参数");
        
        while (true) {
            showMenu();
            System.out.print("请选择操作 (1-6): ");
            
            try {
                int choice = Integer.parseInt(scanner.nextLine().trim());
                
                switch (choice) {
                    case 1:
                        showCurrentProcessInfo();
                        break;
                    case 2:
                        findProcessByName(scanner);
                        break;
                    case 3:
                        showProcessArguments(scanner);
                        break;
                    case 4:
                        showCurrentProcessArguments();
                        break;
                    case 5:
                        showAllJavaProcesses();
                        break;
                    case 6:
                        System.out.println("退出程序...");
                        return;
                    default:
                        System.out.println("无效选择，请重新输入！");
                }
            } catch (NumberFormatException e) {
                System.out.println("请输入有效的数字！");
            }
            
            System.out.println("\n按回车键继续...");
            scanner.nextLine();
        }
    }
    
    private static void showMenu() {
        System.out.println("\n=== 功能菜单 ===");
        System.out.println("1. 显示当前进程信息");
        System.out.println("2. 根据进程名查找PID");
        System.out.println("3. 获取指定PID的命令行参数");
        System.out.println("4. 显示当前进程的启动参数");
        System.out.println("5. 显示所有Java进程");
        System.out.println("6. 退出");
    }
    
    private static void showCurrentProcessInfo() {
        System.out.println("\n=== 当前进程信息 ===");
        String info = MonitorUtil.getCurrentProcessInfo();
        System.out.println(info);
    }
    
    private static void findProcessByName(Scanner scanner) {
        System.out.print("请输入进程名称: ");
        String processName = scanner.nextLine().trim();
        
        if (processName.isEmpty()) {
            System.out.println("进程名称不能为空！");
            return;
        }
        
        System.out.println("\n=== 查找进程: " + processName + " ===");
        
        // 获取单个PID
        String pid = MonitorUtil.getPid(processName);
        if (pid != null) {
            System.out.println("找到进程PID: " + pid);
        } else {
            System.out.println("未找到进程");
        }
        
        // 获取所有匹配的PID
        List<String> allPids = MonitorUtil.getAllPids(processName);
        if (!allPids.isEmpty()) {
            System.out.println("所有匹配的PID: " + allPids);
            System.out.println("总共找到 " + allPids.size() + " 个进程");
        }
    }
    
    private static void showProcessArguments(Scanner scanner) {
        System.out.print("请输入进程PID: ");
        String pid = scanner.nextLine().trim();
        
        if (pid.isEmpty()) {
            System.out.println("PID不能为空！");
            return;
        }
        
        System.out.println("\n=== 进程 " + pid + " 的命令行参数 ===");
        String cmdLine = MonitorUtil.getProcessArguments(pid);
        
        if (cmdLine != null && !cmdLine.isEmpty()) {
            System.out.println("命令行: " + cmdLine);
        } else {
            System.out.println("无法获取该进程的命令行参数");
        }
    }
    
    private static void showCurrentProcessArguments() {
        System.out.println("\n=== 当前进程启动参数 ===");
        
        List<String> args = MonitorUtil.getCurrentProcessArguments();
        if (args.isEmpty()) {
            System.out.println("未找到启动参数");
        } else {
            System.out.println("启动参数列表:");
            for (int i = 0; i < args.size(); i++) {
                System.out.println("  [" + i + "] " + args.get(i));
            }
        }
    }
    
    private static void showAllJavaProcesses() {
        System.out.println("\n=== 所有Java进程 ===");
        
        List<String> javaPids = MonitorUtil.getAllPids("java");
        if (javaPids.isEmpty()) {
            System.out.println("未找到Java进程");
            return;
        }
        
        System.out.println("找到 " + javaPids.size() + " 个Java进程:");
        for (String pid : javaPids) {
            System.out.println("\nPID: " + pid);
            String cmdLine = MonitorUtil.getProcessArguments(pid);
            if (cmdLine != null && !cmdLine.isEmpty()) {
                // 截断过长的命令行以便显示
                if (cmdLine.length() > 100) {
                    cmdLine = cmdLine.substring(0, 100) + "...";
                }
                System.out.println("  命令行: " + cmdLine);
            } else {
                System.out.println("  命令行: 无法获取");
            }
        }
    }
}
