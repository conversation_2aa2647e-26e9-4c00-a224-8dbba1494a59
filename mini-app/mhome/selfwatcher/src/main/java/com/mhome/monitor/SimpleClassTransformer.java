package com.mhome.monitor;

import java.lang.instrument.ClassFileTransformer;
import java.lang.instrument.IllegalClassFormatException;
import java.security.ProtectionDomain;

/**
 * 简单的类转换器示例
 * 演示如何在类加载时拦截和修改字节码
 */
public class SimpleClassTransformer implements ClassFileTransformer {
    
    @Override
    public byte[] transform(ClassLoader loader, String className, Class<?> classBeingRedefined,
                          ProtectionDomain protectionDomain, byte[] classfileBuffer)
            throws IllegalClassFormatException {
        
        // 只处理我们感兴趣的类
        if (className != null && className.contains("com/mhome/monitor")) {
            System.out.println("拦截到类加载: " + className);
            System.out.println("  类加载器: " + (loader != null ? loader.getClass().getName() : "Bootstrap ClassLoader"));
            System.out.println("  字节码大小: " + classfileBuffer.length + " 字节");
            
            // 这里可以修改字节码
            // 为了简单起见，我们只是记录信息，不修改字节码
            return null; // 返回null表示不修改字节码
        }
        
        // 对于其他类，不做任何修改
        return null;
    }
}
