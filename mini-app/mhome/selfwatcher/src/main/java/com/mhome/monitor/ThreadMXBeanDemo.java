package com.mhome.monitor;

import java.lang.management.ManagementFactory;
import java.lang.management.ThreadInfo;
import java.lang.management.ThreadMXBean;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * ThreadMXBean 详细演示
 * 证明 ThreadMXBean 只监控当前JVM进程的线程，不是系统全局线程
 */
public class ThreadMXBeanDemo {
    
    public static void main(String[] args) throws InterruptedException {
        System.out.println("=== ThreadMXBean 作用范围演示 ===\n");
        
        ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
        
        // 1. 显示初始线程状态
        System.out.println("=== 1. 程序启动时的线程状态 ===");
        showThreadInfo(threadBean, "程序启动");
        
        // 2. 创建新线程并观察变化
        System.out.println("\n=== 2. 创建新线程后的变化 ===");
        
        // 创建几个新线程
        ExecutorService executor = Executors.newFixedThreadPool(3);
        
        // 提交一些任务
        for (int i = 0; i < 3; i++) {
            final int taskId = i;
            executor.submit(() -> {
                try {
                    System.out.println("任务 " + taskId + " 在线程 " + 
                        Thread.currentThread().getName() + " 中运行");
                    Thread.sleep(5000); // 让线程存活一段时间
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }
        
        // 等待线程池创建线程
        Thread.sleep(1000);
        
        showThreadInfo(threadBean, "创建线程池后");
        
        // 3. 显示所有线程的详细信息
        System.out.println("\n=== 3. 当前JVM进程中所有线程的详细信息 ===");
        showAllThreadDetails(threadBean);
        
        // 4. 演示线程监控功能
        System.out.println("\n=== 4. 线程监控功能演示 ===");
        demonstrateThreadMonitoring(threadBean);
        
        // 5. 关闭线程池并观察变化
        System.out.println("\n=== 5. 关闭线程池后的变化 ===");
        executor.shutdown();
        executor.awaitTermination(10, TimeUnit.SECONDS);
        
        Thread.sleep(1000); // 等待线程完全结束
        showThreadInfo(threadBean, "关闭线程池后");
        
        System.out.println("\n=== 总结 ===");
        System.out.println("ThreadMXBean 只能监控当前JVM进程内的线程，包括:");
        System.out.println("- 主线程 (main)");
        System.out.println("- JVM内部线程 (GC线程、编译器线程等)");
        System.out.println("- 应用创建的线程 (用户线程、线程池线程等)");
        System.out.println("- 但不包括其他进程的线程或系统级线程");
    }
    
    /**
     * 显示线程基本信息
     */
    private static void showThreadInfo(ThreadMXBean threadBean, String stage) {
        System.out.println("阶段: " + stage);
        System.out.println("  当前线程数: " + threadBean.getThreadCount());
        System.out.println("  守护线程数: " + threadBean.getDaemonThreadCount());
        System.out.println("  峰值线程数: " + threadBean.getPeakThreadCount());
        System.out.println("  总启动线程数: " + threadBean.getTotalStartedThreadCount());
        
        // 重置峰值计数器
        threadBean.resetPeakThreadCount();
    }
    
    /**
     * 显示所有线程的详细信息
     */
    private static void showAllThreadDetails(ThreadMXBean threadBean) {
        long[] threadIds = threadBean.getAllThreadIds();
        ThreadInfo[] threadInfos = threadBean.getThreadInfo(threadIds);
        
        System.out.println("当前JVM进程中的所有线程 (共 " + threadInfos.length + " 个):");
        
        for (ThreadInfo info : threadInfos) {
            if (info != null) {
                System.out.println("  线程ID: " + info.getThreadId() + 
                    ", 名称: " + info.getThreadName() + 
                    ", 状态: " + info.getThreadState() +
                    ", 是否守护线程: " + threadBean.getThreadInfo(info.getThreadId()).getThreadName().contains("daemon"));
            }
        }
    }
    
    /**
     * 演示线程监控功能
     */
    private static void demonstrateThreadMonitoring(ThreadMXBean threadBean) {
        System.out.println("线程监控能力:");
        System.out.println("  支持线程竞争监控: " + threadBean.isThreadContentionMonitoringSupported());
        System.out.println("  支持线程CPU时间: " + threadBean.isThreadCpuTimeSupported());
        System.out.println("  支持当前线程CPU时间: " + threadBean.isCurrentThreadCpuTimeSupported());
        
        // 如果支持CPU时间监控
        if (threadBean.isCurrentThreadCpuTimeSupported()) {
            System.out.println("  当前线程CPU时间: " + threadBean.getCurrentThreadCpuTime() + " 纳秒");
            System.out.println("  当前线程用户时间: " + threadBean.getCurrentThreadUserTime() + " 纳秒");
        }
        
        // 死锁检测
        long[] deadlockedThreads = threadBean.findDeadlockedThreads();
        if (deadlockedThreads != null) {
            System.out.println("  检测到死锁线程: " + java.util.Arrays.toString(deadlockedThreads));
        } else {
            System.out.println("  未检测到死锁");
        }
        
        // 监视器死锁检测
        long[] monitorDeadlocked = threadBean.findMonitorDeadlockedThreads();
        if (monitorDeadlocked != null) {
            System.out.println("  检测到监视器死锁: " + java.util.Arrays.toString(monitorDeadlocked));
        } else {
            System.out.println("  未检测到监视器死锁");
        }
    }
    
    /**
     * 创建一个演示死锁的方法 (注释掉以避免实际死锁)
     */
    /*
    private static void createDeadlockDemo() {
        Object lock1 = new Object();
        Object lock2 = new Object();
        
        Thread t1 = new Thread(() -> {
            synchronized (lock1) {
                try { Thread.sleep(100); } catch (InterruptedException e) {}
                synchronized (lock2) {
                    System.out.println("Thread 1 acquired both locks");
                }
            }
        });
        
        Thread t2 = new Thread(() -> {
            synchronized (lock2) {
                try { Thread.sleep(100); } catch (InterruptedException e) {}
                synchronized (lock1) {
                    System.out.println("Thread 2 acquired both locks");
                }
            }
        });
        
        t1.start();
        t2.start();
    }
    */
}
