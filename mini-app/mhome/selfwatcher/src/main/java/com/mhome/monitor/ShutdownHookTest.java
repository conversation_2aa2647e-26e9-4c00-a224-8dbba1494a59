package com.mhome.monitor;

import java.io.IOException;
import java.lang.management.ManagementFactory;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;

/**
 * 测试关闭钩子在不同kill信号下的行为
 */
public class ShutdownHookTest {
    
    private static final String TEST_LOG_FILE = "shutdown_hook_test.log";
    
    public static void main(String[] args) {
        String pid = ManagementFactory.getRuntimeMXBean().getName().split("@")[0];
        
        System.out.println("=== 关闭钩子测试程序 ===");
        System.out.println("进程PID: " + pid);
        System.out.println("测试不同kill信号对关闭钩子的影响");
        System.out.println();
        
        // 清理之前的日志文件
        try {
            Files.deleteIfExists(Paths.get(TEST_LOG_FILE));
        } catch (IOException e) {
            System.err.println("清理日志文件失败: " + e.getMessage());
        }
        
        // 注册关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            String message = "关闭钩子被触发! 时间: " + new java.util.Date() + 
                           ", PID: " + pid + "\n";
            
            System.out.println(message);
            
            // 写入日志文件
            try {
                Files.write(Paths.get(TEST_LOG_FILE), message.getBytes(), 
                           StandardOpenOption.CREATE, StandardOpenOption.APPEND);
            } catch (IOException e) {
                System.err.println("写入日志失败: " + e.getMessage());
            }
        }));
        
        System.out.println("关闭钩子已注册，现在可以测试不同的kill信号:");
        System.out.println();
        System.out.println("测试命令:");
        System.out.println("1. 正常终止 (会触发钩子):");
        System.out.println("   kill " + pid);
        System.out.println("   kill -15 " + pid);
        System.out.println("   kill -TERM " + pid);
        System.out.println();
        System.out.println("2. 强制终止 (不会触发钩子):");
        System.out.println("   kill -9 " + pid);
        System.out.println("   kill -KILL " + pid);
        System.out.println();
        System.out.println("3. 其他信号:");
        System.out.println("   kill -1 " + pid + "  (SIGHUP)");
        System.out.println("   kill -2 " + pid + "  (SIGINT, 相当于Ctrl+C)");
        System.out.println();
        System.out.println("程序运行中... 请在另一个终端执行kill命令进行测试");
        System.out.println("如果关闭钩子被触发，会在控制台显示消息并写入 " + TEST_LOG_FILE + " 文件");
        System.out.println();
        
        // 定期输出心跳，证明程序还在运行
        int counter = 0;
        while (true) {
            try {
                Thread.sleep(5000);
                counter++;
                System.out.println("[" + counter + "] 程序运行中... " + new java.util.Date());
                
                // 每次心跳都检查日志文件是否存在
                if (Files.exists(Paths.get(TEST_LOG_FILE))) {
                    System.out.println("    注意: 发现日志文件，说明之前的关闭钩子被触发过");
                }
                
            } catch (InterruptedException e) {
                System.out.println("程序被中断");
                Thread.currentThread().interrupt();
                break;
            }
        }
    }
}
