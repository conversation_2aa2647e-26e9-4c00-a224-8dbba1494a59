package com.mhome.monitor;

import java.lang.instrument.Instrumentation;
import java.lang.management.ManagementFactory;

/**
 * Java Instrumentation API 演示
 * 展示 java.lang.instrument 包的核心功能
 */
public class InstrumentationDemo {
    
    private static Instrumentation instrumentation;
    
    /**
     * JVM Agent 入口点 - premain 方法
     * 在主程序启动前执行
     * 
     * 使用方法：
     * java -javaagent:agent.jar com.mhome.monitor.InstrumentationDemo
     */
    public static void premain(String agentArgs, Instrumentation inst) {
        System.out.println("=== Java Agent 启动 (premain) ===");
        System.out.println("Agent 参数: " + agentArgs);
        
        instrumentation = inst;
        
        // 展示 Instrumentation 的基本功能
        showInstrumentationCapabilities(inst);
        
        // 添加类转换器
        addClassTransformer(inst);
        
        System.out.println("Java Agent 初始化完成\n");
    }
    
    /**
     * JVM Agent 入口点 - agentmain 方法
     * 在程序运行时动态加载
     * 
     * 使用方法：
     * 通过 VirtualMachine.attach() 动态加载
     */
    public static void agentmain(String agentArgs, Instrumentation inst) {
        System.out.println("=== Java Agent 动态加载 (agentmain) ===");
        System.out.println("Agent 参数: " + agentArgs);
        
        instrumentation = inst;
        
        showInstrumentationCapabilities(inst);
        addClassTransformer(inst);
        
        System.out.println("动态 Java Agent 加载完成\n");
    }
    
    /**
     * 展示 Instrumentation 的能力
     */
    private static void showInstrumentationCapabilities(Instrumentation inst) {
        System.out.println("Instrumentation 功能检查:");
        System.out.println("  支持类重定义: " + inst.isRedefineClassesSupported());
        System.out.println("  支持类重转换: " + inst.isRetransformClassesSupported());
        System.out.println("  支持原生方法前缀: " + inst.isNativeMethodPrefixSupported());
        System.out.println("  已加载类数量: " + inst.getAllLoadedClasses().length);
        System.out.println("  初始化的类数量: " + getInitializedClassCount(inst));
    }
    
    /**
     * 添加类转换器
     */
    private static void addClassTransformer(Instrumentation inst) {
        // 添加一个简单的类转换器
        inst.addTransformer(new SimpleClassTransformer(), true);
        System.out.println("已添加类转换器");
    }
    
    /**
     * 获取已初始化的类数量
     */
    private static int getInitializedClassCount(Instrumentation inst) {
        Class<?>[] allClasses = inst.getAllLoadedClasses();
        int count = 0;
        for (Class<?> clazz : allClasses) {
            if (inst.isModifiableClass(clazz)) {
                count++;
            }
        }
        return count;
    }
    
    /**
     * 主程序 - 用于演示
     */
    public static void main(String[] args) {
        System.out.println("=== Instrumentation 演示程序 ===");
        
        if (instrumentation != null) {
            System.out.println("检测到 Instrumentation 实例");
            demonstrateInstrumentationFeatures();
        } else {
            System.out.println("未检测到 Instrumentation 实例");
            System.out.println("请使用 -javaagent 参数启动程序");
            System.out.println("示例: java -javaagent:agent.jar com.mhome.monitor.InstrumentationDemo");
        }
        
        // 演示一些基本操作
        demonstrateBasicOperations();
    }
    
    /**
     * 演示 Instrumentation 功能
     */
    private static void demonstrateInstrumentationFeatures() {
        System.out.println("\n=== Instrumentation 功能演示 ===");
        
        // 1. 获取所有已加载的类
        Class<?>[] loadedClasses = instrumentation.getAllLoadedClasses();
        System.out.println("1. 已加载类总数: " + loadedClasses.length);
        
        // 显示前10个类
        System.out.println("   前10个已加载的类:");
        for (int i = 0; i < Math.min(10, loadedClasses.length); i++) {
            System.out.println("     " + loadedClasses[i].getName());
        }
        
        // 2. 获取对象大小
        System.out.println("\n2. 对象大小测量:");
        String testString = "Hello, Instrumentation!";
        long stringSize = instrumentation.getObjectSize(testString);
        System.out.println("   字符串 \"" + testString + "\" 大小: " + stringSize + " 字节");
        
        int[] testArray = new int[100];
        long arraySize = instrumentation.getObjectSize(testArray);
        System.out.println("   int[100] 数组大小: " + arraySize + " 字节");
        
        // 3. 检查可修改的类
        System.out.println("\n3. 可修改类检查:");
        int modifiableCount = 0;
        for (Class<?> clazz : loadedClasses) {
            if (instrumentation.isModifiableClass(clazz)) {
                modifiableCount++;
            }
        }
        System.out.println("   可修改的类数量: " + modifiableCount + "/" + loadedClasses.length);
        
        // 4. 显示当前JVM信息
        System.out.println("\n4. JVM 信息:");
        System.out.println("   JVM 名称: " + ManagementFactory.getRuntimeMXBean().getVmName());
        System.out.println("   JVM 版本: " + ManagementFactory.getRuntimeMXBean().getVmVersion());
    }
    
    /**
     * 演示基本操作
     */
    private static void demonstrateBasicOperations() {
        System.out.println("\n=== 基本操作演示 ===");
        
        // 创建一些对象来演示
        TestClass testObj = new TestClass();
        testObj.doSomething();
        
        // 如果有 instrumentation，测量对象大小
        if (instrumentation != null) {
            long objSize = instrumentation.getObjectSize(testObj);
            System.out.println("TestClass 对象大小: " + objSize + " 字节");
        }
        
        System.out.println("演示完成");
    }
    
    /**
     * 测试类
     */
    static class TestClass {
        private String name = "TestClass";
        private int value = 42;
        
        public void doSomething() {
            System.out.println("TestClass.doSomething() 被调用");
            System.out.println("  name: " + name + ", value: " + value);
        }
    }
}
