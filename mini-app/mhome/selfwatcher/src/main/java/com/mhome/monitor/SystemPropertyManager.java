package com.mhome.monitor;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * 系统属性管理工具类
 * 提供便捷的系统属性设置、读取和管理功能
 */
public class SystemPropertyManager {
    
    // 属性缓存
    private static final Map<String, String> propertyCache = new HashMap<>();
    
    // 是否启用缓存
    private static boolean cacheEnabled = true;
    
    /**
     * 设置系统属性（条件设置，只在未设置时设置）
     */
    public static boolean setPropertyIfAbsent(String key, String value) {
        if (System.getProperty(key) == null) {
            System.setProperty(key, value);
            if (cacheEnabled) {
                propertyCache.put(key, value);
            }
            return true;
        }
        return false;
    }
    
    /**
     * 强制设置系统属性（覆盖已有值）
     */
    public static void setProperty(String key, String value) {
        System.setProperty(key, value);
        if (cacheEnabled) {
            propertyCache.put(key, value);
        }
    }
    
    /**
     * 获取系统属性（带缓存）
     */
    public static String getProperty(String key) {
        if (cacheEnabled && propertyCache.containsKey(key)) {
            return propertyCache.get(key);
        }
        
        String value = System.getProperty(key);
        if (cacheEnabled && value != null) {
            propertyCache.put(key, value);
        }
        
        return value;
    }
    
    /**
     * 获取系统属性（带默认值和缓存）
     */
    public static String getProperty(String key, String defaultValue) {
        String value = getProperty(key);
        return value != null ? value : defaultValue;
    }
    
    /**
     * 批量设置系统属性
     */
    public static void setProperties(Map<String, String> properties) {
        for (Map.Entry<String, String> entry : properties.entrySet()) {
            setProperty(entry.getKey(), entry.getValue());
        }
    }
    
    /**
     * 从属性文件加载系统属性
     */
    public static void loadFromFile(String filePath) throws IOException {
        Properties props = new Properties();
        try (InputStream in = new FileInputStream(filePath)) {
            props.load(in);
            
            for (String name : props.stringPropertyNames()) {
                setProperty(name, props.getProperty(name));
            }
        }
    }
    
    /**
     * 从类路径资源加载系统属性
     */
    public static void loadFromResource(String resourcePath) throws IOException {
        Properties props = new Properties();
        try (InputStream in = SystemPropertyManager.class.getClassLoader().getResourceAsStream(resourcePath)) {
            if (in == null) {
                throw new IOException("资源文件未找到: " + resourcePath);
            }
            
            props.load(in);
            
            for (String name : props.stringPropertyNames()) {
                setProperty(name, props.getProperty(name));
            }
        }
    }
    
    /**
     * 设置JMX相关属性（仅在未设置时）
     */
    public static void configureJMX(int port, boolean authenticate, boolean ssl) {
        setPropertyIfAbsent("com.sun.management.jmxremote", "true");
        setPropertyIfAbsent("com.sun.management.jmxremote.port", String.valueOf(port));
        setPropertyIfAbsent("com.sun.management.jmxremote.authenticate", String.valueOf(authenticate));
        setPropertyIfAbsent("com.sun.management.jmxremote.ssl", String.valueOf(ssl));
        
        if (!authenticate) {
            setPropertyIfAbsent("com.sun.management.jmxremote.access.file", "");
            setPropertyIfAbsent("com.sun.management.jmxremote.password.file", "");
        }
    }
    
    /**
     * 设置日志相关属性
     */
    public static void configureLogging(String configFile, String logLevel) {
        setPropertyIfAbsent("java.util.logging.config.file", configFile);
        if (logLevel != null) {
            setPropertyIfAbsent("java.util.logging.level", logLevel);
        }
    }
    
    /**
     * 设置网络代理属性
     */
    public static void configureProxy(String host, int port, String nonProxyHosts) {
        if (host != null && !host.isEmpty()) {
            setProperty("http.proxyHost", host);
            setProperty("http.proxyPort", String.valueOf(port));
            setProperty("https.proxyHost", host);
            setProperty("https.proxyPort", String.valueOf(port));
            
            if (nonProxyHosts != null) {
                setProperty("http.nonProxyHosts", nonProxyHosts);
                setProperty("https.nonProxyHosts", nonProxyHosts);
            }
        }
    }
    
    /**
     * 设置SSL相关属性
     */
    public static void configureSSL(String trustStore, String trustStorePassword, 
                                   String keyStore, String keyStorePassword) {
        if (trustStore != null) {
            setProperty("javax.net.ssl.trustStore", trustStore);
            if (trustStorePassword != null) {
                setProperty("javax.net.ssl.trustStorePassword", trustStorePassword);
            }
        }
        
        if (keyStore != null) {
            setProperty("javax.net.ssl.keyStore", keyStore);
            if (keyStorePassword != null) {
                setProperty("javax.net.ssl.keyStorePassword", keyStorePassword);
            }
        }
    }
    
    /**
     * 设置应用程序相关属性
     */
    public static void configureApplication(String appName, String version, String environment) {
        setPropertyIfAbsent("app.name", appName);
        setPropertyIfAbsent("app.version", version);
        setPropertyIfAbsent("app.environment", environment);
        setPropertyIfAbsent("app.startup.time", String.valueOf(System.currentTimeMillis()));
    }
    
    /**
     * 验证必需的属性是否已设置
     */
    public static boolean validateRequiredProperties(String... requiredProperties) {
        for (String prop : requiredProperties) {
            if (System.getProperty(prop) == null) {
                System.err.println("必需的系统属性未设置: " + prop);
                return false;
            }
        }
        return true;
    }
    
    /**
     * 显示所有自定义设置的属性
     */
    public static void displayCustomProperties() {
        System.out.println("=== 自定义系统属性 ===");
        
        String[] customPrefixes = {"app.", "custom.", "com.mhome."};
        Properties props = System.getProperties();
        
        for (String name : props.stringPropertyNames()) {
            for (String prefix : customPrefixes) {
                if (name.startsWith(prefix)) {
                    System.out.println(name + " = " + props.getProperty(name));
                    break;
                }
            }
        }
    }
    
    /**
     * 清除属性缓存
     */
    public static void clearCache() {
        propertyCache.clear();
    }
    
    /**
     * 启用或禁用属性缓存
     */
    public static void setCacheEnabled(boolean enabled) {
        cacheEnabled = enabled;
        if (!enabled) {
            clearCache();
        }
    }
    
    /**
     * 获取缓存状态
     */
    public static boolean isCacheEnabled() {
        return cacheEnabled;
    }
    
    /**
     * 获取缓存大小
     */
    public static int getCacheSize() {
        return propertyCache.size();
    }
    
    /**
     * 示例：应用程序启动时的属性配置
     */
    public static void configureForApplication() {
        // 设置应用程序基本信息
        configureApplication("SystemPropertyDemo", "1.0.0", "development");
        
        // 设置文件编码
        setPropertyIfAbsent("file.encoding", "UTF-8");
        
        // 设置时区
        setPropertyIfAbsent("user.timezone", "Asia/Shanghai");
        
        // 设置临时目录
        String tmpDir = System.getProperty("java.io.tmpdir") + "/myapp";
        setPropertyIfAbsent("app.temp.dir", tmpDir);
        
        // 设置日志级别
        setPropertyIfAbsent("java.util.logging.level", "INFO");
        
        System.out.println("应用程序系统属性配置完成");
    }
    
    /**
     * 主方法 - 演示用法
     */
    public static void main(String[] args) {
        System.out.println("=== SystemPropertyManager 演示 ===\n");
        
        // 配置应用程序属性
        configureForApplication();
        
        // 显示自定义属性
        displayCustomProperties();
        
        // 验证必需属性
        boolean valid = validateRequiredProperties("app.name", "app.version");
        System.out.println("\n必需属性验证: " + (valid ? "通过" : "失败"));
        
        // 缓存状态
        System.out.println("\n缓存状态: " + (isCacheEnabled() ? "启用" : "禁用"));
        System.out.println("缓存大小: " + getCacheSize());
    }
}
