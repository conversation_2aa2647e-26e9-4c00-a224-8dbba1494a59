package com.mhome.monitor;

import java.util.*;
import java.util.regex.Pattern;

/**
 * Java命令行参数解析演示
 * 展示从简单到复杂的各种命令行参数解析方式
 */
public class CommandLineParsingDemo {
    
    public static void main(String[] args) {
        System.out.println("=== Java命令行参数解析演示 ===\n");
        
        // 模拟命令行参数
        String[] testArgs = {
            "--host", "localhost",
            "--port", "8080",
            "--verbose",
            "--config", "/path/to/config.properties",
            "--threads", "4",
            "--enable-ssl",
            "file1.txt", "file2.txt"
        };
        
        System.out.println("测试参数: " + Arrays.toString(testArgs));
        System.out.println();
        
        // 1. 简单手工解析
        demonstrateSimpleParsing(testArgs);
        
        // 2. 优雅的自定义解析器
        demonstrateElegantParsing(testArgs);
        
        // 3. 使用Apache Commons CLI（如果可用）
        demonstrateApacheCommonsCLI(testArgs);
        
        // 4. 现代Java风格的解析器
        demonstrateModernParsing(testArgs);
    }
    
    /**
     * 1. 简单手工解析（不推荐，但有时必要）
     */
    private static void demonstrateSimpleParsing(String[] args) {
        System.out.println("=== 1. 简单手工解析 ===");
        
        String host = "localhost";
        int port = 8080;
        boolean verbose = false;
        String config = null;
        List<String> files = new ArrayList<>();
        
        for (int i = 0; i < args.length; i++) {
            switch (args[i]) {
                case "--host":
                    if (i + 1 < args.length) {
                        host = args[++i];
                    }
                    break;
                case "--port":
                    if (i + 1 < args.length) {
                        try {
                            port = Integer.parseInt(args[++i]);
                        } catch (NumberFormatException e) {
                            System.err.println("无效的端口号: " + args[i]);
                        }
                    }
                    break;
                case "--verbose":
                    verbose = true;
                    break;
                case "--config":
                    if (i + 1 < args.length) {
                        config = args[++i];
                    }
                    break;
                default:
                    if (!args[i].startsWith("--")) {
                        files.add(args[i]);
                    }
                    break;
            }
        }
        
        System.out.println("解析结果:");
        System.out.println("  host: " + host);
        System.out.println("  port: " + port);
        System.out.println("  verbose: " + verbose);
        System.out.println("  config: " + config);
        System.out.println("  files: " + files);
        System.out.println();
    }
    
    /**
     * 2. 优雅的自定义解析器
     */
    private static void demonstrateElegantParsing(String[] args) {
        System.out.println("=== 2. 优雅的自定义解析器 ===");
        
        CommandLineParser parser = new CommandLineParser();
        
        try {
            ParsedArgs parsed = parser
                .option("--host", "-h").withValue().defaultValue("localhost").description("服务器主机")
                .option("--port", "-p").withValue().type(Integer.class).defaultValue(8080).description("服务器端口")
                .option("--verbose", "-v").flag().description("启用详细输出")
                .option("--config", "-c").withValue().description("配置文件路径")
                .option("--threads", "-t").withValue().type(Integer.class).defaultValue(1).description("线程数")
                .option("--enable-ssl").flag().description("启用SSL")
                .parse(args);
            
            System.out.println("解析结果:");
            System.out.println("  host: " + parsed.getString("host"));
            System.out.println("  port: " + parsed.getInt("port"));
            System.out.println("  verbose: " + parsed.getBoolean("verbose"));
            System.out.println("  config: " + parsed.getString("config"));
            System.out.println("  threads: " + parsed.getInt("threads"));
            System.out.println("  enable-ssl: " + parsed.getBoolean("enable-ssl"));
            System.out.println("  remaining: " + parsed.getRemaining());
            
        } catch (Exception e) {
            System.err.println("解析错误: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 3. Apache Commons CLI风格（模拟实现）
     */
    private static void demonstrateApacheCommonsCLI(String[] args) {
        System.out.println("=== 3. Apache Commons CLI风格 ===");
        
        // 这里模拟Apache Commons CLI的使用方式
        CLIOptions options = new CLIOptions()
            .addOption("h", "host", true, "服务器主机")
            .addOption("p", "port", true, "服务器端口")
            .addOption("v", "verbose", false, "启用详细输出")
            .addOption("c", "config", true, "配置文件路径");
        
        try {
            CLIResult result = options.parse(args);
            
            System.out.println("解析结果:");
            System.out.println("  host: " + result.getOptionValue("host", "localhost"));
            System.out.println("  port: " + result.getOptionValue("port", "8080"));
            System.out.println("  verbose: " + result.hasOption("verbose"));
            System.out.println("  config: " + result.getOptionValue("config"));
            System.out.println("  args: " + result.getArgs());
            
        } catch (Exception e) {
            System.err.println("解析错误: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 4. 现代Java风格的解析器
     */
    private static void demonstrateModernParsing(String[] args) {
        System.out.println("=== 4. 现代Java风格解析器 ===");
        
        ModernCommandLineParser parser = new ModernCommandLineParser();
        
        AppConfig config = parser
            .define("--host", String.class, "localhost", "服务器主机")
            .define("--port", Integer.class, 8080, "服务器端口")
            .define("--verbose", Boolean.class, false, "启用详细输出")
            .define("--config", String.class, null, "配置文件路径")
            .define("--threads", Integer.class, 1, "线程数")
            .parseInto(AppConfig.class, args);
        
        System.out.println("解析结果:");
        System.out.println("  " + config);
        System.out.println();
    }
    
    // ========== 自定义解析器实现 ==========
    
    /**
     * 优雅的命令行解析器
     */
    static class CommandLineParser {
        private final Map<String, OptionDef> options = new HashMap<>();
        private final Map<String, String> aliases = new HashMap<>();
        
        public CommandLineParser option(String name, String alias) {
            OptionDef def = new OptionDef(name);
            options.put(name, def);
            if (alias != null) {
                aliases.put(alias, name);
            }
            return this;
        }
        
        public CommandLineParser option(String name) {
            return option(name, null);
        }
        
        public CommandLineParser withValue() {
            // 最后添加的选项
            String lastOption = options.keySet().stream()
                .reduce((first, second) -> second).orElse(null);
            if (lastOption != null) {
                options.get(lastOption).hasValue = true;
            }
            return this;
        }
        
        public CommandLineParser flag() {
            String lastOption = options.keySet().stream()
                .reduce((first, second) -> second).orElse(null);
            if (lastOption != null) {
                options.get(lastOption).isFlag = true;
            }
            return this;
        }
        
        public CommandLineParser type(Class<?> type) {
            String lastOption = options.keySet().stream()
                .reduce((first, second) -> second).orElse(null);
            if (lastOption != null) {
                options.get(lastOption).type = type;
            }
            return this;
        }
        
        public CommandLineParser defaultValue(Object value) {
            String lastOption = options.keySet().stream()
                .reduce((first, second) -> second).orElse(null);
            if (lastOption != null) {
                options.get(lastOption).defaultValue = value;
            }
            return this;
        }
        
        public CommandLineParser description(String desc) {
            String lastOption = options.keySet().stream()
                .reduce((first, second) -> second).orElse(null);
            if (lastOption != null) {
                options.get(lastOption).description = desc;
            }
            return this;
        }
        
        public ParsedArgs parse(String[] args) {
            Map<String, Object> values = new HashMap<>();
            List<String> remaining = new ArrayList<>();
            
            // 设置默认值
            for (Map.Entry<String, OptionDef> entry : options.entrySet()) {
                String key = entry.getKey().replaceFirst("--", "");
                OptionDef def = entry.getValue();
                if (def.defaultValue != null) {
                    values.put(key, def.defaultValue);
                } else if (def.isFlag) {
                    values.put(key, false);
                }
            }
            
            for (int i = 0; i < args.length; i++) {
                String arg = args[i];
                String optionName = aliases.getOrDefault(arg, arg);
                OptionDef def = options.get(optionName);
                
                if (def != null) {
                    String key = optionName.replaceFirst("--", "");
                    
                    if (def.isFlag) {
                        values.put(key, true);
                    } else if (def.hasValue && i + 1 < args.length) {
                        String value = args[++i];
                        values.put(key, convertValue(value, def.type));
                    }
                } else if (!arg.startsWith("-")) {
                    remaining.add(arg);
                }
            }
            
            return new ParsedArgs(values, remaining);
        }
        
        private Object convertValue(String value, Class<?> type) {
            if (type == Integer.class) {
                return Integer.parseInt(value);
            } else if (type == Boolean.class) {
                return Boolean.parseBoolean(value);
            } else if (type == Double.class) {
                return Double.parseDouble(value);
            }
            return value;
        }
    }
    
    static class OptionDef {
        String name;
        boolean hasValue = false;
        boolean isFlag = false;
        Class<?> type = String.class;
        Object defaultValue;
        String description;
        
        OptionDef(String name) {
            this.name = name;
        }
    }
    
    static class ParsedArgs {
        private final Map<String, Object> values;
        private final List<String> remaining;
        
        ParsedArgs(Map<String, Object> values, List<String> remaining) {
            this.values = values;
            this.remaining = remaining;
        }
        
        public String getString(String key) {
            Object value = values.get(key);
            return value != null ? value.toString() : null;
        }
        
        public int getInt(String key) {
            Object value = values.get(key);
            return value instanceof Integer ? (Integer) value : 0;
        }
        
        public boolean getBoolean(String key) {
            Object value = values.get(key);
            return value instanceof Boolean ? (Boolean) value : false;
        }
        
        public List<String> getRemaining() {
            return remaining;
        }
    }
    
    // ========== Apache Commons CLI风格实现 ==========
    
    static class CLIOptions {
        private final Map<String, CLIOption> options = new HashMap<>();
        
        public CLIOptions addOption(String shortName, String longName, boolean hasArg, String description) {
            CLIOption option = new CLIOption(shortName, longName, hasArg, description);
            options.put(shortName, option);
            options.put(longName, option);
            return this;
        }
        
        public CLIResult parse(String[] args) {
            Map<String, String> values = new HashMap<>();
            List<String> remaining = new ArrayList<>();
            
            for (int i = 0; i < args.length; i++) {
                String arg = args[i];
                
                if (arg.startsWith("--")) {
                    String optName = arg.substring(2);
                    CLIOption option = options.get(optName);
                    if (option != null) {
                        if (option.hasArg && i + 1 < args.length) {
                            values.put(optName, args[++i]);
                        } else {
                            values.put(optName, "true");
                        }
                    }
                } else if (arg.startsWith("-")) {
                    String optName = arg.substring(1);
                    CLIOption option = options.get(optName);
                    if (option != null) {
                        if (option.hasArg && i + 1 < args.length) {
                            values.put(option.longName, args[++i]);
                        } else {
                            values.put(option.longName, "true");
                        }
                    }
                } else {
                    remaining.add(arg);
                }
            }
            
            return new CLIResult(values, remaining);
        }
    }
    
    static class CLIOption {
        String shortName;
        String longName;
        boolean hasArg;
        String description;
        
        CLIOption(String shortName, String longName, boolean hasArg, String description) {
            this.shortName = shortName;
            this.longName = longName;
            this.hasArg = hasArg;
            this.description = description;
        }
    }
    
    static class CLIResult {
        private final Map<String, String> values;
        private final List<String> args;
        
        CLIResult(Map<String, String> values, List<String> args) {
            this.values = values;
            this.args = args;
        }
        
        public String getOptionValue(String name) {
            return values.get(name);
        }
        
        public String getOptionValue(String name, String defaultValue) {
            return values.getOrDefault(name, defaultValue);
        }
        
        public boolean hasOption(String name) {
            return values.containsKey(name);
        }
        
        public List<String> getArgs() {
            return args;
        }
    }
    
    // ========== 现代Java风格解析器 ==========
    
    static class ModernCommandLineParser {
        private final Map<String, OptionConfig> configs = new HashMap<>();
        
        public <T> ModernCommandLineParser define(String name, Class<T> type, T defaultValue, String description) {
            configs.put(name, new OptionConfig(type, defaultValue, description));
            return this;
        }
        
        public <T> T parseInto(Class<T> targetClass, String[] args) {
            try {
                T instance = targetClass.getDeclaredConstructor().newInstance();
                
                // 这里应该使用反射设置字段值，简化演示
                if (targetClass == AppConfig.class) {
                    AppConfig config = (AppConfig) instance;
                    
                    for (int i = 0; i < args.length; i++) {
                        String arg = args[i];
                        OptionConfig optConfig = configs.get(arg);
                        
                        if (optConfig != null) {
                            if (optConfig.type == Boolean.class) {
                                setConfigValue(config, arg, true);
                            } else if (i + 1 < args.length) {
                                String value = args[++i];
                                setConfigValue(config, arg, convertValue(value, optConfig.type));
                            }
                        }
                    }
                    
                    return (T) config;
                }
                
                return instance;
            } catch (Exception e) {
                throw new RuntimeException("解析失败", e);
            }
        }
        
        private void setConfigValue(AppConfig config, String option, Object value) {
            switch (option) {
                case "--host":
                    config.host = (String) value;
                    break;
                case "--port":
                    config.port = (Integer) value;
                    break;
                case "--verbose":
                    config.verbose = (Boolean) value;
                    break;
                case "--config":
                    config.configFile = (String) value;
                    break;
                case "--threads":
                    config.threads = (Integer) value;
                    break;
            }
        }
        
        private Object convertValue(String value, Class<?> type) {
            if (type == Integer.class) {
                return Integer.parseInt(value);
            } else if (type == Boolean.class) {
                return Boolean.parseBoolean(value);
            }
            return value;
        }
    }
    
    static class OptionConfig {
        Class<?> type;
        Object defaultValue;
        String description;
        
        OptionConfig(Class<?> type, Object defaultValue, String description) {
            this.type = type;
            this.defaultValue = defaultValue;
            this.description = description;
        }
    }
    
    static class AppConfig {
        String host = "localhost";
        int port = 8080;
        boolean verbose = false;
        String configFile;
        int threads = 1;
        
        @Override
        public String toString() {
            return String.format("AppConfig{host='%s', port=%d, verbose=%s, configFile='%s', threads=%d}", 
                               host, port, verbose, configFile, threads);
        }
    }
}
