package com.mhome.monitor;

import java.util.concurrent.BlockingDeque;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 基于 BlockingDeque 的任务调度器示例
 * 演示如何使用双端阻塞队列实现优先级任务调度
 */
public class TaskSchedulerExample {
    
    /**
     * 任务类
     */
    static class Task {
        private static final AtomicInteger idGenerator = new AtomicInteger(0);
        
        private final int id;
        private final String name;
        private final TaskPriority priority;
        private final Runnable action;
        private final long createTime;
        
        public Task(String name, TaskPriority priority, Runnable action) {
            this.id = idGenerator.incrementAndGet();
            this.name = name;
            this.priority = priority;
            this.action = action;
            this.createTime = System.currentTimeMillis();
        }
        
        public void execute() {
            System.out.println("开始执行任务: " + this);
            long startTime = System.currentTimeMillis();
            
            try {
                action.run();
            } catch (Exception e) {
                System.err.println("任务执行失败: " + this + ", 错误: " + e.getMessage());
            }
            
            long duration = System.currentTimeMillis() - startTime;
            System.out.println("任务执行完成: " + this + ", 耗时: " + duration + "ms");
        }
        
        public TaskPriority getPriority() { return priority; }
        public int getId() { return id; }
        public String getName() { return name; }
        public long getCreateTime() { return createTime; }
        
        @Override
        public String toString() {
            return String.format("Task{id=%d, name='%s', priority=%s}", id, name, priority);
        }
    }
    
    /**
     * 任务优先级枚举
     */
    enum TaskPriority {
        URGENT(1),    // 紧急任务
        HIGH(2),      // 高优先级
        NORMAL(3),    // 普通优先级
        LOW(4);       // 低优先级
        
        private final int level;
        
        TaskPriority(int level) {
            this.level = level;
        }
        
        public int getLevel() { return level; }
    }
    
    /**
     * 任务调度器
     */
    static class TaskScheduler {
        private final BlockingDeque<Task> taskQueue;
        private final Thread[] workers;
        private volatile boolean running = true;
        private final AtomicInteger completedTasks = new AtomicInteger(0);
        
        public TaskScheduler(int capacity, int workerCount) {
            this.taskQueue = new LinkedBlockingDeque<>(capacity);
            this.workers = new Thread[workerCount];
            
            // 创建工作线程
            for (int i = 0; i < workerCount; i++) {
                workers[i] = new Thread(this::workerLoop, "TaskWorker-" + (i + 1));
                workers[i].start();
            }
            
            System.out.println("任务调度器启动，容量: " + capacity + ", 工作线程: " + workerCount);
        }
        
        /**
         * 提交任务
         */
        public boolean submitTask(Task task) {
            if (!running) {
                System.out.println("调度器已关闭，拒绝任务: " + task);
                return false;
            }
            
            try {
                // 根据优先级决定插入位置
                if (task.getPriority() == TaskPriority.URGENT) {
                    // 紧急任务插入到队列头部
                    taskQueue.putFirst(task);
                    System.out.println("紧急任务已插入队列头部: " + task);
                } else {
                    // 其他任务插入到队列尾部
                    taskQueue.putLast(task);
                    System.out.println("任务已插入队列尾部: " + task);
                }
                return true;
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                System.err.println("提交任务被中断: " + task);
                return false;
            }
        }
        
        /**
         * 尝试提交任务（非阻塞）
         */
        public boolean trySubmitTask(Task task, long timeout, TimeUnit unit) {
            if (!running) {
                return false;
            }
            
            try {
                boolean success;
                if (task.getPriority() == TaskPriority.URGENT) {
                    success = taskQueue.offerFirst(task, timeout, unit);
                } else {
                    success = taskQueue.offerLast(task, timeout, unit);
                }
                
                if (success) {
                    System.out.println("任务提交成功: " + task);
                } else {
                    System.out.println("任务提交超时: " + task);
                }
                
                return success;
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }
        
        /**
         * 工作线程循环
         */
        private void workerLoop() {
            while (running || !taskQueue.isEmpty()) {
                try {
                    // 从队列头部取任务（优先处理紧急任务）
                    Task task = taskQueue.pollFirst(1, TimeUnit.SECONDS);
                    
                    if (task != null) {
                        task.execute();
                        completedTasks.incrementAndGet();
                    }
                    
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
            
            System.out.println(Thread.currentThread().getName() + " 工作线程退出");
        }
        
        /**
         * 获取队列状态
         */
        public void printStatus() {
            System.out.println("=== 调度器状态 ===");
            System.out.println("队列大小: " + taskQueue.size());
            System.out.println("剩余容量: " + taskQueue.remainingCapacity());
            System.out.println("已完成任务: " + completedTasks.get());
            System.out.println("运行状态: " + (running ? "运行中" : "已停止"));
        }
        
        /**
         * 关闭调度器
         */
        public void shutdown() {
            System.out.println("开始关闭任务调度器...");
            running = false;
            
            // 等待所有工作线程完成
            for (Thread worker : workers) {
                try {
                    worker.join(5000); // 最多等待5秒
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
            
            System.out.println("任务调度器已关闭，最终完成任务数: " + completedTasks.get());
        }
    }
    
    /**
     * 主方法 - 演示任务调度器
     */
    public static void main(String[] args) {
        System.out.println("=== BlockingDeque 任务调度器演示 ===\n");
        
        // 创建任务调度器
        TaskScheduler scheduler = new TaskScheduler(10, 3);
        
        try {
            // 提交各种优先级的任务
            submitTestTasks(scheduler);
            
            // 等待一段时间让任务执行
            Thread.sleep(3000);
            
            // 查看状态
            scheduler.printStatus();
            
            // 提交紧急任务测试插队
            System.out.println("\n=== 提交紧急任务测试 ===");
            scheduler.submitTask(new Task("紧急修复", TaskPriority.URGENT, () -> {
                try { Thread.sleep(500); } catch (InterruptedException e) {}
            }));
            
            // 再等待一段时间
            Thread.sleep(2000);
            
            // 最终状态
            scheduler.printStatus();
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            // 关闭调度器
            scheduler.shutdown();
        }
    }
    
    /**
     * 提交测试任务
     */
    private static void submitTestTasks(TaskScheduler scheduler) {
        System.out.println("=== 提交测试任务 ===");
        
        // 普通任务
        scheduler.submitTask(new Task("数据备份", TaskPriority.NORMAL, () -> {
            try { Thread.sleep(1000); } catch (InterruptedException e) {}
        }));
        
        scheduler.submitTask(new Task("日志清理", TaskPriority.LOW, () -> {
            try { Thread.sleep(800); } catch (InterruptedException e) {}
        }));
        
        scheduler.submitTask(new Task("报表生成", TaskPriority.HIGH, () -> {
            try { Thread.sleep(1200); } catch (InterruptedException e) {}
        }));
        
        scheduler.submitTask(new Task("系统监控", TaskPriority.NORMAL, () -> {
            try { Thread.sleep(600); } catch (InterruptedException e) {}
        }));
        
        scheduler.submitTask(new Task("缓存预热", TaskPriority.LOW, () -> {
            try { Thread.sleep(900); } catch (InterruptedException e) {}
        }));
        
        // 高优先级任务
        scheduler.submitTask(new Task("安全扫描", TaskPriority.HIGH, () -> {
            try { Thread.sleep(700); } catch (InterruptedException e) {}
        }));
        
        System.out.println("所有测试任务已提交\n");
    }
}
