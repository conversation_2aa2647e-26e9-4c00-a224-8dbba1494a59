package com.mhome.monitor;

import java.lang.instrument.ClassFileTransformer;
import java.lang.instrument.IllegalClassFormatException;
import java.lang.instrument.Instrumentation;
import java.security.ProtectionDomain;

/**
 * 高级 Instrumentation 示例
 * 展示更复杂的字节码操作和监控功能
 */
public class AdvancedInstrumentationExample {
    
    private static Instrumentation instrumentation;
    
    public static void premain(String agentArgs, Instrumentation inst) {
        System.out.println("=== 高级 Instrumentation Agent 启动 ===");
        instrumentation = inst;
        
        // 添加方法执行时间监控转换器
        inst.addTransformer(new MethodTimingTransformer(), true);
        
        // 添加内存使用监控转换器
        inst.addTransformer(new MemoryMonitorTransformer(), true);
        
        System.out.println("高级监控 Agent 初始化完成");
    }
    
    /**
     * 方法执行时间监控转换器
     */
    static class MethodTimingTransformer implements ClassFileTransformer {
        
        @Override
        public byte[] transform(ClassLoader loader, String className, Class<?> classBeingRedefined,
                              ProtectionDomain protectionDomain, byte[] classfileBuffer)
                throws IllegalClassFormatException {
            
            // 只监控特定包下的类
            if (className != null && className.startsWith("com/mhome/monitor") && 
                !className.contains("Transformer") && !className.contains("Agent")) {
                
                System.out.println("添加方法执行时间监控: " + className);
                
                // 这里应该使用ASM或其他字节码操作库来修改字节码
                // 为了简化，我们只是记录信息
                return enhanceWithTiming(classfileBuffer, className);
            }
            
            return null;
        }
        
        private byte[] enhanceWithTiming(byte[] originalBytes, String className) {
            // 实际项目中，这里会使用ASM等库来修改字节码
            // 添加方法执行前后的时间记录代码
            System.out.println("  -> 为类 " + className + " 添加执行时间监控");
            return null; // 简化示例，不实际修改字节码
        }
    }
    
    /**
     * 内存使用监控转换器
     */
    static class MemoryMonitorTransformer implements ClassFileTransformer {
        
        @Override
        public byte[] transform(ClassLoader loader, String className, Class<?> classBeingRedefined,
                              ProtectionDomain protectionDomain, byte[] classfileBuffer)
                throws IllegalClassFormatException {
            
            if (className != null && className.startsWith("com/mhome/monitor") && 
                !className.contains("Transformer") && !className.contains("Agent")) {
                
                System.out.println("添加内存使用监控: " + className);
                return enhanceWithMemoryMonitoring(classfileBuffer, className);
            }
            
            return null;
        }
        
        private byte[] enhanceWithMemoryMonitoring(byte[] originalBytes, String className) {
            System.out.println("  -> 为类 " + className + " 添加内存使用监控");
            return null; // 简化示例
        }
    }
    
    /**
     * 运行时动态修改类的示例
     */
    public static void dynamicClassModification() {
        if (instrumentation == null) {
            System.out.println("Instrumentation 不可用");
            return;
        }
        
        System.out.println("\n=== 动态类修改示例 ===");
        
        try {
            // 获取目标类
            Class<?> targetClass = AdvancedInstrumentationExample.class;
            
            if (instrumentation.isModifiableClass(targetClass)) {
                System.out.println("类 " + targetClass.getName() + " 可以被修改");
                
                // 这里可以重新定义类
                // instrumentation.redefineClasses(new ClassDefinition(targetClass, newClassBytes));
                
            } else {
                System.out.println("类 " + targetClass.getName() + " 不能被修改");
            }
            
        } catch (Exception e) {
            System.err.println("动态修改类失败: " + e.getMessage());
        }
    }
    
    /**
     * 内存分析示例
     */
    public static void memoryAnalysis() {
        if (instrumentation == null) {
            System.out.println("Instrumentation 不可用");
            return;
        }
        
        System.out.println("\n=== 内存分析示例 ===");
        
        // 创建不同类型的对象并测量大小
        Object[] testObjects = {
            new String("测试字符串"),
            new int[1000],
            new double[500],
            new java.util.ArrayList<>(),
            new java.util.HashMap<>(),
            new Thread()
        };
        
        for (Object obj : testObjects) {
            long size = instrumentation.getObjectSize(obj);
            System.out.println(obj.getClass().getSimpleName() + " 对象大小: " + size + " 字节");
        }
    }
    
    /**
     * 类加载统计
     */
    public static void classLoadingStatistics() {
        if (instrumentation == null) {
            System.out.println("Instrumentation 不可用");
            return;
        }
        
        System.out.println("\n=== 类加载统计 ===");
        
        Class<?>[] allClasses = instrumentation.getAllLoadedClasses();
        
        // 按包名分组统计
        java.util.Map<String, Integer> packageStats = new java.util.HashMap<>();
        
        for (Class<?> clazz : allClasses) {
            String packageName = clazz.getPackage() != null ? 
                clazz.getPackage().getName() : "<default>";
            
            // 只统计前两级包名
            String[] parts = packageName.split("\\.");
            String topLevelPackage = parts.length > 0 ? parts[0] : packageName;
            if (parts.length > 1) {
                topLevelPackage += "." + parts[1];
            }
            
            packageStats.put(topLevelPackage, packageStats.getOrDefault(topLevelPackage, 0) + 1);
        }
        
        System.out.println("总共加载了 " + allClasses.length + " 个类");
        System.out.println("按包名统计 (前10个):");
        
        packageStats.entrySet().stream()
            .sorted((e1, e2) -> e2.getValue().compareTo(e1.getValue()))
            .limit(10)
            .forEach(entry -> 
                System.out.println("  " + entry.getKey() + ": " + entry.getValue() + " 个类"));
    }
    
    /**
     * 主方法 - 演示各种功能
     */
    public static void main(String[] args) {
        System.out.println("=== 高级 Instrumentation 演示 ===");
        
        if (instrumentation != null) {
            memoryAnalysis();
            classLoadingStatistics();
            dynamicClassModification();
        } else {
            System.out.println("请使用 -javaagent 参数启动程序");
        }
    }
}
