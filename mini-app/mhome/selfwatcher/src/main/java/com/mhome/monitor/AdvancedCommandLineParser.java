package com.mhome.monitor;

import java.util.*;

/**
 * 高级命令行参数解析器
 * 支持位置参数、短选项、长选项、选项值、标志等各种情况
 */
public class AdvancedCommandLineParser {
    
    public static void main(String[] args) {
        System.out.println("=== 高级命令行参数解析演示 ===\n");
        
        // 测试各种复杂的命令行参数组合
        String[] testCases = {
            // 基本用法
            "--host localhost --port 8080 --verbose file1.txt file2.txt",
            
            // 短选项
            "-h localhost -p 8080 -v file1.txt file2.txt",
            
            // 组合短选项
            "-vd -h localhost -p 8080 file1.txt file2.txt",
            
            // 等号格式
            "--host=localhost --port=8080 --config=/path/to/config file1.txt",
            
            // 混合格式
            "-v --host=localhost -p 8080 --debug file1.txt file2.txt",
            
            // 使用 -- 分隔符
            "--host localhost -v -- --not-an-option file1.txt -file2.txt",
            
            // 只有位置参数
            "input.txt output.txt backup.txt",
            
            // 复杂组合
            "-vd --host=************* --port 9000 --threads=4 -- file1.txt --file2.txt -file3.txt"
        };
        
        for (int i = 0; i < testCases.length; i++) {
            System.out.println("=== 测试用例 " + (i + 1) + " ===");
            System.out.println("输入: " + testCases[i]);
            
            String[] testArgs = testCases[i].split("\\s+");
            demonstrateAdvancedParsing(testArgs);
            System.out.println();
        }
    }
    
    /**
     * 演示高级解析功能
     */
    private static void demonstrateAdvancedParsing(String[] args) {
        try {
            AdvancedArgs result = AdvancedArgsParser.create()
                .option("--host", "-h").withValue().defaultValue("localhost").description("服务器主机")
                .option("--port", "-p").withValue().asInt().defaultValue(8080).description("服务器端口")
                .option("--verbose", "-v").flag().description("启用详细输出")
                .option("--debug", "-d").flag().description("启用调试模式")
                .option("--config", "-c").withValue().description("配置文件路径")
                .option("--threads", "-t").withValue().asInt().defaultValue(1).description("线程数")
                .option("--timeout").withValue().asInt().defaultValue(30).description("超时时间(秒)")
                .parse(args);
            
            System.out.println("解析结果:");
            System.out.println("  host: " + result.getString("host"));
            System.out.println("  port: " + result.getInt("port"));
            System.out.println("  verbose: " + result.getBoolean("verbose"));
            System.out.println("  debug: " + result.getBoolean("debug"));
            System.out.println("  config: " + result.getString("config"));
            System.out.println("  threads: " + result.getInt("threads"));
            System.out.println("  timeout: " + result.getInt("timeout"));
            System.out.println("  位置参数: " + result.getPositionalArgs());
            System.out.println("  参数数量: " + result.getPositionalArgCount());
            
        } catch (Exception e) {
            System.err.println("解析错误: " + e.getMessage());
        }
    }
    
    /**
     * 高级命令行参数解析器
     */
    static class AdvancedArgsParser {
        private final Map<String, OptionDefinition> options = new HashMap<>();
        private final Map<String, String> aliases = new HashMap<>();
        
        public static AdvancedArgsParser create() {
            return new AdvancedArgsParser();
        }
        
        public OptionBuilder option(String longName, String shortName) {
            OptionDefinition def = new OptionDefinition(longName, shortName);
            options.put(longName, def);
            if (shortName != null) {
                aliases.put(shortName, longName);
                options.put(shortName, def);
            }
            return new OptionBuilder(this, def);
        }
        
        public OptionBuilder option(String longName) {
            return option(longName, null);
        }
        
        public AdvancedArgs parse(String[] args) throws ParseException {
            Map<String, Object> values = new HashMap<>();
            List<String> positionalArgs = new ArrayList<>();
            boolean endOfOptions = false;
            
            // 设置默认值
            for (OptionDefinition def : options.values()) {
                if (def.defaultValue != null) {
                    String key = def.longName.replaceFirst("--", "");
                    values.put(key, def.defaultValue);
                } else if (def.isFlag) {
                    String key = def.longName.replaceFirst("--", "");
                    values.put(key, false);
                }
            }
            
            for (int i = 0; i < args.length; i++) {
                String arg = args[i];
                
                // 处理 -- 分隔符
                if ("--".equals(arg)) {
                    endOfOptions = true;
                    continue;
                }
                
                if (!endOfOptions && arg.startsWith("--")) {
                    // 长选项
                    i = parseLongOption(args, i, values);
                } else if (!endOfOptions && arg.startsWith("-") && arg.length() > 1) {
                    // 短选项
                    i = parseShortOptions(args, i, values);
                } else {
                    // 位置参数
                    positionalArgs.add(arg);
                }
            }
            
            return new AdvancedArgs(values, positionalArgs);
        }
        
        private int parseLongOption(String[] args, int index, Map<String, Object> values) throws ParseException {
            String arg = args[index];
            String optionName;
            String optionValue = null;
            
            // 处理 --option=value 格式
            if (arg.contains("=")) {
                String[] parts = arg.split("=", 2);
                optionName = parts[0];
                optionValue = parts[1];
            } else {
                optionName = arg;
            }
            
            OptionDefinition def = options.get(optionName);
            if (def == null) {
                throw new ParseException("未知选项: " + optionName);
            }
            
            String key = def.longName.replaceFirst("--", "");
            
            if (def.isFlag) {
                values.put(key, true);
            } else if (def.hasValue) {
                if (optionValue != null) {
                    // 使用 = 后的值
                    values.put(key, convertValue(optionValue, def.type));
                } else if (index + 1 < args.length && !args[index + 1].startsWith("-")) {
                    // 使用下一个参数作为值
                    values.put(key, convertValue(args[index + 1], def.type));
                    return index + 1;
                } else {
                    throw new ParseException("选项 " + optionName + " 需要一个值");
                }
            }
            
            return index;
        }
        
        private int parseShortOptions(String[] args, int index, Map<String, Object> values) throws ParseException {
            String arg = args[index];
            String shortOpts = arg.substring(1);
            
            for (int j = 0; j < shortOpts.length(); j++) {
                String shortOpt = "-" + shortOpts.charAt(j);
                String longName = aliases.get(shortOpt);
                
                if (longName == null) {
                    throw new ParseException("未知短选项: " + shortOpt);
                }
                
                OptionDefinition def = options.get(longName);
                String key = def.longName.replaceFirst("--", "");
                
                if (def.isFlag) {
                    values.put(key, true);
                } else if (def.hasValue) {
                    if (j == shortOpts.length() - 1) {
                        // 最后一个短选项，可以有值
                        if (index + 1 < args.length && !args[index + 1].startsWith("-")) {
                            values.put(key, convertValue(args[index + 1], def.type));
                            return index + 1;
                        } else {
                            throw new ParseException("选项 " + shortOpt + " 需要一个值");
                        }
                    } else {
                        throw new ParseException("带值的短选项 " + shortOpt + " 必须是最后一个");
                    }
                }
            }
            
            return index;
        }
        
        private Object convertValue(String value, Class<?> type) throws ParseException {
            try {
                if (type == Integer.class) {
                    return Integer.parseInt(value);
                } else if (type == Double.class) {
                    return Double.parseDouble(value);
                } else if (type == Boolean.class) {
                    return Boolean.parseBoolean(value);
                }
                return value;
            } catch (NumberFormatException e) {
                throw new ParseException("无法转换值 '" + value + "' 为 " + type.getSimpleName());
            }
        }
    }
    
    /**
     * 选项构建器
     */
    static class OptionBuilder {
        private final AdvancedArgsParser parser;
        private final OptionDefinition definition;
        
        OptionBuilder(AdvancedArgsParser parser, OptionDefinition definition) {
            this.parser = parser;
            this.definition = definition;
        }
        
        public OptionBuilder withValue() {
            definition.hasValue = true;
            return this;
        }
        
        public OptionBuilder flag() {
            definition.isFlag = true;
            return this;
        }
        
        public OptionBuilder asInt() {
            definition.type = Integer.class;
            return this;
        }
        
        public OptionBuilder asDouble() {
            definition.type = Double.class;
            return this;
        }
        
        public OptionBuilder defaultValue(Object value) {
            definition.defaultValue = value;
            return this;
        }
        
        public OptionBuilder description(String desc) {
            definition.description = desc;
            return this;
        }
        
        public OptionBuilder option(String longName, String shortName) {
            return parser.option(longName, shortName);
        }
        
        public OptionBuilder option(String longName) {
            return parser.option(longName);
        }
        
        public AdvancedArgs parse(String[] args) throws ParseException {
            return parser.parse(args);
        }
    }
    
    /**
     * 选项定义
     */
    static class OptionDefinition {
        String longName;
        String shortName;
        boolean hasValue = false;
        boolean isFlag = false;
        Class<?> type = String.class;
        Object defaultValue;
        String description;
        
        OptionDefinition(String longName, String shortName) {
            this.longName = longName;
            this.shortName = shortName;
        }
    }
    
    /**
     * 解析结果
     */
    static class AdvancedArgs {
        private final Map<String, Object> values;
        private final List<String> positionalArgs;
        
        AdvancedArgs(Map<String, Object> values, List<String> positionalArgs) {
            this.values = values;
            this.positionalArgs = positionalArgs;
        }
        
        public String getString(String key) {
            Object value = values.get(key);
            return value != null ? value.toString() : null;
        }
        
        public int getInt(String key) {
            Object value = values.get(key);
            return value instanceof Integer ? (Integer) value : 0;
        }
        
        public double getDouble(String key) {
            Object value = values.get(key);
            return value instanceof Double ? (Double) value : 0.0;
        }
        
        public boolean getBoolean(String key) {
            Object value = values.get(key);
            return value instanceof Boolean ? (Boolean) value : false;
        }
        
        public List<String> getPositionalArgs() {
            return new ArrayList<>(positionalArgs);
        }
        
        public String getPositionalArg(int index) {
            return index < positionalArgs.size() ? positionalArgs.get(index) : null;
        }
        
        public int getPositionalArgCount() {
            return positionalArgs.size();
        }
        
        public boolean hasOption(String key) {
            return values.containsKey(key);
        }
    }
    
    /**
     * 解析异常
     */
    static class ParseException extends Exception {
        ParseException(String message) {
            super(message);
        }
    }
}
