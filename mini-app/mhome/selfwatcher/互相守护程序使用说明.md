# 互相守护程序使用说明

## 概述

这是一个Java实现的互相守护程序，主程序和守护程序合并为一个程序，它们能够互相监控并在对方崩溃时自动重启对方，确保系统的高可用性。

## 核心特性

### 🔄 **互相守护机制**
- 主进程和守护进程互相监控
- 任一进程崩溃时，另一进程自动重启它
- 支持最大重启尝试次数限制
- 心跳检测机制确保实时监控

### 📊 **进程管理**
- 自动记录和管理进程PID
- 跨平台进程存活检测
- 优雅的进程启动和关闭
- 完整的日志记录

### 🛡️ **容错机制**
- 重启失败重试机制
- 异常处理和恢复
- 资源清理和释放
- 关闭钩子确保优雅退出

## 文件结构

```
selfwatcher/
├── src/main/java/com/mhome/monitor/
│   ├── MutualGuardian.java      # 主程序
│   ├── GuardianTester.java      # 测试工具
│   └── MonitorUtil.java         # 工具类
├── start_guardian.sh            # Linux/Mac启动脚本
├── start_guardian.bat           # Windows启动脚本
├── pids/                        # PID文件目录
│   ├── main.pid                 # 主进程PID
│   └── daemon.pid               # 守护进程PID
└── logs/                        # 日志目录
    └── guardian.log             # 运行日志
```

## 使用方法

### 1. 编译程序

```bash
# 创建输出目录
mkdir -p target/classes

# 编译Java代码
javac -d target/classes src/main/java/com/mhome/monitor/*.java
```

### 2. 启动程序

#### Linux/Mac系统：
```bash
# 给启动脚本执行权限
chmod +x start_guardian.sh

# 启动程序
./start_guardian.sh
```

#### Windows系统：
```cmd
# 双击运行或命令行执行
start_guardian.bat
```

#### 手动启动：
```bash
# 启动主程序（会自动启动守护进程）
java -cp target/classes com.mhome.monitor.MutualGuardian

# 或者指定进程类型
java -cp target/classes com.mhome.monitor.MutualGuardian MAIN
java -cp target/classes com.mhome.monitor.MutualGuardian DAEMON
```

### 3. 测试守护功能

```bash
# 编译测试工具
javac -d target/classes src/main/java/com/mhome/monitor/GuardianTester.java

# 运行测试工具
java -cp target/classes com.mhome.monitor.GuardianTester
```

## 工作原理

### 启动流程
1. **程序启动**: 默认启动主进程
2. **守护进程启动**: 主进程自动启动守护进程
3. **PID记录**: 两个进程分别记录自己的PID到文件
4. **心跳监控**: 启动定时任务互相检查对方进程状态
5. **业务逻辑**: 各自执行相应的业务逻辑

### 守护机制
1. **心跳检测**: 每5秒检查一次对方进程是否存活
2. **进程检测**: 通过PID文件和系统命令检查进程状态
3. **自动重启**: 发现对方进程停止时，延迟3秒后重启
4. **重试限制**: 最多尝试重启5次，防止无限重启循环

### 业务逻辑
- **主进程**: 执行主要业务逻辑（每30秒一次）
- **守护进程**: 执行监控任务，如内存使用情况检查（每30秒一次）

## 配置参数

可以在 `MutualGuardian.java` 中修改以下配置：

```java
private static final int HEARTBEAT_INTERVAL = 5;      // 心跳间隔(秒)
private static final int RESTART_DELAY = 3;           // 重启延迟(秒)
private static final int MAX_RESTART_ATTEMPTS = 5;    // 最大重启尝试次数
```

## 测试场景

### 1. 正常运行测试
- 启动程序，观察两个进程是否正常运行
- 查看日志输出，确认心跳检测正常

### 2. 主进程崩溃测试
```bash
# 使用测试工具终止主进程
java -cp target/classes com.mhome.monitor.GuardianTester
# 选择选项2：终止主进程
```

### 3. 守护进程崩溃测试
```bash
# 使用测试工具终止守护进程
java -cp target/classes com.mhome.monitor.GuardianTester
# 选择选项3：终止守护进程
```

### 4. 手动终止测试
```bash
# 查找Java进程
ps aux | grep java

# 手动终止进程
kill -9 <PID>

# 观察另一个进程是否会重启被终止的进程
```

## 日志说明

程序运行时会输出详细的日志信息：

```
=== 互相守护程序启动 ===
进程类型: MAIN
进程PID: 12345
启动时间: Mon Jan 01 12:00:00 CST 2024

[MAIN] 心跳检查 - Mon Jan 01 12:00:05 CST 2024 - 对方进程状态: 正常
[主进程] 执行业务逻辑 - Mon Jan 01 12:00:10 CST 2024
[守护进程] 执行监控任务 - Mon Jan 01 12:00:10 CST 2024
```

## 注意事项

### 1. 权限要求
- 程序需要有创建文件和目录的权限
- 需要有执行系统命令的权限（用于进程检测）

### 2. 平台兼容性
- 支持 Windows、Linux、macOS
- 不同平台使用不同的系统命令进行进程管理

### 3. 资源使用
- 程序会定期执行系统命令，有一定的CPU开销
- PID文件和日志文件会占用少量磁盘空间

### 4. 安全考虑
- PID文件可能被恶意修改，建议设置适当的文件权限
- 程序具有重启其他进程的能力，需要谨慎使用

## 故障排除

### 1. 程序无法启动
- 检查Java环境是否正确安装
- 确认类文件已正确编译
- 检查文件权限是否足够

### 2. 进程无法重启
- 检查系统命令是否可用（kill、tasklist等）
- 查看错误日志了解具体原因
- 确认没有达到最大重启尝试次数

### 3. PID文件问题
- 手动删除 `pids/` 目录下的PID文件
- 重新启动程序

### 4. 端口或资源冲突
- 确保没有其他程序占用相同的资源
- 检查系统资源是否充足

## 扩展建议

1. **添加配置文件**: 支持外部配置文件来修改参数
2. **增强监控**: 添加更多的健康检查指标
3. **通知机制**: 添加邮件或短信通知功能
4. **Web界面**: 提供Web管理界面
5. **集群支持**: 支持多节点部署和管理

这个互相守护程序提供了一个可靠的进程监控和自动恢复机制，适用于需要高可用性的Java应用程序。
