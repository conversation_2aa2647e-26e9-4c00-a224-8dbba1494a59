#!/bin/bash

echo "=== Java Web Start 应用构建脚本 ==="

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 配置变量
BUILD_DIR="build"
WEBSTART_DIR="webstart"
CLASSES_DIR="$BUILD_DIR/classes"
DIST_DIR="$BUILD_DIR/dist"
KEYSTORE_FILE="$BUILD_DIR/webstart-demo.jks"
KEYSTORE_ALIAS="webstart-demo"
KEYSTORE_PASSWORD="changeit"

echo "1. 清理构建目录..."
rm -rf "$BUILD_DIR"
mkdir -p "$CLASSES_DIR"
mkdir -p "$DIST_DIR"

echo "2. 编译Java源代码..."
javac -d "$CLASSES_DIR" src/main/java/com/mhome/monitor/WebStartDemo.java

if [ $? -ne 0 ]; then
    echo "❌ 编译失败！"
    exit 1
fi

echo "✅ 编译成功"

echo "3. 创建JAR文件..."
cd "$CLASSES_DIR"
jar cvf "../dist/webstart-demo.jar" com/
cd "$SCRIPT_DIR"

echo "4. 生成自签名证书..."
if [ ! -f "$KEYSTORE_FILE" ]; then
    keytool -genkey -alias "$KEYSTORE_ALIAS" \
            -keystore "$KEYSTORE_FILE" \
            -storepass "$KEYSTORE_PASSWORD" \
            -keypass "$KEYSTORE_PASSWORD" \
            -dname "CN=WebStart Demo, OU=Development, O=Example Corp, L=City, ST=State, C=US" \
            -keyalg RSA \
            -keysize 2048 \
            -validity 365 \
            -noprompt
    
    if [ $? -eq 0 ]; then
        echo "✅ 证书生成成功"
    else
        echo "❌ 证书生成失败！"
        exit 1
    fi
else
    echo "✅ 使用现有证书"
fi

echo "5. 签名JAR文件..."
jarsigner -keystore "$KEYSTORE_FILE" \
          -storepass "$KEYSTORE_PASSWORD" \
          -keypass "$KEYSTORE_PASSWORD" \
          "$DIST_DIR/webstart-demo.jar" \
          "$KEYSTORE_ALIAS"

if [ $? -eq 0 ]; then
    echo "✅ JAR签名成功"
else
    echo "❌ JAR签名失败！"
    exit 1
fi

echo "6. 验证JAR签名..."
jarsigner -verify "$DIST_DIR/webstart-demo.jar"

if [ $? -eq 0 ]; then
    echo "✅ JAR签名验证成功"
else
    echo "❌ JAR签名验证失败！"
    exit 1
fi

echo "7. 复制文件到部署目录..."
cp "$DIST_DIR/webstart-demo.jar" "$WEBSTART_DIR/"
cp "$WEBSTART_DIR/webstart-demo.jnlp" "$DIST_DIR/"
cp "$WEBSTART_DIR/index.html" "$DIST_DIR/"

# 创建简单的图标文件（如果不存在）
if [ ! -f "$WEBSTART_DIR/icon.png" ]; then
    echo "创建默认图标文件..."
    # 这里应该有一个真实的图标文件，现在创建一个占位符
    touch "$WEBSTART_DIR/icon.png"
    touch "$WEBSTART_DIR/icon-large.png"
fi

echo "8. 构建完成！"
echo ""
echo "构建结果:"
echo "  JAR文件: $DIST_DIR/webstart-demo.jar"
echo "  JNLP文件: $DIST_DIR/webstart-demo.jnlp"
echo "  HTML页面: $DIST_DIR/index.html"
echo ""
echo "部署说明:"
echo "1. 将 $DIST_DIR 目录下的所有文件复制到Web服务器"
echo "2. 确保Web服务器配置了正确的MIME类型:"
echo "   .jnlp -> application/x-java-jnlp-file"
echo "   .jar  -> application/x-java-archive"
echo ""
echo "3. 启动简单HTTP服务器进行测试:"
echo "   cd $DIST_DIR"
echo "   python3 -m http.server 8080"
echo "   然后访问: http://localhost:8080/"
echo ""
echo "4. 手动启动应用程序:"
echo "   javaws http://localhost:8080/webstart-demo.jnlp"
echo ""
echo "注意: Java Web Start 在 Java 11+ 中已被移除，请使用 Java 8-10 进行测试"
