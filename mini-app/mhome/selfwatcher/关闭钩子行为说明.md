# Java 关闭钩子 (Shutdown Hook) 行为详解

## 概述

`Runtime.getRuntime().addShutdownHook()` 是Java提供的一种在JVM关闭时执行清理代码的机制。但是，它的触发条件有限制。

## 🔍 触发关闭钩子的情况

### ✅ **会触发关闭钩子**

1. **正常程序退出**
   ```java
   System.exit(0);  // 正常退出
   System.exit(1);  // 异常退出
   ```

2. **用户中断**
   ```bash
   # Ctrl+C (发送SIGINT信号)
   ^C
   ```

3. **优雅终止信号**
   ```bash
   kill <pid>           # 默认发送SIGTERM
   kill -15 <pid>       # SIGTERM信号
   kill -TERM <pid>     # SIGTERM信号
   kill -2 <pid>        # SIGINT信号 (相当于Ctrl+C)
   kill -1 <pid>        # SIGHUP信号
   ```

4. **JVM正常关闭**
   - 所有非守护线程结束
   - main方法正常返回

### ❌ **不会触发关闭钩子**

1. **强制终止信号**
   ```bash
   kill -9 <pid>        # SIGKILL信号 - 最常见的不触发情况
   kill -KILL <pid>     # SIGKILL信号
   ```

2. **系统异常**
   - JVM崩溃
   - 系统断电
   - 操作系统强制关闭
   - 内存不足被系统杀死

3. **Runtime.halt()**
   ```java
   Runtime.getRuntime().halt(0);  // 强制退出，不执行关闭钩子
   ```

## 📊 信号对比表

| 信号 | 命令 | 是否触发关闭钩子 | 说明 |
|------|------|------------------|------|
| SIGTERM | `kill <pid>` | ✅ 是 | 优雅终止，默认信号 |
| SIGTERM | `kill -15 <pid>` | ✅ 是 | 优雅终止 |
| SIGINT | `kill -2 <pid>` | ✅ 是 | 中断信号，相当于Ctrl+C |
| SIGHUP | `kill -1 <pid>` | ✅ 是 | 挂起信号 |
| SIGKILL | `kill -9 <pid>` | ❌ 否 | 强制终止，无法捕获 |
| SIGKILL | `kill -KILL <pid>` | ❌ 否 | 强制终止，无法捕获 |

## 🛠️ 实际测试

### 测试程序
```java
// 使用 ShutdownHookTest.java 进行测试
java -cp target/classes com.mhome.monitor.ShutdownHookTest
```

### 测试步骤
1. 运行测试程序，记录显示的PID
2. 在另一个终端执行不同的kill命令
3. 观察关闭钩子是否被触发

### 预期结果
```bash
# 会触发关闭钩子
kill 12345        # 输出: "关闭钩子被触发!"
kill -15 12345    # 输出: "关闭钩子被触发!"
kill -2 12345     # 输出: "关闭钩子被触发!"

# 不会触发关闭钩子
kill -9 12345     # 程序直接终止，无任何输出
```

## 🔧 在互相守护程序中的处理

### 问题
由于 `kill -9` 不会触发关闭钩子，被强制终止的进程无法清理PID文件，可能导致：
- PID文件残留
- 对方进程误判为仍在运行
- 重启机制失效

### 解决方案

1. **增强进程检测**
   ```java
   // 不仅检查PID文件存在，还要验证进程是否真的存活
   private boolean checkOtherProcess() {
       // 1. 检查PID文件是否存在
       // 2. 读取PID
       // 3. 验证进程是否存活
       // 4. 如果进程不存在，清理PID文件
   }
   ```

2. **自动清理机制**
   ```java
   // 如果检测到进程不存在，自动清理PID文件
   if (!alive) {
       Files.deleteIfExists(Paths.get(pidFile));
   }
   ```

3. **多重验证**
   ```java
   // 使用系统命令验证进程存在
   private boolean isProcessAlive(String pid) {
       // Linux/Mac: kill -0 <pid>
       // Windows: tasklist /FI "PID eq <pid>"
   }
   ```

## 💡 最佳实践

### 1. 关闭钩子设计原则
```java
Runtime.getRuntime().addShutdownHook(new Thread(() -> {
    try {
        // 快速执行，避免长时间阻塞
        cleanup();
    } catch (Exception e) {
        // 记录错误但不抛出异常
        System.err.println("关闭钩子执行失败: " + e.getMessage());
    }
}));
```

### 2. 不要依赖关闭钩子
- 关闭钩子只是最后的保障，不是主要的清理机制
- 重要的清理工作应该在正常流程中完成
- 使用try-with-resources等机制确保资源释放

### 3. 进程监控策略
```java
// 定期检查进程状态，而不是仅依赖PID文件
private boolean isProcessReallyAlive(String pid) {
    // 1. 检查PID文件
    // 2. 验证进程存在
    // 3. 检查进程是否响应
    // 4. 清理无效的PID文件
}
```

### 4. 优雅关闭支持
```java
// 支持优雅关闭信号
public void gracefulShutdown() {
    running = false;
    // 等待当前任务完成
    // 清理资源
    // 删除PID文件
}
```

## ⚠️ 注意事项

1. **关闭钩子限制**
   - 执行时间有限制（通常几秒钟）
   - 不能执行复杂的操作
   - 可能被强制中断

2. **信号处理差异**
   - 不同操作系统的信号处理可能不同
   - Windows和Unix系统的行为有差异

3. **测试重要性**
   - 必须测试不同的终止方式
   - 验证清理机制是否有效
   - 确保重启机制正常工作

## 总结

**关键点：`kill -9` 不会触发关闭钩子！**

在设计互相守护程序时，必须考虑到这种情况，通过以下方式增强健壮性：
- 增强的进程存活检测
- 自动PID文件清理
- 多重验证机制
- 定期状态检查

这样即使进程被 `kill -9` 强制终止，守护机制仍然能够正常工作。
