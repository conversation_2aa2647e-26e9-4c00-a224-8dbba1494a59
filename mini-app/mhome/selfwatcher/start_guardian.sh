#!/bin/bash

# 互相守护程序启动脚本

echo "=== 互相守护程序启动脚本 ==="

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java环境，请先安装Java"
    exit 1
fi

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
echo "脚本目录: $SCRIPT_DIR"

# 设置类路径
CLASSPATH="$SCRIPT_DIR/target/classes"

# 检查编译后的类文件是否存在
if [ ! -f "$CLASSPATH/com/mhome/monitor/MutualGuardian.class" ]; then
    echo "错误: 未找到编译后的类文件"
    echo "请先编译Java代码:"
    echo "  cd $SCRIPT_DIR"
    echo "  mkdir -p target/classes"
    echo "  javac -d target/classes src/main/java/com/mhome/monitor/*.java"
    exit 1
fi

# 创建日志目录
mkdir -p "$SCRIPT_DIR/logs"

# 启动主进程
echo "启动互相守护程序..."
echo "日志将输出到控制台和 logs/guardian.log"

# 启动程序并记录日志
java -cp "$CLASSPATH" com.mhome.monitor.MutualGuardian 2>&1 | tee "$SCRIPT_DIR/logs/guardian.log"
