#!/bin/bash

echo "=== Java Agent 构建脚本 ==="

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 创建构建目录
BUILD_DIR="build"
AGENT_DIR="$BUILD_DIR/agent"
mkdir -p "$AGENT_DIR"

echo "1. 编译Java代码..."

# 编译所有Java文件
javac -d "$AGENT_DIR" src/main/java/com/mhome/monitor/InstrumentationDemo.java
javac -d "$AGENT_DIR" src/main/java/com/mhome/monitor/SimpleClassTransformer.java
javac -d "$AGENT_DIR" src/main/java/com/mhome/monitor/AdvancedInstrumentationExample.java

if [ $? -ne 0 ]; then
    echo "编译失败！"
    exit 1
fi

echo "2. 创建 MANIFEST.MF 文件..."

# 创建 MANIFEST.MF 文件
cat > "$AGENT_DIR/MANIFEST.MF" << EOF
Manifest-Version: 1.0
Premain-Class: com.mhome.monitor.InstrumentationDemo
Agent-Class: com.mhome.monitor.InstrumentationDemo
Can-Redefine-Classes: true
Can-Retransform-Classes: true
Can-Set-Native-Method-Prefix: true

EOF

echo "3. 创建高级Agent的 MANIFEST.MF 文件..."

cat > "$AGENT_DIR/MANIFEST-ADVANCED.MF" << EOF
Manifest-Version: 1.0
Premain-Class: com.mhome.monitor.AdvancedInstrumentationExample
Agent-Class: com.mhome.monitor.AdvancedInstrumentationExample
Can-Redefine-Classes: true
Can-Retransform-Classes: true
Can-Set-Native-Method-Prefix: true

EOF

echo "4. 打包Agent JAR文件..."

# 打包基础Agent
cd "$AGENT_DIR"
jar cmf MANIFEST.MF ../instrumentation-agent.jar com/

# 打包高级Agent
jar cmf MANIFEST-ADVANCED.MF ../advanced-agent.jar com/

cd "$SCRIPT_DIR"

echo "5. 构建完成！"
echo ""
echo "生成的文件:"
echo "  $BUILD_DIR/instrumentation-agent.jar - 基础Instrumentation演示Agent"
echo "  $BUILD_DIR/advanced-agent.jar - 高级Instrumentation演示Agent"
echo ""
echo "使用方法:"
echo "  # 基础Agent"
echo "  java -javaagent:$BUILD_DIR/instrumentation-agent.jar com.mhome.monitor.InstrumentationDemo"
echo ""
echo "  # 高级Agent"
echo "  java -javaagent:$BUILD_DIR/advanced-agent.jar com.mhome.monitor.AdvancedInstrumentationExample"
echo ""
echo "注意: 需要先编译目标类:"
echo "  javac -d target/classes src/main/java/com/mhome/monitor/*.java"
echo "  java -cp target/classes -javaagent:$BUILD_DIR/instrumentation-agent.jar com.mhome.monitor.InstrumentationDemo"
