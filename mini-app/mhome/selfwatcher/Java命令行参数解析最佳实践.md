# Java命令行参数解析最佳实践

## 概述

Java命令行参数解析有多种方式，从简单的手工解析到使用专业的第三方库。本文总结了最优雅和实用的解析方法。

## 🏆 推荐方案排序

### 1. **第三方库（推荐）**
- **Apache Commons CLI** - 成熟稳定
- **JCommander** - 注解驱动
- **Picocli** - 现代化，功能丰富
- **Args4j** - 轻量级

### 2. **自定义优雅解析器**
- 流式API设计
- 建造者模式
- 链式调用

### 3. **简单手工解析**
- 适用于参数很少的情况
- 快速原型开发

## 📚 详细方案对比

### 方案1: Apache Commons CLI（最推荐）

```xml
<dependency>
    <groupId>commons-cli</groupId>
    <artifactId>commons-cli</artifactId>
    <version>1.5.0</version>
</dependency>
```

```java
import org.apache.commons.cli.*;

public class ApacheCliExample {
    public static void main(String[] args) {
        Options options = new Options();
        
        options.addOption("h", "host", true, "服务器主机");
        options.addOption("p", "port", true, "服务器端口");
        options.addOption("v", "verbose", false, "启用详细输出");
        options.addOption("c", "config", true, "配置文件路径");
        
        CommandLineParser parser = new DefaultParser();
        HelpFormatter formatter = new HelpFormatter();
        
        try {
            CommandLine cmd = parser.parse(options, args);
            
            String host = cmd.getOptionValue("host", "localhost");
            int port = Integer.parseInt(cmd.getOptionValue("port", "8080"));
            boolean verbose = cmd.hasOption("verbose");
            String config = cmd.getOptionValue("config");
            
            System.out.println("Host: " + host);
            System.out.println("Port: " + port);
            System.out.println("Verbose: " + verbose);
            System.out.println("Config: " + config);
            
        } catch (ParseException e) {
            System.err.println("参数解析失败: " + e.getMessage());
            formatter.printHelp("myapp", options);
        }
    }
}
```

**优点：**
- 成熟稳定，广泛使用
- 支持短选项和长选项
- 自动生成帮助信息
- 良好的错误处理

**缺点：**
- 需要额外依赖
- API相对传统

### 方案2: Picocli（现代化推荐）

```xml
<dependency>
    <groupId>info.picocli</groupId>
    <artifactId>picocli</artifactId>
    <version>4.7.0</version>
</dependency>
```

```java
import picocli.CommandLine;
import picocli.CommandLine.Option;
import picocli.CommandLine.Parameters;

@CommandLine.Command(name = "myapp", description = "示例应用程序")
public class PicocliExample implements Runnable {
    
    @Option(names = {"-h", "--host"}, defaultValue = "localhost", description = "服务器主机")
    private String host;
    
    @Option(names = {"-p", "--port"}, defaultValue = "8080", description = "服务器端口")
    private int port;
    
    @Option(names = {"-v", "--verbose"}, description = "启用详细输出")
    private boolean verbose;
    
    @Option(names = {"-c", "--config"}, description = "配置文件路径")
    private String config;
    
    @Parameters(description = "输入文件")
    private List<String> files;
    
    @Override
    public void run() {
        System.out.println("Host: " + host);
        System.out.println("Port: " + port);
        System.out.println("Verbose: " + verbose);
        System.out.println("Config: " + config);
        System.out.println("Files: " + files);
    }
    
    public static void main(String[] args) {
        int exitCode = new CommandLine(new PicocliExample()).execute(args);
        System.exit(exitCode);
    }
}
```

**优点：**
- 注解驱动，代码简洁
- 自动类型转换
- 丰富的验证功能
- 自动生成帮助和手册页
- 支持子命令

**缺点：**
- 需要额外依赖
- 学习曲线稍陡

### 方案3: 自定义流式API解析器

```java
public class ElegantArgsParser {
    public static void main(String[] args) {
        CommandArgs result = Args.parse(args)
            .option("--host").defaultValue("localhost")
            .option("--port").asInt().defaultValue(8080)
            .option("--verbose").asFlag()
            .option("--config").required()
            .build();
        
        System.out.println("Host: " + result.get("host"));
        System.out.println("Port: " + result.getInt("port"));
        System.out.println("Verbose: " + result.getBoolean("verbose"));
        System.out.println("Config: " + result.get("config"));
    }
}
```

**优点：**
- 无外部依赖
- API设计优雅
- 可定制性强
- 学习成本低

**缺点：**
- 需要自己实现
- 功能相对简单
- 错误处理需要完善

### 方案4: 简单Map-based解析器

```java
public class SimpleArgsParser {
    public static Map<String, String> parseArgs(String[] args) {
        Map<String, String> result = new HashMap<>();
        
        for (int i = 0; i < args.length; i++) {
            if (args[i].startsWith("--")) {
                String key = args[i].substring(2);
                if (i + 1 < args.length && !args[i + 1].startsWith("-")) {
                    result.put(key, args[++i]);
                } else {
                    result.put(key, "true");
                }
            }
        }
        
        return result;
    }
    
    public static void main(String[] args) {
        Map<String, String> params = parseArgs(args);
        
        String host = params.getOrDefault("host", "localhost");
        int port = Integer.parseInt(params.getOrDefault("port", "8080"));
        boolean verbose = "true".equals(params.get("verbose"));
        
        System.out.println("Host: " + host);
        System.out.println("Port: " + port);
        System.out.println("Verbose: " + verbose);
    }
}
```

**优点：**
- 极简实现
- 无外部依赖
- 易于理解和修改

**缺点：**
- 功能有限
- 缺乏类型安全
- 错误处理简陋

## 🎯 选择建议

### 企业级应用
```java
// 推荐使用 Picocli
@CommandLine.Command(name = "enterprise-app")
public class EnterpriseApp implements Runnable {
    @Option(names = "--config", required = true)
    private File configFile;
    
    @Option(names = "--env", defaultValue = "dev")
    private Environment environment;
    
    // ...
}
```

### 简单工具
```java
// 推荐使用 Apache Commons CLI
Options options = new Options();
options.addOption("f", "file", true, "输入文件");
options.addOption("o", "output", true, "输出文件");

CommandLine cmd = new DefaultParser().parse(options, args);
```

### 原型开发
```java
// 使用简单解析器
Map<String, String> args = parseSimpleArgs(argv);
String input = args.getOrDefault("input", "default.txt");
```

## 💡 最佳实践

### 1. **参数设计原则**
- 使用长选项名（`--host`）提高可读性
- 提供短选项（`-h`）提高效率
- 设置合理的默认值
- 必需参数要明确标识

### 2. **错误处理**
```java
try {
    CommandLine cmd = parser.parse(options, args);
    // 处理参数
} catch (ParseException e) {
    System.err.println("错误: " + e.getMessage());
    formatter.printHelp("myapp", options);
    System.exit(1);
}
```

### 3. **帮助信息**
```java
if (cmd.hasOption("help")) {
    formatter.printHelp("myapp [OPTIONS] [FILES...]", options);
    return;
}
```

### 4. **类型安全**
```java
// 使用专门的方法进行类型转换
public static int getIntOption(CommandLine cmd, String option, int defaultValue) {
    try {
        return Integer.parseInt(cmd.getOptionValue(option, String.valueOf(defaultValue)));
    } catch (NumberFormatException e) {
        System.err.println("无效的数字: " + cmd.getOptionValue(option));
        return defaultValue;
    }
}
```

### 5. **配置文件集成**
```java
// 优先级: 命令行参数 > 配置文件 > 默认值
String host = cmd.getOptionValue("host");
if (host == null) {
    host = config.getProperty("host", "localhost");
}
```

## 🎯 处理位置参数（不带 - 或 -- 的参数）

### 位置参数的类型

1. **文件路径**: `myapp input.txt output.txt`
2. **命令参数**: `git add file1.txt file2.txt`
3. **子命令**: `docker run ubuntu`
4. **混合参数**: `rsync -av source/ dest/`

### Apache Commons CLI 处理位置参数

```java
CommandLine cmd = parser.parse(options, args);

// 获取所有位置参数
String[] remainingArgs = cmd.getArgs();
System.out.println("位置参数: " + Arrays.toString(remainingArgs));

// 处理特定位置的参数
if (remainingArgs.length >= 2) {
    String inputFile = remainingArgs[0];
    String outputFile = remainingArgs[1];
}
```

### Picocli 处理位置参数

```java
@CommandLine.Command(name = "myapp")
public class App {
    @Option(names = {"-v", "--verbose"})
    boolean verbose;

    @Parameters(index = "0", description = "输入文件")
    File inputFile;

    @Parameters(index = "1", description = "输出文件")
    File outputFile;

    @Parameters(index = "2..*", description = "其他文件")
    List<File> otherFiles;
}
```

### 自定义解析器处理位置参数

```java
public class PositionalArgsParser {
    public static ParseResult parse(String[] args) {
        Map<String, String> options = new HashMap<>();
        List<String> positionalArgs = new ArrayList<>();
        boolean endOfOptions = false;

        for (int i = 0; i < args.length; i++) {
            String arg = args[i];

            // 处理 -- 分隔符
            if ("--".equals(arg)) {
                endOfOptions = true;
                continue;
            }

            if (!endOfOptions && arg.startsWith("-")) {
                // 处理选项
                if (arg.startsWith("--")) {
                    // 长选项
                    if (arg.contains("=")) {
                        String[] parts = arg.split("=", 2);
                        options.put(parts[0].substring(2), parts[1]);
                    } else if (i + 1 < args.length && !args[i + 1].startsWith("-")) {
                        options.put(arg.substring(2), args[++i]);
                    } else {
                        options.put(arg.substring(2), "true");
                    }
                } else {
                    // 短选项
                    String shortOpts = arg.substring(1);
                    for (int j = 0; j < shortOpts.length(); j++) {
                        String opt = String.valueOf(shortOpts.charAt(j));
                        if (j == shortOpts.length() - 1 && i + 1 < args.length &&
                            !args[i + 1].startsWith("-")) {
                            options.put(opt, args[++i]);
                        } else {
                            options.put(opt, "true");
                        }
                    }
                }
            } else {
                // 位置参数
                positionalArgs.add(arg);
            }
        }

        return new ParseResult(options, positionalArgs);
    }
}
```

### 复杂命令行示例

```bash
# 基本用法
myapp --host localhost --port 8080 input.txt output.txt

# 短选项组合
myapp -vd -h localhost -p 8080 file1.txt file2.txt

# 等号格式
myapp --host=localhost --port=8080 input.txt

# 使用 -- 分隔符（之后都是位置参数）
myapp --verbose -- --not-an-option -file.txt

# 混合格式
myapp -v --host=localhost file1.txt --file2.txt
```

## 🔧 实用工具类

```java
public class ArgsUtils {
    public static boolean isValidPort(String port) {
        try {
            int p = Integer.parseInt(port);
            return p > 0 && p <= 65535;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    public static File validateFile(String path, boolean mustExist) {
        File file = new File(path);
        if (mustExist && !file.exists()) {
            throw new IllegalArgumentException("文件不存在: " + path);
        }
        return file;
    }

    public static List<String> getPositionalArgs(String[] args) {
        List<String> positional = new ArrayList<>();
        boolean endOfOptions = false;

        for (String arg : args) {
            if ("--".equals(arg)) {
                endOfOptions = true;
                continue;
            }

            if (endOfOptions || !arg.startsWith("-")) {
                positional.add(arg);
            }
        }

        return positional;
    }

    public static void printUsage(String appName, Options options) {
        HelpFormatter formatter = new HelpFormatter();
        formatter.printHelp(appName + " [OPTIONS] [FILES...]", options);
    }
}
```

## 总结

**最优雅的方式排序：**

1. **Picocli** - 现代Java应用的首选
2. **Apache Commons CLI** - 传统但可靠的选择
3. **自定义流式API** - 无依赖的优雅解决方案
4. **简单Map解析** - 快速原型的选择

选择哪种方式取决于项目的复杂度、依赖管理策略和团队偏好。对于大多数项目，推荐使用成熟的第三方库来处理命令行参数解析。
