# BlockingDeque<E> 详解

## 概述

`BlockingDeque<E>` 是Java并发包中的双端阻塞队列接口，继承自 `BlockingQueue<E>` 和 `Deque<E>`。它结合了双端队列的灵活性和阻塞队列的线程安全特性，支持在队列的两端进行阻塞的插入和移除操作。

## 🔍 核心特性

### 1. **双端操作**
- 可以在队列的头部和尾部进行操作
- 支持FIFO（先进先出）和LIFO（后进先出）两种模式

### 2. **阻塞特性**
- 当队列满时，插入操作会阻塞
- 当队列空时，移除操作会阻塞

### 3. **线程安全**
- 所有操作都是线程安全的
- 支持多个生产者和消费者并发操作

## 📊 方法分类

### 1. **插入操作**

| 方法 | 头部 | 尾部 | 特性 |
|------|------|------|------|
| `addFirst(e)` / `addLast(e)` | ✓ | ✓ | 立即返回，满时抛异常 |
| `offerFirst(e)` / `offerLast(e)` | ✓ | ✓ | 立即返回boolean |
| `putFirst(e)` / `putLast(e)` | ✓ | ✓ | **阻塞直到成功** |
| `offerFirst(e,time,unit)` / `offerLast(e,time,unit)` | ✓ | ✓ | **超时阻塞** |

### 2. **移除操作**

| 方法 | 头部 | 尾部 | 特性 |
|------|------|------|------|
| `removeFirst()` / `removeLast()` | ✓ | ✓ | 立即返回，空时抛异常 |
| `pollFirst()` / `pollLast()` | ✓ | ✓ | 立即返回null |
| `takeFirst()` / `takeLast()` | ✓ | ✓ | **阻塞直到成功** |
| `pollFirst(time,unit)` / `pollLast(time,unit)` | ✓ | ✓ | **超时阻塞** |

### 3. **检查操作**

| 方法 | 头部 | 尾部 | 特性 |
|------|------|------|------|
| `getFirst()` / `getLast()` | ✓ | ✓ | 空时抛异常 |
| `peekFirst()` / `peekLast()` | ✓ | ✓ | 空时返回null |

## 🛠️ 主要实现类

### 1. **LinkedBlockingDeque**
```java
// 基于链表的双端阻塞队列
BlockingDeque<String> deque = new LinkedBlockingDeque<>();
BlockingDeque<String> boundedDeque = new LinkedBlockingDeque<>(100); // 有界
```

**特点：**
- 基于双向链表实现
- 支持有界和无界
- 头尾操作时间复杂度 O(1)
- 内存使用相对较高（链表节点开销）

## 🔧 工作原理

### 1. **内部结构**
```java
// LinkedBlockingDeque 内部结构（简化）
class LinkedBlockingDeque<E> {
    // 双向链表节点
    static final class Node<E> {
        E item;
        Node<E> prev;
        Node<E> next;
    }
    
    // 头尾指针
    transient Node<E> first;
    transient Node<E> last;
    
    // 当前元素数量
    private transient int count;
    
    // 容量限制
    private final int capacity;
    
    // 锁和条件变量
    final ReentrantLock lock = new ReentrantLock();
    private final Condition notEmpty = lock.newCondition();
    private final Condition notFull = lock.newCondition();
}
```

### 2. **阻塞机制**
```java
// putFirst 方法的简化实现
public void putFirst(E e) throws InterruptedException {
    if (e == null) throw new NullPointerException();
    
    final ReentrantLock lock = this.lock;
    lock.lock();
    try {
        // 如果队列满了，等待notFull条件
        while (count == capacity) {
            notFull.await();
        }
        
        // 添加到头部
        linkFirst(e);
        
        // 通知等待的消费者
        notEmpty.signal();
    } finally {
        lock.unlock();
    }
}
```

### 3. **双端操作**
```java
// 头部插入
private void linkFirst(E e) {
    Node<E> f = first;
    Node<E> newNode = new Node<>(null, e, f);
    first = newNode;
    if (f == null) {
        last = newNode;
    } else {
        f.prev = newNode;
    }
    count++;
}

// 尾部插入
private void linkLast(E e) {
    Node<E> l = last;
    Node<E> newNode = new Node<>(l, e, null);
    last = newNode;
    if (l == null) {
        first = newNode;
    } else {
        l.next = newNode;
    }
    count++;
}
```

## 🎯 应用场景

### 1. **优先级任务处理**
```java
// 高优先级任务放头部，普通任务放尾部
BlockingDeque<Task> taskQueue = new LinkedBlockingDeque<>();

// 生产者
taskQueue.putFirst(highPriorityTask);  // 高优先级
taskQueue.putLast(normalTask);         // 普通优先级

// 消费者
Task task = taskQueue.takeFirst();     // 优先处理高优先级
```

### 2. **工作窃取（Work Stealing）**
```java
// 每个工作线程有自己的双端队列
BlockingDeque<Task> workerQueue = new LinkedBlockingDeque<>();

// 工作线程从自己队列头部取任务
Task myTask = workerQueue.takeFirst();

// 其他线程可以从尾部"窃取"任务
Task stolenTask = workerQueue.pollLast();
```

### 3. **撤销操作**
```java
// 支持撤销最近的操作
BlockingDeque<Operation> operationHistory = new LinkedBlockingDeque<>();

// 添加操作
operationHistory.addFirst(newOperation);

// 撤销最近操作
Operation lastOp = operationHistory.removeFirst();
```

### 4. **缓存淘汰策略**
```java
// LRU缓存实现
BlockingDeque<CacheItem> cache = new LinkedBlockingDeque<>(maxSize);

// 访问时移到头部
cache.removeFirstOccurrence(item);
cache.addFirst(item);

// 淘汰尾部元素
if (cache.size() >= maxSize) {
    cache.removeLast();
}
```

## ⚡ 性能特点

### 1. **时间复杂度**
- 头尾插入/删除：O(1)
- 查找特定元素：O(n)
- 大小检查：O(1)

### 2. **空间复杂度**
- LinkedBlockingDeque：O(n) + 链表节点开销

### 3. **并发性能**
- 使用单个锁，头尾操作会互相影响
- 适合中等并发场景
- 高并发场景可考虑 ConcurrentLinkedDeque（非阻塞）

## 🔒 线程安全机制

### 1. **锁机制**
```java
// 所有操作都使用同一个锁
final ReentrantLock lock = new ReentrantLock();

// 条件变量用于阻塞和唤醒
private final Condition notEmpty = lock.newCondition();
private final Condition notFull = lock.newCondition();
```

### 2. **阻塞和唤醒**
```java
// 队列满时阻塞
while (count == capacity) {
    notFull.await();  // 等待空间
}

// 添加元素后唤醒消费者
notEmpty.signal();   // 通知有元素可取
```

## ⚠️ 注意事项

### 1. **null值**
- 不允许插入null值
- 会抛出 NullPointerException

### 2. **中断处理**
- 阻塞方法支持中断
- 被中断时抛出 InterruptedException

### 3. **内存使用**
- 链表实现有额外的节点开销
- 无界队列可能导致内存溢出

### 4. **性能考虑**
- 单锁设计，高并发时可能成为瓶颈
- 适合中等并发的场景

## 💡 最佳实践

### 1. **容量设置**
```java
// 根据业务需求设置合适的容量
BlockingDeque<Task> deque = new LinkedBlockingDeque<>(1000);
```

### 2. **异常处理**
```java
try {
    deque.putFirst(item);
} catch (InterruptedException e) {
    Thread.currentThread().interrupt();
    // 处理中断
}
```

### 3. **超时操作**
```java
// 使用超时方法避免无限阻塞
boolean success = deque.offerFirst(item, 5, TimeUnit.SECONDS);
if (!success) {
    // 处理超时情况
}
```

### 4. **资源清理**
```java
// 程序结束时清理队列
deque.clear();
```

## 🆚 与其他队列的比较

| 特性 | BlockingDeque | BlockingQueue | Deque | ConcurrentLinkedDeque |
|------|---------------|---------------|-------|----------------------|
| 双端操作 | ✓ | ✗ | ✓ | ✓ |
| 阻塞特性 | ✓ | ✓ | ✗ | ✗ |
| 线程安全 | ✓ | ✓ | ✗ | ✓ |
| 有界支持 | ✓ | ✓ | ✗ | ✗ |
| 性能 | 中等 | 中等 | 高 | 高 |

BlockingDeque 是一个功能强大的并发数据结构，特别适合需要双端操作和阻塞特性的场景。
