@echo off
REM 互相守护程序启动脚本 (Windows)

echo === 互相守护程序启动脚本 ===

REM 检查Java环境
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境，请先安装Java
    pause
    exit /b 1
)

REM 获取脚本所在目录
set SCRIPT_DIR=%~dp0
echo 脚本目录: %SCRIPT_DIR%

REM 设置类路径
set CLASSPATH=%SCRIPT_DIR%target\classes

REM 检查编译后的类文件是否存在
if not exist "%CLASSPATH%\com\mhome\monitor\MutualGuardian.class" (
    echo 错误: 未找到编译后的类文件
    echo 请先编译Java代码:
    echo   cd %SCRIPT_DIR%
    echo   mkdir target\classes
    echo   javac -d target\classes src\main\java\com\mhome\monitor\*.java
    pause
    exit /b 1
)

REM 创建日志目录
if not exist "%SCRIPT_DIR%logs" mkdir "%SCRIPT_DIR%logs"

REM 启动主进程
echo 启动互相守护程序...
echo 日志将输出到控制台和 logs\guardian.log

REM 启动程序
java -cp "%CLASSPATH%" com.mhome.monitor.MutualGuardian

pause
