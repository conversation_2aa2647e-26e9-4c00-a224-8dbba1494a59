# 智慧化光伏场站前端设计方案

## 1. 前端架构设计

### 1.1 技术栈选择

#### 核心技术栈
```yaml
前端框架: Vue.js 3.0 + Composition API
开发语言: TypeScript 4.8+
构建工具: Vite 4.0
UI组件库: Element Plus 2.0
状态管理: Pinia 2.0
路由管理: Vue Router 4.0
HTTP客户端: Axios 1.0
CSS预处理器: Sass/SCSS
代码规范: ESLint + Prettier
```

#### 可视化技术栈
```yaml
地图组件: Leaflet.js + OpenLayers
图表组件: ECharts 5.0 + D3.js 7.0
3D渲染: Three.js + WebGL
数据可视化: AntV G6 + G2Plot
实时通信: Socket.IO Client
视频播放: Video.js + HLS.js
```

### 1.2 项目结构

#### 目录结构
```
solar-plant-frontend/
├── public/                 # 静态资源
│   ├── index.html
│   ├── favicon.ico
│   └── assets/
├── src/
│   ├── api/               # API接口
│   ├── assets/            # 资源文件
│   ├── components/        # 公共组件
│   ├── composables/       # 组合式函数
│   ├── layouts/           # 布局组件
│   ├── pages/             # 页面组件
│   ├── router/            # 路由配置
│   ├── stores/            # 状态管理
│   ├── styles/            # 样式文件
│   ├── types/             # TypeScript类型
│   ├── utils/             # 工具函数
│   ├── App.vue
│   └── main.ts
├── tests/                 # 测试文件
├── docs/                  # 文档
├── package.json
├── vite.config.ts
├── tsconfig.json
└── README.md
```

### 1.3 组件架构

#### 组件分层
```
组件架构
├── 页面组件 (Pages)
│   ├── 监控大屏页面
│   ├── 设备管理页面
│   ├── 任务调度页面
│   ├── 数据分析页面
│   └── 系统设置页面
├── 布局组件 (Layouts)
│   ├── 主布局组件
│   ├── 大屏布局组件
│   ├── 移动端布局
│   └── 登录布局组件
├── 业务组件 (Business)
│   ├── 设备状态组件
│   ├── 实时数据组件
│   ├── 告警信息组件
│   ├── 任务执行组件
│   └── 数据图表组件
└── 基础组件 (Base)
    ├── 表格组件
    ├── 表单组件
    ├── 弹窗组件
    ├── 图标组件
    └── 工具组件
```

## 2. 核心页面设计

### 2.1 监控大屏

#### 大屏布局设计
```vue
<template>
  <div class="dashboard-screen">
    <!-- 顶部标题栏 -->
    <header class="dashboard-header">
      <div class="title">智慧化光伏场站监控中心</div>
      <div class="datetime">{{ currentTime }}</div>
    </header>

    <!-- 主要内容区域 -->
    <main class="dashboard-main">
      <!-- 左侧面板 -->
      <aside class="left-panel">
        <div class="panel-item">
          <h3>场站概览</h3>
          <StationOverview :data="stationData" />
        </div>

        <div class="panel-item">
          <h3>发电统计</h3>
          <PowerGeneration :data="powerData" />
        </div>

        <div class="panel-item">
          <h3>设备状态</h3>
          <DeviceStatus :devices="devices" />
        </div>
      </aside>

      <!-- 中央地图区域 -->
      <section class="center-map">
        <StationMap
          :devices="devices"
          :alarms="alarms"
          @device-click="handleDeviceClick"
        />
      </section>

      <!-- 右侧面板 -->
      <aside class="right-panel">
        <div class="panel-item">
          <h3>实时告警</h3>
          <AlarmList :alarms="realtimeAlarms" />
        </div>

        <div class="panel-item">
          <h3>任务执行</h3>
          <TaskExecution :tasks="runningTasks" />
        </div>

        <div class="panel-item">
          <h3>环境监测</h3>
          <EnvironmentMonitor :data="environmentData" />
        </div>
      </aside>
    </main>

    <!-- 底部数据面板 -->
    <footer class="dashboard-footer">
      <div class="data-panel">
        <div class="data-item">
          <span class="label">总装机容量</span>
          <span class="value">{{ stationData.totalCapacity }}MW</span>
        </div>
        <div class="data-item">
          <span class="label">当前功率</span>
          <span class="value">{{ stationData.currentPower }}MW</span>
        </div>
        <div class="data-item">
          <span class="label">今日发电量</span>
          <span class="value">{{ stationData.todayGeneration }}MWh</span>
        </div>
        <div class="data-item">
          <span class="label">设备在线率</span>
          <span class="value">{{ stationData.onlineRate }}%</span>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useWebSocket } from '@/composables/useWebSocket'
import { useStationData } from '@/composables/useStationData'

// 响应式数据
const currentTime = ref('')
const stationData = ref({})
const powerData = ref({})
const devices = ref([])
const alarms = ref([])
const realtimeAlarms = ref([])
const runningTasks = ref([])
const environmentData = ref({})

// 组合式函数
const { connect, disconnect, on } = useWebSocket()
const { fetchStationData, fetchDevices } = useStationData()

// 生命周期
onMounted(async () => {
  // 初始化数据
  await initializeData()

  // 建立WebSocket连接
  connect('/ws/realtime')

  // 监听实时数据
  on('device-status', handleDeviceStatusUpdate)
  on('alarm', handleNewAlarm)
  on('task-status', handleTaskStatusUpdate)

  // 启动时间更新
  updateTime()
  setInterval(updateTime, 1000)
})

onUnmounted(() => {
  disconnect()
})

// 方法
const initializeData = async () => {
  try {
    stationData.value = await fetchStationData()
    devices.value = await fetchDevices()
    // 其他数据初始化...
  } catch (error) {
    console.error('数据初始化失败:', error)
  }
}

const updateTime = () => {
  currentTime.value = new Date().toLocaleString()
}

const handleDeviceClick = (device: Device) => {
  // 处理设备点击事件
  console.log('设备点击:', device)
}

const handleDeviceStatusUpdate = (data: any) => {
  // 更新设备状态
  const deviceIndex = devices.value.findIndex(d => d.id === data.deviceId)
  if (deviceIndex !== -1) {
    devices.value[deviceIndex].status = data.status
  }
}

const handleNewAlarm = (alarm: Alarm) => {
  // 处理新告警
  realtimeAlarms.value.unshift(alarm)
  if (realtimeAlarms.value.length > 10) {
    realtimeAlarms.value.pop()
  }
}

const handleTaskStatusUpdate = (data: any) => {
  // 更新任务状态
  const taskIndex = runningTasks.value.findIndex(t => t.id === data.taskId)
  if (taskIndex !== -1) {
    runningTasks.value[taskIndex].status = data.status
  }
}
</script>

<style lang="scss" scoped>
.dashboard-screen {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0c1426 0%, #1a2332 100%);
  color: #ffffff;
  overflow: hidden;

  .dashboard-header {
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 40px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);

    .title {
      font-size: 28px;
      font-weight: bold;
      background: linear-gradient(45deg, #00d4ff, #00ff88);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .datetime {
      font-size: 18px;
      color: #a0a0a0;
    }
  }

  .dashboard-main {
    height: calc(100vh - 160px);
    display: flex;
    gap: 20px;
    padding: 20px;

    .left-panel,
    .right-panel {
      width: 350px;
      display: flex;
      flex-direction: column;
      gap: 20px;

      .panel-item {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        padding: 20px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);

        h3 {
          margin: 0 0 15px 0;
          font-size: 18px;
          color: #00d4ff;
        }
      }
    }

    .center-map {
      flex: 1;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 12px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      overflow: hidden;
    }
  }

  .dashboard-footer {
    height: 80px;
    padding: 0 40px;

    .data-panel {
      height: 100%;
      display: flex;
      justify-content: space-around;
      align-items: center;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 12px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);

      .data-item {
        text-align: center;

        .label {
          display: block;
          font-size: 14px;
          color: #a0a0a0;
          margin-bottom: 5px;
        }

        .value {
          display: block;
          font-size: 24px;
          font-weight: bold;
          color: #00ff88;
        }
      }
    }
  }
}
</style>
```

### 2.2 设备管理页面

#### 设备管理组件
```vue
<template>
  <div class="device-management">
    <!-- 搜索和操作栏 -->
    <div class="toolbar">
      <div class="search-section">
        <el-input
          v-model="searchQuery"
          placeholder="搜索设备..."
          prefix-icon="Search"
          clearable
          @input="handleSearch"
        />
        <el-select v-model="deviceTypeFilter" placeholder="设备类型" clearable>
          <el-option
            v-for="type in deviceTypes"
            :key="type.value"
            :label="type.label"
            :value="type.value"
          />
        </el-select>
        <el-select v-model="statusFilter" placeholder="设备状态" clearable>
          <el-option
            v-for="status in deviceStatuses"
            :key="status.value"
            :label="status.label"
            :value="status.value"
          />
        </el-select>
      </div>

      <div class="action-section">
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          添加设备
        </el-button>
        <el-button @click="exportDevices">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
        <el-button @click="refreshDevices">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 设备列表表格 -->
    <div class="device-table">
      <el-table
        :data="filteredDevices"
        v-loading="loading"
        stripe
        border
        height="calc(100vh - 200px)"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column prop="deviceId" label="设备ID" width="120" />

        <el-table-column prop="deviceName" label="设备名称" min-width="150" />

        <el-table-column prop="deviceType" label="设备类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getDeviceTypeColor(row.deviceType)">
              {{ getDeviceTypeName(row.deviceType) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)">
              {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="location" label="位置" width="150" />

        <el-table-column prop="lastUpdateTime" label="最后更新" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.lastUpdateTime) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewDevice(row)">
              查看
            </el-button>
            <el-button size="small" type="primary" @click="editDevice(row)">
              编辑
            </el-button>
            <el-button size="small" type="warning" @click="controlDevice(row)">
              控制
            </el-button>
            <el-button size="small" type="danger" @click="deleteDevice(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="totalDevices"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 添加/编辑设备对话框 -->
    <DeviceDialog
      v-model="showAddDialog"
      :device="currentDevice"
      @save="handleDeviceSave"
    />

    <!-- 设备控制对话框 -->
    <DeviceControlDialog
      v-model="showControlDialog"
      :device="currentDevice"
      @control="handleDeviceControl"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Download, Refresh, Search } from '@element-plus/icons-vue'
import { useDeviceStore } from '@/stores/device'
import { Device, DeviceType, DeviceStatus } from '@/types/device'
import DeviceDialog from '@/components/DeviceDialog.vue'
import DeviceControlDialog from '@/components/DeviceControlDialog.vue'

// 响应式数据
const loading = ref(false)
const searchQuery = ref('')
const deviceTypeFilter = ref('')
const statusFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const selectedDevices = ref<Device[]>([])
const showAddDialog = ref(false)
const showControlDialog = ref(false)
const currentDevice = ref<Device | null>(null)

// 状态管理
const deviceStore = useDeviceStore()

// 计算属性
const filteredDevices = computed(() => {
  let devices = deviceStore.devices

  // 搜索过滤
  if (searchQuery.value) {
    devices = devices.filter(device =>
      device.deviceName.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      device.deviceId.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  // 设备类型过滤
  if (deviceTypeFilter.value) {
    devices = devices.filter(device => device.deviceType === deviceTypeFilter.value)
  }

  // 状态过滤
  if (statusFilter.value) {
    devices = devices.filter(device => device.status === statusFilter.value)
  }

  return devices
})

const totalDevices = computed(() => filteredDevices.value.length)

// 常量数据
const deviceTypes = [
  { label: '逆变器', value: 'inverter' },
  { label: '汇流箱', value: 'combiner_box' },
  { label: '变压器', value: 'transformer' },
  { label: '传感器', value: 'sensor' }
]

const deviceStatuses = [
  { label: '在线', value: 'online' },
  { label: '离线', value: 'offline' },
  { label: '故障', value: 'fault' },
  { label: '维护', value: 'maintenance' }
]

// 生命周期
onMounted(() => {
  loadDevices()
})

// 方法
const loadDevices = async () => {
  loading.value = true
  try {
    await deviceStore.fetchDevices()
  } catch (error) {
    ElMessage.error('加载设备列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
}

const handleSelectionChange = (selection: Device[]) => {
  selectedDevices.value = selection
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

const viewDevice = (device: Device) => {
  // 跳转到设备详情页面
  router.push(`/devices/${device.deviceId}`)
}

const editDevice = (device: Device) => {
  currentDevice.value = { ...device }
  showAddDialog.value = true
}

const controlDevice = (device: Device) => {
  currentDevice.value = device
  showControlDialog.value = true
}

const deleteDevice = async (device: Device) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除设备 "${device.deviceName}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deviceStore.deleteDevice(device.deviceId)
    ElMessage.success('设备删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('设备删除失败')
    }
  }
}

const handleDeviceSave = async (device: Device) => {
  try {
    if (device.deviceId) {
      await deviceStore.updateDevice(device)
      ElMessage.success('设备更新成功')
    } else {
      await deviceStore.createDevice(device)
      ElMessage.success('设备创建成功')
    }
    showAddDialog.value = false
    currentDevice.value = null
  } catch (error) {
    ElMessage.error('设备保存失败')
  }
}

const handleDeviceControl = async (command: any) => {
  try {
    await deviceStore.controlDevice(currentDevice.value!.deviceId, command)
    ElMessage.success('设备控制成功')
    showControlDialog.value = false
  } catch (error) {
    ElMessage.error('设备控制失败')
  }
}

const exportDevices = () => {
  // 导出设备列表
  const data = filteredDevices.value.map(device => ({
    设备ID: device.deviceId,
    设备名称: device.deviceName,
    设备类型: getDeviceTypeName(device.deviceType),
    状态: getStatusName(device.status),
    位置: device.location,
    最后更新: formatDateTime(device.lastUpdateTime)
  }))

  // 使用工具函数导出Excel
  exportToExcel(data, '设备列表')
}

const refreshDevices = () => {
  loadDevices()
}

// 工具方法
const getDeviceTypeColor = (type: string) => {
  const colors = {
    inverter: 'primary',
    combiner_box: 'success',
    transformer: 'warning',
    sensor: 'info'
  }
  return colors[type] || 'default'
}

const getDeviceTypeName = (type: string) => {
  const names = {
    inverter: '逆变器',
    combiner_box: '汇流箱',
    transformer: '变压器',
    sensor: '传感器'
  }
  return names[type] || type
}

const getStatusColor = (status: string) => {
  const colors = {
    online: 'success',
    offline: 'info',
    fault: 'danger',
    maintenance: 'warning'
  }
  return colors[status] || 'default'
}

const getStatusName = (status: string) => {
  const names = {
    online: '在线',
    offline: '离线',
    fault: '故障',
    maintenance: '维护'
  }
  return names[status] || status
}

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString()
}
</script>

<style lang="scss" scoped>
.device-management {
  padding: 20px;

  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .search-section {
      display: flex;
      gap: 10px;

      .el-input {
        width: 200px;
      }

      .el-select {
        width: 120px;
      }
    }

    .action-section {
      display: flex;
      gap: 10px;
    }
  }

  .device-table {
    margin-bottom: 20px;
  }

  .pagination {
    display: flex;
    justify-content: center;
  }
}
</style>
```