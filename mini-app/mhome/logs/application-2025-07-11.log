2025-07-11 16:23:05.253 [main] INFO  com.mhome.logging.LoggerDemo.initializeLogging(LoggerDemo.java:68) - 日志配置初始化完成
2025-07-11 16:23:05.276 [main] INFO  com.mhome.logging.LoggerDemo.demonstrateBasicLogging(LoggerDemo.java:75) - === 演示基本日志功能 ===
2025-07-11 16:23:05.277 [main] DEBUG com.mhome.logging.LoggerDemo.demonstrateBasicLogging(LoggerDemo.java:79) - 这是DEBUG级别的日志
2025-07-11 16:23:05.277 [main] INFO  com.mhome.logging.LoggerDemo.demonstrateBasicLogging(LoggerDemo.java:80) - 这是INFO级别的日志
2025-07-11 16:23:05.278 [main] WARN  com.mhome.logging.LoggerDemo.demonstrateBasicLogging(LoggerDemo.java:81) - 这是WARN级别的日志
2025-07-11 16:23:05.278 [main] ERROR com.mhome.logging.LoggerDemo.demonstrateBasicLogging(LoggerDemo.java:82) - 这是ERROR级别的日志
2025-07-11 16:23:05.278 [main] FATAL com.mhome.logging.LoggerDemo.demonstrateBasicLogging(LoggerDemo.java:83) - 这是FATAL级别的日志
2025-07-11 16:23:05.279 [main] INFO  com.mhome.logging.LoggerDemo.methodA(LoggerDemo.java:93) - 在methodA中记录日志
2025-07-11 16:23:05.279 [main] DEBUG com.mhome.logging.LoggerDemo.methodB(LoggerDemo.java:101) - 在methodB中记录调试信息
2025-07-11 16:23:05.281 [main] INFO  com.mhome.logging.LoggerDemo$InnerClass.logFromInnerClass(LoggerDemo.java:112) - 来自内部类的日志消息
2025-07-11 16:23:05.284 [main] INFO  com.mhome.logging.LoggerDemo.demonstrateExceptionLogging(LoggerDemo.java:120) - === 演示异常日志功能 ===
2025-07-11 16:23:05.285 [main] ERROR com.mhome.logging.LoggerDemo.demonstrateExceptionLogging(LoggerDemo.java:126) - 捕获到异常
java.io.IOException: 这是一个模拟的IO异常
	at com.mhome.logging.LoggerDemo.throwException(LoggerDemo.java:141)
	at com.mhome.logging.LoggerDemo.demonstrateExceptionLogging(LoggerDemo.java:124)
	at com.mhome.logging.LoggerDemo.main(LoggerDemo.java:33)

2025-07-11 16:23:05.286 [main] FATAL com.mhome.logging.LoggerDemo.demonstrateExceptionLogging(LoggerDemo.java:133) - 捕获到嵌套异常
java.lang.RuntimeException: 包装异常
	at com.mhome.logging.LoggerDemo.throwNestedExceptions(LoggerDemo.java:151)
	at com.mhome.logging.LoggerDemo.demonstrateExceptionLogging(LoggerDemo.java:131)
	at com.mhome.logging.LoggerDemo.main(LoggerDemo.java:33)
Caused by: java.lang.IllegalArgumentException: 原始异常
	at com.mhome.logging.LoggerDemo.throwNestedExceptions(LoggerDemo.java:149)
	... 2 more

2025-07-11 16:23:05.286 [main] INFO  com.mhome.logging.LoggerDemo.demonstrateMultiThreadLogging(LoggerDemo.java:159) - === 演示多线程日志功能 ===
2025-07-11 16:23:05.294 [pool-1-thread-3] INFO  com.mhome.logging.LoggerDemo.lambda$0(LoggerDemo.java:168) [TaskLogger-3] - 任务 3 开始执行
2025-07-11 16:23:05.294 [pool-1-thread-1] INFO  com.mhome.logging.LoggerDemo.lambda$0(LoggerDemo.java:168) [TaskLogger-1] - 任务 1 开始执行
2025-07-11 16:23:05.294 [pool-1-thread-2] INFO  com.mhome.logging.LoggerDemo.lambda$0(LoggerDemo.java:168) [TaskLogger-2] - 任务 2 开始执行
2025-07-11 16:23:05.449 [pool-1-thread-1] DEBUG com.mhome.logging.LoggerDemo.lambda$0(LoggerDemo.java:173) [TaskLogger-1] - 任务 1 正在处理数据...
2025-07-11 16:23:05.454 [pool-1-thread-1] INFO  com.mhome.logging.LoggerDemo.lambda$0(LoggerDemo.java:180) [TaskLogger-1] - 任务 1 执行完成
2025-07-11 16:23:05.456 [pool-1-thread-1] INFO  com.mhome.logging.LoggerDemo.lambda$0(LoggerDemo.java:168) [TaskLogger-4] - 任务 4 开始执行
2025-07-11 16:23:05.499 [pool-1-thread-2] DEBUG com.mhome.logging.LoggerDemo.lambda$0(LoggerDemo.java:173) [TaskLogger-2] - 任务 2 正在处理数据...
2025-07-11 16:23:05.500 [pool-1-thread-2] INFO  com.mhome.logging.LoggerDemo.lambda$0(LoggerDemo.java:180) [TaskLogger-2] - 任务 2 执行完成
2025-07-11 16:23:05.500 [pool-1-thread-2] INFO  com.mhome.logging.LoggerDemo.lambda$0(LoggerDemo.java:168) [TaskLogger-5] - 任务 5 开始执行
2025-07-11 16:23:05.545 [pool-1-thread-3] DEBUG com.mhome.logging.LoggerDemo.lambda$0(LoggerDemo.java:173) [TaskLogger-3] - 任务 3 正在处理数据...
2025-07-11 16:23:05.545 [pool-1-thread-3] WARN  com.mhome.logging.LoggerDemo.lambda$0(LoggerDemo.java:177) [TaskLogger-3] - 任务 3 遇到了一些问题，但可以继续
2025-07-11 16:23:05.546 [pool-1-thread-3] INFO  com.mhome.logging.LoggerDemo.lambda$0(LoggerDemo.java:180) [TaskLogger-3] - 任务 3 执行完成
2025-07-11 16:23:05.759 [pool-1-thread-1] DEBUG com.mhome.logging.LoggerDemo.lambda$0(LoggerDemo.java:173) [TaskLogger-4] - 任务 4 正在处理数据...
2025-07-11 16:23:05.760 [pool-1-thread-1] INFO  com.mhome.logging.LoggerDemo.lambda$0(LoggerDemo.java:180) [TaskLogger-4] - 任务 4 执行完成
2025-07-11 16:23:05.853 [pool-1-thread-2] DEBUG com.mhome.logging.LoggerDemo.lambda$0(LoggerDemo.java:173) [TaskLogger-5] - 任务 5 正在处理数据...
2025-07-11 16:23:05.854 [pool-1-thread-2] INFO  com.mhome.logging.LoggerDemo.lambda$0(LoggerDemo.java:180) [TaskLogger-5] - 任务 5 执行完成
2025-07-11 16:23:05.858 [main] INFO  com.mhome.logging.LoggerDemo.demonstratePackageLevelLogging(LoggerDemo.java:200) - === 演示包级别日志配置 ===
2025-07-11 16:23:05.858 [main] TRACE com.mhome.logging.LoggerDemo.demonstratePackageLevelLogging(LoggerDemo.java:211) - logging包的TRACE消息 - 应该显示
2025-07-11 16:23:05.858 [main] WARN  com.mhome.logging.LoggerDemo.demonstratePackageLevelLogging(LoggerDemo.java:213) [com.mhome.service.UserService] - service包的WARN消息 - 应该显示
2025-07-11 16:23:05.860 [main] INFO  com.mhome.logging.LoggerDemo.demonstratePackageLevelLogging(LoggerDemo.java:214) [com.mhome.dao.UserDao] - dao包的INFO消息 - 应该显示（使用全局级别）
2025-07-11 16:23:05.860 [main] INFO  com.mhome.logging.LoggerDemo.demonstrateFormattedLogging(LoggerDemo.java:221) - === 演示格式化日志功能 ===
2025-07-11 16:23:05.866 [main] INFO  com.mhome.logging.LoggerDemo.demonstrateFormattedLogging(LoggerDemo.java:227) - 用户信息: 姓名=张三, 年龄=25, 薪资=8500.50
2025-07-11 16:23:05.867 [main] DEBUG com.mhome.logging.LoggerDemo.demonstrateFormattedLogging(LoggerDemo.java:228) - 处理用户 张三 的请求，参数数量: 3
2025-07-11 16:23:05.868 [main] WARN  com.mhome.logging.LoggerDemo.demonstrateFormattedLogging(LoggerDemo.java:229) - 用户 张三 的薪资 8500.50 低于平均水平
2025-07-11 16:23:05.869 [main] INFO  com.mhome.logging.LoggerDemo.demonstrateFormattedLogging(LoggerDemo.java:232) - 系统状态: CPU使用率=45.6%, 内存使用率=78.2%, 磁盘使用率=23.8%
2025-07-11 16:23:05.870 [main] INFO  com.mhome.logging.LoggerDemo.showConfigurationInfo(LoggerDemo.java:240) - === 当前日志配置信息 ===
