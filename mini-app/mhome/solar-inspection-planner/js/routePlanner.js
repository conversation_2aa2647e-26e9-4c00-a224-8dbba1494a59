/**
 * 航线规划器 - 负责生成和优化巡检航线
 */
class RoutePlanner {
    constructor() {
        this.mapManager = null;
    }
    
    /**
     * 设置地图管理器引用
     */
    setMapManager(mapManager) {
        this.mapManager = mapManager;
    }
    
    /**
     * 生成巡检航线
     */
    async generateRoute(panels, params) {
        const { height = 50, speed = 5, overlap = 80, pattern = 'zigzag' } = params;
        
        if (!panels || panels.length === 0) {
            throw new Error('没有检测到光伏板');
        }
        
        try {
            let route = [];
            
            switch (pattern) {
                case 'zigzag':
                    route = this.generateZigzagRoute(panels, height, overlap);
                    break;
                case 'grid':
                    route = this.generateGridRoute(panels, height, overlap);
                    break;
                case 'perimeter':
                    route = this.generatePerimeterRoute(panels, height);
                    break;
                default:
                    throw new Error('不支持的航线模式');
            }
            
            // 添加起飞和降落点
            route = this.addTakeoffAndLandingPoints(route, height);
            
            // 设置航点属性
            route = this.setWaypointProperties(route, speed, height);
            
            return route;
            
        } catch (error) {
            throw new Error('航线生成失败: ' + error.message);
        }
    }
    
    /**
     * 生成之字形航线
     */
    generateZigzagRoute(panels, height, overlap) {
        // 计算覆盖区域的边界
        const bounds = this.calculateCoverageBounds(panels);
        
        // 计算航线间距
        const spacing = this.calculateLineSpacing(height, overlap);
        
        const route = [];
        const { minLat, maxLat, minLng, maxLng } = bounds;
        
        // 计算航线数量
        const latRange = maxLat - minLat;
        const numLines = Math.ceil(latRange / spacing) + 1;
        
        for (let i = 0; i < numLines; i++) {
            const lat = minLat + i * spacing;
            
            if (i % 2 === 0) {
                // 从西到东
                route.push({ lat, lng: minLng, altitude: height });
                route.push({ lat, lng: maxLng, altitude: height });
            } else {
                // 从东到西
                route.push({ lat, lng: maxLng, altitude: height });
                route.push({ lat, lng: minLng, altitude: height });
            }
        }
        
        return route;
    }
    
    /**
     * 生成网格状航线
     */
    generateGridRoute(panels, height, overlap) {
        const bounds = this.calculateCoverageBounds(panels);
        const spacing = this.calculateLineSpacing(height, overlap);
        
        const route = [];
        const { minLat, maxLat, minLng, maxLng } = bounds;
        
        // 先横向扫描
        const latRange = maxLat - minLat;
        const numLatLines = Math.ceil(latRange / spacing) + 1;
        
        for (let i = 0; i < numLatLines; i++) {
            const lat = minLat + i * spacing;
            
            if (i % 2 === 0) {
                route.push({ lat, lng: minLng, altitude: height });
                route.push({ lat, lng: maxLng, altitude: height });
            } else {
                route.push({ lat, lng: maxLng, altitude: height });
                route.push({ lat, lng: minLng, altitude: height });
            }
        }
        
        // 再纵向扫描
        const lngRange = maxLng - minLng;
        const numLngLines = Math.ceil(lngRange / spacing) + 1;
        
        for (let i = 0; i < numLngLines; i++) {
            const lng = minLng + i * spacing;
            
            if (i % 2 === 0) {
                route.push({ lat: minLat, lng, altitude: height });
                route.push({ lat: maxLat, lng, altitude: height });
            } else {
                route.push({ lat: maxLat, lng, altitude: height });
                route.push({ lat: minLat, lng, altitude: height });
            }
        }
        
        return route;
    }
    
    /**
     * 生成周边巡检航线
     */
    generatePerimeterRoute(panels, height) {
        const bounds = this.calculateCoverageBounds(panels);
        const { minLat, maxLat, minLng, maxLng } = bounds;
        
        // 扩展边界以确保完全覆盖
        const latMargin = (maxLat - minLat) * 0.1;
        const lngMargin = (maxLng - minLng) * 0.1;
        
        const route = [
            { lat: minLat - latMargin, lng: minLng - lngMargin, altitude: height },
            { lat: minLat - latMargin, lng: maxLng + lngMargin, altitude: height },
            { lat: maxLat + latMargin, lng: maxLng + lngMargin, altitude: height },
            { lat: maxLat + latMargin, lng: minLng - lngMargin, altitude: height },
            { lat: minLat - latMargin, lng: minLng - lngMargin, altitude: height }
        ];
        
        return route;
    }
    
    /**
     * 计算覆盖区域边界
     */
    calculateCoverageBounds(panels) {
        if (!this.mapManager || !this.mapManager.currentBounds) {
            // 使用面板边界计算
            let minLat = Infinity, maxLat = -Infinity;
            let minLng = Infinity, maxLng = -Infinity;
            
            panels.forEach(panel => {
                const bounds = this.mapManager.convertPixelToBounds(panel.bounds);
                const [[lat1, lng1], [lat2, lng2]] = bounds;
                
                minLat = Math.min(minLat, lat1, lat2);
                maxLat = Math.max(maxLat, lat1, lat2);
                minLng = Math.min(minLng, lng1, lng2);
                maxLng = Math.max(maxLng, lng1, lng2);
            });
            
            return { minLat, maxLat, minLng, maxLng };
        }
        
        // 使用图像边界
        const [[minLat, minLng], [maxLat, maxLng]] = this.mapManager.currentBounds;
        return { minLat, maxLat, minLng, maxLng };
    }
    
    /**
     * 计算航线间距
     */
    calculateLineSpacing(height, overlap) {
        // 根据飞行高度和重叠率计算航线间距
        // 假设相机视野角度为60度
        const fovAngle = 60 * Math.PI / 180; // 转换为弧度
        const groundCoverage = 2 * height * Math.tan(fovAngle / 2);
        const spacing = groundCoverage * (1 - overlap / 100);
        
        // 转换为地理坐标间距（粗略估算）
        const metersPerDegree = 111000; // 1度约等于111公里
        return spacing / metersPerDegree;
    }
    
    /**
     * 添加起飞和降落点
     */
    addTakeoffAndLandingPoints(route, height) {
        if (route.length === 0) return route;
        
        const firstPoint = route[0];
        const lastPoint = route[route.length - 1];
        
        // 计算起飞点（在第一个航点附近）
        const takeoffPoint = {
            lat: firstPoint.lat - 0.001, // 约100米
            lng: firstPoint.lng - 0.001,
            altitude: 0,
            action: 'takeoff'
        };
        
        // 计算降落点（在最后一个航点附近）
        const landingPoint = {
            lat: lastPoint.lat + 0.001,
            lng: lastPoint.lng + 0.001,
            altitude: 0,
            action: 'landing'
        };
        
        return [takeoffPoint, ...route, landingPoint];
    }
    
    /**
     * 设置航点属性
     */
    setWaypointProperties(route, speed, height) {
        return route.map((point, index) => ({
            ...point,
            id: index + 1,
            speed: speed,
            action: point.action || 'photo',
            heading: this.calculateHeading(route, index),
            gimbalPitch: -90, // 垂直向下拍摄
            stayTime: point.action === 'photo' ? 2 : 0 // 拍照停留时间
        }));
    }
    
    /**
     * 计算航向角
     */
    calculateHeading(route, index) {
        if (index >= route.length - 1) {
            return index > 0 ? this.calculateHeading(route, index - 1) : 0;
        }
        
        const current = route[index];
        const next = route[index + 1];
        
        const deltaLat = next.lat - current.lat;
        const deltaLng = next.lng - current.lng;
        
        let heading = Math.atan2(deltaLng, deltaLat) * 180 / Math.PI;
        if (heading < 0) heading += 360;
        
        return Math.round(heading);
    }
    
    /**
     * 优化航线
     */
    optimizeRoute(route) {
        if (!route || route.length < 3) return route;
        
        // 移除起飞和降落点进行优化
        const takeoffPoint = route[0];
        const landingPoint = route[route.length - 1];
        const waypointsToOptimize = route.slice(1, -1);
        
        // 使用最近邻算法优化路径
        const optimizedWaypoints = this.nearestNeighborOptimization(waypointsToOptimize);
        
        // 重新添加起飞和降落点
        return [takeoffPoint, ...optimizedWaypoints, landingPoint];
    }
    
    /**
     * 最近邻优化算法
     */
    nearestNeighborOptimization(waypoints) {
        if (waypoints.length <= 2) return waypoints;
        
        const optimized = [];
        const remaining = [...waypoints];
        
        // 从第一个点开始
        let current = remaining.shift();
        optimized.push(current);
        
        while (remaining.length > 0) {
            let nearestIndex = 0;
            let nearestDistance = this.calculateDistance(
                current.lat, current.lng,
                remaining[0].lat, remaining[0].lng
            );
            
            // 找到最近的点
            for (let i = 1; i < remaining.length; i++) {
                const distance = this.calculateDistance(
                    current.lat, current.lng,
                    remaining[i].lat, remaining[i].lng
                );
                
                if (distance < nearestDistance) {
                    nearestDistance = distance;
                    nearestIndex = i;
                }
            }
            
            // 移动到最近的点
            current = remaining.splice(nearestIndex, 1)[0];
            optimized.push(current);
        }
        
        return optimized;
    }
    
    /**
     * 计算两点间距离（米）
     */
    calculateDistance(lat1, lng1, lat2, lng2) {
        const R = 6371000; // 地球半径（米）
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLng = (lng2 - lng1) * Math.PI / 180;
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                Math.sin(dLng/2) * Math.sin(dLng/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
    }
    
    /**
     * 验证航线安全性
     */
    validateRoute(route) {
        const issues = [];
        
        // 检查航点数量
        if (route.length < 2) {
            issues.push('航线至少需要2个航点');
        }
        
        // 检查飞行高度
        route.forEach((point, index) => {
            if (point.altitude < 10 || point.altitude > 500) {
                issues.push(`航点 ${index + 1} 高度超出安全范围 (10-500m)`);
            }
        });
        
        // 检查航点间距
        for (let i = 1; i < route.length; i++) {
            const distance = this.calculateDistance(
                route[i-1].lat, route[i-1].lng,
                route[i].lat, route[i].lng
            );
            
            if (distance > 2000) { // 超过2公里
                issues.push(`航点 ${i} 到 ${i+1} 距离过远 (${Math.round(distance)}m)`);
            }
        }
        
        return {
            isValid: issues.length === 0,
            issues: issues
        };
    }
    
    /**
     * 计算航线统计信息
     */
    calculateRouteStats(route) {
        if (!route || route.length < 2) {
            return {
                totalDistance: 0,
                estimatedTime: 0,
                waypointCount: 0,
                coverageArea: 0
            };
        }
        
        let totalDistance = 0;
        let totalTime = 0;
        
        for (let i = 1; i < route.length; i++) {
            const distance = this.calculateDistance(
                route[i-1].lat, route[i-1].lng,
                route[i].lat, route[i].lng
            );
            
            totalDistance += distance;
            
            // 计算飞行时间（包括拍照停留时间）
            const speed = route[i].speed || 5; // m/s
            const flightTime = distance / speed;
            const stayTime = route[i].stayTime || 0;
            
            totalTime += flightTime + stayTime;
        }
        
        return {
            totalDistance: Math.round(totalDistance),
            estimatedTime: Math.round(totalTime / 60), // 转换为分钟
            waypointCount: route.length,
            coverageArea: this.calculateCoverageArea(route)
        };
    }
    
    /**
     * 计算覆盖面积
     */
    calculateCoverageArea(route) {
        // 简化计算：使用航线边界矩形面积
        if (route.length < 2) return 0;
        
        let minLat = route[0].lat, maxLat = route[0].lat;
        let minLng = route[0].lng, maxLng = route[0].lng;
        
        route.forEach(point => {
            minLat = Math.min(minLat, point.lat);
            maxLat = Math.max(maxLat, point.lat);
            minLng = Math.min(minLng, point.lng);
            maxLng = Math.max(maxLng, point.lng);
        });
        
        const latDistance = this.calculateDistance(minLat, minLng, maxLat, minLng);
        const lngDistance = this.calculateDistance(minLat, minLng, minLat, maxLng);
        
        return Math.round(latDistance * lngDistance); // 平方米
    }
}
