/**
 * 光伏板检测器 - 负责识别图像中的光伏板
 */
class PanelDetection {
    constructor() {
        this.mapManager = null;
        this.canvas = null;
        this.ctx = null;
        this.imageData = null;
    }
    
    /**
     * 设置地图管理器引用
     */
    setMapManager(mapManager) {
        this.mapManager = mapManager;
    }
    
    /**
     * 检测光伏板
     */
    async detectPanels(imageFile, options = {}) {
        const { sensitivity = 0.7, minArea = 10 } = options;
        
        try {
            // 加载图像到canvas
            await this.loadImageToCanvas(imageFile);
            
            // 预处理图像
            this.preprocessImage();
            
            // 检测矩形区域（模拟光伏板检测）
            const detectedRegions = this.detectRectangularRegions(sensitivity, minArea);
            
            // 过滤和优化检测结果
            const panels = this.filterAndOptimizePanels(detectedRegions, minArea);
            
            return panels;
            
        } catch (error) {
            throw new Error('光伏板检测失败: ' + error.message);
        }
    }
    
    /**
     * 加载图像到canvas
     */
    async loadImageToCanvas(imageFile) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            const reader = new FileReader();
            
            reader.onload = (e) => {
                img.onload = () => {
                    // 创建canvas
                    this.canvas = document.createElement('canvas');
                    this.ctx = this.canvas.getContext('2d');
                    
                    // 设置canvas尺寸（限制最大尺寸以提高性能）
                    const maxSize = 1024;
                    const scale = Math.min(maxSize / img.width, maxSize / img.height, 1);
                    
                    this.canvas.width = img.width * scale;
                    this.canvas.height = img.height * scale;
                    
                    // 绘制图像
                    this.ctx.drawImage(img, 0, 0, this.canvas.width, this.canvas.height);
                    
                    // 获取图像数据
                    this.imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
                    
                    resolve();
                };
                
                img.onerror = () => reject(new Error('图像加载失败'));
                img.src = e.target.result;
            };
            
            reader.onerror = () => reject(new Error('文件读取失败'));
            reader.readAsDataURL(imageFile);
        });
    }
    
    /**
     * 预处理图像
     */
    preprocessImage() {
        if (!this.imageData) return;
        
        const data = this.imageData.data;
        const width = this.imageData.width;
        const height = this.imageData.height;
        
        // 转换为灰度图像
        for (let i = 0; i < data.length; i += 4) {
            const gray = Math.round(0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2]);
            data[i] = gray;     // R
            data[i + 1] = gray; // G
            data[i + 2] = gray; // B
            // Alpha通道保持不变
        }
        
        // 应用高斯模糊减少噪声
        this.applyGaussianBlur(data, width, height);
        
        // 边缘检测
        this.applyEdgeDetection(data, width, height);
    }
    
    /**
     * 应用高斯模糊
     */
    applyGaussianBlur(data, width, height) {
        const kernel = [
            [1, 2, 1],
            [2, 4, 2],
            [1, 2, 1]
        ];
        const kernelSum = 16;
        
        const tempData = new Uint8ClampedArray(data);
        
        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                let sum = 0;
                
                for (let ky = -1; ky <= 1; ky++) {
                    for (let kx = -1; kx <= 1; kx++) {
                        const pixelIndex = ((y + ky) * width + (x + kx)) * 4;
                        sum += tempData[pixelIndex] * kernel[ky + 1][kx + 1];
                    }
                }
                
                const pixelIndex = (y * width + x) * 4;
                const blurredValue = Math.round(sum / kernelSum);
                data[pixelIndex] = blurredValue;
                data[pixelIndex + 1] = blurredValue;
                data[pixelIndex + 2] = blurredValue;
            }
        }
    }
    
    /**
     * 边缘检测（Sobel算子）
     */
    applyEdgeDetection(data, width, height) {
        const sobelX = [
            [-1, 0, 1],
            [-2, 0, 2],
            [-1, 0, 1]
        ];
        
        const sobelY = [
            [-1, -2, -1],
            [0, 0, 0],
            [1, 2, 1]
        ];
        
        const tempData = new Uint8ClampedArray(data);
        
        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                let gx = 0, gy = 0;
                
                for (let ky = -1; ky <= 1; ky++) {
                    for (let kx = -1; kx <= 1; kx++) {
                        const pixelIndex = ((y + ky) * width + (x + kx)) * 4;
                        const pixelValue = tempData[pixelIndex];
                        
                        gx += pixelValue * sobelX[ky + 1][kx + 1];
                        gy += pixelValue * sobelY[ky + 1][kx + 1];
                    }
                }
                
                const magnitude = Math.sqrt(gx * gx + gy * gy);
                const pixelIndex = (y * width + x) * 4;
                
                data[pixelIndex] = magnitude;
                data[pixelIndex + 1] = magnitude;
                data[pixelIndex + 2] = magnitude;
            }
        }
    }
    
    /**
     * 检测矩形区域
     */
    detectRectangularRegions(sensitivity, minArea) {
        const width = this.imageData.width;
        const height = this.imageData.height;
        const data = this.imageData.data;
        
        // 二值化
        const threshold = (1 - sensitivity) * 255;
        const binaryData = new Uint8Array(width * height);
        
        for (let i = 0; i < data.length; i += 4) {
            const gray = data[i];
            const index = i / 4;
            binaryData[index] = gray > threshold ? 1 : 0;
        }
        
        // 连通组件分析
        const components = this.findConnectedComponents(binaryData, width, height);
        
        // 从连通组件中提取矩形区域
        const rectangles = this.extractRectangles(components, width, height, minArea);
        
        return rectangles;
    }
    
    /**
     * 连通组件分析
     */
    findConnectedComponents(binaryData, width, height) {
        const visited = new Array(width * height).fill(false);
        const components = [];
        
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                const index = y * width + x;
                
                if (binaryData[index] === 1 && !visited[index]) {
                    const component = this.floodFill(binaryData, visited, x, y, width, height);
                    if (component.length > 50) { // 最小组件大小
                        components.push(component);
                    }
                }
            }
        }
        
        return components;
    }
    
    /**
     * 洪水填充算法
     */
    floodFill(binaryData, visited, startX, startY, width, height) {
        const stack = [{x: startX, y: startY}];
        const component = [];
        
        while (stack.length > 0) {
            const {x, y} = stack.pop();
            const index = y * width + x;
            
            if (x < 0 || x >= width || y < 0 || y >= height || 
                visited[index] || binaryData[index] === 0) {
                continue;
            }
            
            visited[index] = true;
            component.push({x, y});
            
            // 添加相邻像素
            stack.push({x: x + 1, y: y});
            stack.push({x: x - 1, y: y});
            stack.push({x: x, y: y + 1});
            stack.push({x: x, y: y - 1});
        }
        
        return component;
    }
    
    /**
     * 从连通组件提取矩形
     */
    extractRectangles(components, width, height, minArea) {
        const rectangles = [];
        
        components.forEach((component, index) => {
            // 计算边界框
            let minX = width, maxX = 0, minY = height, maxY = 0;
            
            component.forEach(point => {
                minX = Math.min(minX, point.x);
                maxX = Math.max(maxX, point.x);
                minY = Math.min(minY, point.y);
                maxY = Math.max(maxY, point.y);
            });
            
            const rectWidth = maxX - minX;
            const rectHeight = maxY - minY;
            const area = rectWidth * rectHeight;
            
            // 检查是否符合矩形特征
            const aspectRatio = rectWidth / rectHeight;
            const fillRatio = component.length / area;
            
            // 光伏板通常是矩形，长宽比在合理范围内
            if (area >= minArea && 
                aspectRatio > 0.5 && aspectRatio < 5 && 
                fillRatio > 0.3) {
                
                rectangles.push({
                    bounds: {
                        x: minX,
                        y: minY,
                        width: rectWidth,
                        height: rectHeight
                    },
                    area: area,
                    confidence: Math.min(fillRatio * 2, 1.0),
                    componentSize: component.length
                });
            }
        });
        
        return rectangles;
    }
    
    /**
     * 过滤和优化检测结果
     */
    filterAndOptimizePanels(detectedRegions, minArea) {
        // 按置信度排序
        detectedRegions.sort((a, b) => b.confidence - a.confidence);
        
        // 移除重叠的检测结果
        const filteredPanels = [];
        
        detectedRegions.forEach(region => {
            let isOverlapping = false;
            
            for (const panel of filteredPanels) {
                if (this.calculateOverlap(region.bounds, panel.bounds) > 0.3) {
                    isOverlapping = true;
                    break;
                }
            }
            
            if (!isOverlapping && region.area >= minArea) {
                filteredPanels.push({
                    bounds: region.bounds,
                    area: this.pixelAreaToRealArea(region.area),
                    confidence: region.confidence,
                    id: filteredPanels.length + 1
                });
            }
        });
        
        return filteredPanels;
    }
    
    /**
     * 计算两个矩形的重叠率
     */
    calculateOverlap(rect1, rect2) {
        const x1 = Math.max(rect1.x, rect2.x);
        const y1 = Math.max(rect1.y, rect2.y);
        const x2 = Math.min(rect1.x + rect1.width, rect2.x + rect2.width);
        const y2 = Math.min(rect1.y + rect1.height, rect2.y + rect2.height);
        
        if (x2 <= x1 || y2 <= y1) return 0;
        
        const overlapArea = (x2 - x1) * (y2 - y1);
        const area1 = rect1.width * rect1.height;
        const area2 = rect2.width * rect2.height;
        const unionArea = area1 + area2 - overlapArea;
        
        return overlapArea / unionArea;
    }
    
    /**
     * 像素面积转换为实际面积
     */
    pixelAreaToRealArea(pixelArea) {
        // 这里需要根据图像的地理信息计算实际比例
        // 暂时使用估算值：1像素 ≈ 0.1m²
        return pixelArea * 0.1;
    }
}
