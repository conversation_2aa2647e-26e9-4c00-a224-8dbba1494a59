/**
 * 地图管理器 - 负责地图显示和交互
 */
class MapManager {
    constructor(containerId) {
        this.containerId = containerId;
        this.map = null;
        this.imageLayer = null;
        this.panelLayers = [];
        this.routeLayer = null;
        this.waypointLayers = [];
        this.gridLayer = null;
        this.currentBounds = null;
        
        this.initMap();
        this.bindMapEvents();
    }
    
    /**
     * 初始化地图
     */
    initMap() {
        // 创建地图实例
        this.map = L.map(this.containerId, {
            center: [39.9042, 116.4074], // 默认北京坐标
            zoom: 15,
            zoomControl: false,
            attributionControl: false
        });
        
        // 添加默认底图
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(this.map);
        
        // 添加比例尺
        L.control.scale({
            position: 'bottomright',
            metric: true,
            imperial: false
        }).addTo(this.map);
    }
    
    /**
     * 绑定地图事件
     */
    bindMapEvents() {
        // 鼠标移动显示坐标
        this.map.on('mousemove', (e) => {
            const lat = e.latlng.lat.toFixed(6);
            const lng = e.latlng.lng.toFixed(6);
            document.getElementById('coordinateDisplay').textContent = `坐标: ${lat}, ${lng}`;
        });
        
        // 缩放级别变化
        this.map.on('zoomend', () => {
            const zoom = this.map.getZoom();
            document.getElementById('scaleDisplay').textContent = `缩放: ${zoom}`;
        });
        
        // 地图点击事件（用于添加航点）
        this.map.on('click', (e) => {
            if (this.addingWaypoint) {
                this.addWaypointAt(e.latlng);
                this.addingWaypoint = false;
                this.map.getContainer().style.cursor = '';
            }
        });
    }
    
    /**
     * 加载图像到地图
     */
    async loadImage(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                try {
                    const img = new Image();
                    img.onload = () => {
                        // 移除之前的图像层
                        if (this.imageLayer) {
                            this.map.removeLayer(this.imageLayer);
                        }
                        
                        // 计算图像边界（这里使用模拟坐标，实际应用中需要从EXIF或其他元数据获取）
                        const bounds = this.calculateImageBounds(img);
                        this.currentBounds = bounds;
                        
                        // 创建图像覆盖层
                        this.imageLayer = L.imageOverlay(e.target.result, bounds, {
                            opacity: 0.8,
                            interactive: false
                        }).addTo(this.map);
                        
                        // 调整地图视图到图像范围
                        this.map.fitBounds(bounds);
                        
                        resolve();
                    };
                    
                    img.onerror = () => {
                        reject(new Error('图像加载失败'));
                    };
                    
                    img.src = e.target.result;
                    
                } catch (error) {
                    reject(error);
                }
            };
            
            reader.onerror = () => {
                reject(new Error('文件读取失败'));
            };
            
            reader.readAsDataURL(file);
        });
    }
    
    /**
     * 计算图像边界
     */
    calculateImageBounds(img) {
        // 这里使用模拟的地理坐标
        // 实际应用中应该从图像的地理信息中获取真实坐标
        const centerLat = 39.9042;
        const centerLng = 116.4074;
        
        // 根据图像尺寸计算大致的地理范围
        const aspectRatio = img.width / img.height;
        const latRange = 0.01; // 约1公里
        const lngRange = latRange * aspectRatio;
        
        return [
            [centerLat - latRange/2, centerLng - lngRange/2],
            [centerLat + latRange/2, centerLng + lngRange/2]
        ];
    }
    
    /**
     * 显示检测到的光伏板
     */
    displayPanels(panels) {
        // 清除之前的光伏板层
        this.clearPanelLayers();
        
        panels.forEach((panel, index) => {
            const bounds = this.convertPixelToBounds(panel.bounds);
            
            // 创建矩形标记
            const rectangle = L.rectangle(bounds, {
                color: '#ff7800',
                weight: 2,
                fillColor: '#ff7800',
                fillOpacity: 0.3
            }).addTo(this.map);
            
            // 添加标签
            const center = rectangle.getBounds().getCenter();
            const marker = L.marker(center, {
                icon: L.divIcon({
                    className: 'panel-label',
                    html: `<div style="background: white; padding: 2px 6px; border-radius: 3px; font-size: 12px; border: 1px solid #ff7800;">P${index + 1}</div>`,
                    iconSize: [30, 20],
                    iconAnchor: [15, 10]
                })
            }).addTo(this.map);
            
            // 添加弹出信息
            rectangle.bindPopup(`
                <div>
                    <h4>光伏板组串 #${index + 1}</h4>
                    <p>面积: ${panel.area.toFixed(2)} m²</p>
                    <p>置信度: ${(panel.confidence * 100).toFixed(1)}%</p>
                </div>
            `);
            
            this.panelLayers.push(rectangle, marker);
        });
    }
    
    /**
     * 显示航线
     */
    displayRoute(route) {
        // 清除之前的航线
        this.clearRouteLayers();
        
        if (route.length < 2) return;
        
        // 创建航线路径
        const latlngs = route.map(point => [point.lat, point.lng]);
        
        this.routeLayer = L.polyline(latlngs, {
            color: '#2196F3',
            weight: 3,
            opacity: 0.8
        }).addTo(this.map);
        
        // 添加航点标记
        route.forEach((point, index) => {
            const marker = L.circleMarker([point.lat, point.lng], {
                radius: 8,
                fillColor: index === 0 ? '#4CAF50' : index === route.length - 1 ? '#F44336' : '#2196F3',
                color: 'white',
                weight: 2,
                opacity: 1,
                fillOpacity: 0.8
            }).addTo(this.map);
            
            // 添加航点编号
            const label = L.marker([point.lat, point.lng], {
                icon: L.divIcon({
                    className: 'waypoint-label',
                    html: `<div style="color: white; font-weight: bold; font-size: 10px; text-align: center; line-height: 16px;">${index + 1}</div>`,
                    iconSize: [16, 16],
                    iconAnchor: [8, 8]
                })
            }).addTo(this.map);
            
            // 添加弹出信息
            marker.bindPopup(`
                <div>
                    <h4>航点 #${index + 1}</h4>
                    <p>坐标: ${point.lat.toFixed(6)}, ${point.lng.toFixed(6)}</p>
                    <p>高度: ${point.altitude || 50}m</p>
                    <p>动作: ${point.action || '拍照'}</p>
                </div>
            `);
            
            this.waypointLayers.push(marker, label);
        });
        
        // 调整视图到航线范围
        this.map.fitBounds(this.routeLayer.getBounds(), { padding: [20, 20] });
    }
    
    /**
     * 像素坐标转换为地理边界
     */
    convertPixelToBounds(pixelBounds) {
        if (!this.currentBounds) {
            return [[0, 0], [0, 0]];
        }
        
        const [[minLat, minLng], [maxLat, maxLng]] = this.currentBounds;
        const latRange = maxLat - minLat;
        const lngRange = maxLng - minLng;
        
        // 假设图像尺寸为1000x1000像素（实际应该从图像获取）
        const imageWidth = 1000;
        const imageHeight = 1000;
        
        const lat1 = minLat + (pixelBounds.y / imageHeight) * latRange;
        const lng1 = minLng + (pixelBounds.x / imageWidth) * lngRange;
        const lat2 = minLat + ((pixelBounds.y + pixelBounds.height) / imageHeight) * latRange;
        const lng2 = minLng + ((pixelBounds.x + pixelBounds.width) / imageWidth) * lngRange;
        
        return [[lat1, lng1], [lat2, lng2]];
    }
    
    /**
     * 启用航点添加模式
     */
    enableWaypointAddition() {
        this.addingWaypoint = true;
        this.map.getContainer().style.cursor = 'crosshair';
    }
    
    /**
     * 启用航点删除模式
     */
    enableWaypointDeletion() {
        this.deletingWaypoint = true;
        this.map.getContainer().style.cursor = 'not-allowed';
        
        // 为航点添加点击事件
        this.waypointLayers.forEach(layer => {
            if (layer instanceof L.CircleMarker) {
                layer.on('click', (e) => {
                    if (this.deletingWaypoint) {
                        this.removeWaypoint(layer);
                        this.deletingWaypoint = false;
                        this.map.getContainer().style.cursor = '';
                    }
                });
            }
        });
    }
    
    /**
     * 在指定位置添加航点
     */
    addWaypointAt(latlng) {
        // 这里应该调用应用程序的方法来更新航线数据
        console.log('添加航点:', latlng);
    }
    
    /**
     * 移除航点
     */
    removeWaypoint(marker) {
        this.map.removeLayer(marker);
        // 这里应该调用应用程序的方法来更新航线数据
        console.log('删除航点');
    }
    
    /**
     * 清除光伏板层
     */
    clearPanelLayers() {
        this.panelLayers.forEach(layer => {
            this.map.removeLayer(layer);
        });
        this.panelLayers = [];
    }
    
    /**
     * 清除航线层
     */
    clearRouteLayers() {
        if (this.routeLayer) {
            this.map.removeLayer(this.routeLayer);
            this.routeLayer = null;
        }
        
        this.waypointLayers.forEach(layer => {
            this.map.removeLayer(layer);
        });
        this.waypointLayers = [];
    }
    
    /**
     * 切换网格线显示
     */
    toggleGridLines(show) {
        if (show && !this.gridLayer) {
            this.gridLayer = L.layerGroup().addTo(this.map);
            this.drawGridLines();
        } else if (!show && this.gridLayer) {
            this.map.removeLayer(this.gridLayer);
            this.gridLayer = null;
        }
    }
    
    /**
     * 绘制网格线
     */
    drawGridLines() {
        if (!this.currentBounds || !this.gridLayer) return;
        
        const [[minLat, minLng], [maxLat, maxLng]] = this.currentBounds;
        const latStep = (maxLat - minLat) / 10;
        const lngStep = (maxLng - minLng) / 10;
        
        // 绘制纬度线
        for (let i = 0; i <= 10; i++) {
            const lat = minLat + i * latStep;
            L.polyline([[lat, minLng], [lat, maxLng]], {
                color: '#666',
                weight: 1,
                opacity: 0.5
            }).addTo(this.gridLayer);
        }
        
        // 绘制经度线
        for (let i = 0; i <= 10; i++) {
            const lng = minLng + i * lngStep;
            L.polyline([[minLat, lng], [maxLat, lng]], {
                color: '#666',
                weight: 1,
                opacity: 0.5
            }).addTo(this.gridLayer);
        }
    }
    
    /**
     * 切换坐标显示
     */
    toggleCoordinates(show) {
        const display = document.getElementById('coordinateDisplay');
        display.style.display = show ? 'block' : 'none';
    }
    
    /**
     * 切换图层
     */
    toggleLayers() {
        // 实现图层切换逻辑
        console.log('切换图层');
    }
    
    /**
     * 地图控制方法
     */
    zoomIn() {
        this.map.zoomIn();
    }
    
    zoomOut() {
        this.map.zoomOut();
    }
    
    resetView() {
        if (this.currentBounds) {
            this.map.fitBounds(this.currentBounds);
        }
    }
}
