/**
 * 文件导出器 - 负责导出航线文件
 */
class FileExporter {
    constructor() {
        this.mapManager = null;
    }
    
    /**
     * 设置地图管理器引用
     */
    setMapManager(mapManager) {
        this.mapManager = mapManager;
    }
    
    /**
     * 导出航线文件
     */
    async exportRoute(route, missionName, format) {
        if (!route || route.length === 0) {
            throw new Error('没有可导出的航线');
        }
        
        try {
            switch (format) {
                case 'kmz':
                    await this.exportKMZ(route, missionName);
                    break;
                case 'waypoints':
                    await this.exportWaypoints(route, missionName);
                    break;
                case 'litchi':
                    await this.exportLitchi(route, missionName);
                    break;
                default:
                    throw new Error('不支持的导出格式');
            }
        } catch (error) {
            throw new Error('文件导出失败: ' + error.message);
        }
    }
    
    /**
     * 导出KMZ格式（大疆兼容）
     */
    async exportKMZ(route, missionName) {
        // 创建KML内容
        const kmlContent = this.generateKML(route, missionName);
        
        // 创建ZIP文件
        const zip = new JSZip();
        zip.file('doc.kml', kmlContent);
        
        // 生成并下载文件
        const content = await zip.generateAsync({ type: 'blob' });
        this.downloadFile(content, `${missionName}.kmz`, 'application/vnd.google-earth.kmz');
    }
    
    /**
     * 生成KML内容
     */
    generateKML(route, missionName) {
        const waypoints = route.map((point, index) => {
            return `
                <Placemark>
                    <name>WP${index + 1}</name>
                    <description>
                        <![CDATA[
                        <table>
                            <tr><td>高度:</td><td>${point.altitude}m</td></tr>
                            <tr><td>速度:</td><td>${point.speed}m/s</td></tr>
                            <tr><td>动作:</td><td>${point.action}</td></tr>
                            <tr><td>航向:</td><td>${point.heading}°</td></tr>
                        </table>
                        ]]>
                    </description>
                    <styleUrl>#waypoint-style</styleUrl>
                    <Point>
                        <altitudeMode>absolute</altitudeMode>
                        <coordinates>${point.lng},${point.lat},${point.altitude}</coordinates>
                    </Point>
                </Placemark>`;
        }).join('');
        
        const routeLine = `
            <Placemark>
                <name>航线路径</name>
                <styleUrl>#route-style</styleUrl>
                <LineString>
                    <altitudeMode>absolute</altitudeMode>
                    <coordinates>
                        ${route.map(p => `${p.lng},${p.lat},${p.altitude}`).join(' ')}
                    </coordinates>
                </LineString>
            </Placemark>`;
        
        return `<?xml version="1.0" encoding="UTF-8"?>
<kml xmlns="http://www.opengis.net/kml/2.2">
    <Document>
        <name>${missionName}</name>
        <description>光伏板巡检航线 - 生成时间: ${new Date().toLocaleString()}</description>
        
        <Style id="waypoint-style">
            <IconStyle>
                <Icon>
                    <href>http://maps.google.com/mapfiles/kml/shapes/placemark_circle.png</href>
                </Icon>
                <scale>1.2</scale>
            </IconStyle>
            <LabelStyle>
                <scale>1.0</scale>
            </LabelStyle>
        </Style>
        
        <Style id="route-style">
            <LineStyle>
                <color>ff0000ff</color>
                <width>3</width>
            </LineStyle>
        </Style>
        
        <Folder>
            <name>航点</name>
            ${waypoints}
        </Folder>
        
        <Folder>
            <name>航线</name>
            ${routeLine}
        </Folder>
        
        <!-- 大疆航线扩展信息 -->
        <ExtendedData>
            <Data name="missionType">
                <value>waypoint</value>
            </Data>
            <Data name="waypointCount">
                <value>${route.length}</value>
            </Data>
            <Data name="totalDistance">
                <value>${this.calculateTotalDistance(route)}</value>
            </Data>
        </ExtendedData>
    </Document>
</kml>`;
    }
    
    /**
     * 导出通用航点格式
     */
    async exportWaypoints(route, missionName) {
        const waypointsData = {
            mission: {
                name: missionName,
                type: 'solar_inspection',
                created: new Date().toISOString(),
                version: '1.0'
            },
            waypoints: route.map((point, index) => ({
                id: index + 1,
                latitude: point.lat,
                longitude: point.lng,
                altitude: point.altitude,
                speed: point.speed,
                action: point.action,
                heading: point.heading,
                gimbalPitch: point.gimbalPitch,
                stayTime: point.stayTime
            })),
            statistics: {
                waypointCount: route.length,
                totalDistance: this.calculateTotalDistance(route),
                estimatedTime: this.calculateEstimatedTime(route)
            }
        };
        
        const jsonContent = JSON.stringify(waypointsData, null, 2);
        const blob = new Blob([jsonContent], { type: 'application/json' });
        this.downloadFile(blob, `${missionName}_waypoints.json`, 'application/json');
    }
    
    /**
     * 导出Litchi CSV格式
     */
    async exportLitchi(route, missionName) {
        const headers = [
            'latitude', 'longitude', 'altitude(m)', 'heading(deg)', 
            'curvesize(m)', 'rotationdir', 'gimbalmode', 'gimbalpitchangle',
            'actiontype1', 'actionparam1', 'actiontype2', 'actionparam2',
            'actiontype3', 'actionparam3', 'actiontype4', 'actionparam4',
            'actiontype5', 'actionparam5', 'actiontype6', 'actionparam6',
            'actiontype7', 'actionparam7', 'actiontype8', 'actionparam8',
            'actiontype9', 'actionparam9', 'actiontype10', 'actionparam10',
            'actiontype11', 'actionparam11', 'actiontype12', 'actionparam12',
            'actiontype13', 'actionparam13', 'actiontype14', 'actionparam14',
            'actiontype15', 'actionparam15', 'altitudemode', 'speed(m/s)',
            'poi_latitude', 'poi_longitude', 'poi_altitude(m)', 'poi_altitudemode'
        ];
        
        const csvRows = [headers.join(',')];
        
        route.forEach(point => {
            const row = [
                point.lat,
                point.lng,
                point.altitude,
                point.heading || 0,
                0, // curvesize
                0, // rotationdir
                0, // gimbalmode (0=disabled, 1=focus POI, 2=interpolate)
                point.gimbalPitch || -90,
                point.action === 'photo' ? 1 : 0, // actiontype1 (1=stay for, 5=take photo)
                point.action === 'photo' ? point.stayTime || 2 : 0, // actionparam1
                ...Array(28).fill(0), // 其他action参数
                1, // altitudemode (0=AGL, 1=MSL)
                point.speed || 5,
                0, 0, 0, 0 // POI参数
            ];
            csvRows.push(row.join(','));
        });
        
        const csvContent = csvRows.join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv' });
        this.downloadFile(blob, `${missionName}_litchi.csv`, 'text/csv');
    }
    
    /**
     * 导出大疆Pilot 2格式
     */
    async exportDJIPilot2(route, missionName) {
        const missionData = {
            "file_type": "dji_pilot",
            "version": "1.0.0",
            "mission_config": {
                "fly_to_waypoint_mode": "safely",
                "finish_action": "go_home",
                "exit_mission_on_rc_lost": "execute_lost_action",
                "execute_lost_action": "go_home",
                "take_off_security_height": 20,
                "global_transition_speed": 8,
                "drone_info": {
                    "drone_type": "M300 RTK",
                    "drone_sub_type": 0,
                    "gimbal_type": "Zenmuse H20T"
                }
            },
            "mission_waypoint": route.map((point, index) => ({
                "waypoint_index": index,
                "location": {
                    "latitude": point.lat,
                    "longitude": point.lng,
                    "height": point.altitude
                },
                "speed": point.speed || 5,
                "gimbal_pitch": point.gimbalPitch || -90,
                "gimbal_yaw": point.heading || 0,
                "aircraft_heading": point.heading || 0,
                "flight_path_mode": 0,
                "heading_mode": 0,
                "turn_mode": 0,
                "poi_index": -1,
                "photo_time_interval": -1,
                "photo_distance_interval": -1,
                "actions": this.generateWaypointActions(point)
            }))
        };
        
        const jsonContent = JSON.stringify(missionData, null, 2);
        const blob = new Blob([jsonContent], { type: 'application/json' });
        this.downloadFile(blob, `${missionName}_dji_pilot2.json`, 'application/json');
    }
    
    /**
     * 生成航点动作
     */
    generateWaypointActions(point) {
        const actions = [];
        
        if (point.action === 'photo') {
            actions.push({
                "action_type": 1, // 拍照
                "action_param": 0
            });
        }
        
        if (point.stayTime && point.stayTime > 0) {
            actions.push({
                "action_type": 0, // 悬停
                "action_param": point.stayTime * 1000 // 毫秒
            });
        }
        
        return actions;
    }
    
    /**
     * 计算总距离
     */
    calculateTotalDistance(route) {
        let totalDistance = 0;
        for (let i = 1; i < route.length; i++) {
            const prev = route[i - 1];
            const curr = route[i];
            totalDistance += this.calculateDistance(prev.lat, prev.lng, curr.lat, curr.lng);
        }
        return Math.round(totalDistance);
    }
    
    /**
     * 计算预计时间
     */
    calculateEstimatedTime(route) {
        let totalTime = 0;
        for (let i = 1; i < route.length; i++) {
            const prev = route[i - 1];
            const curr = route[i];
            const distance = this.calculateDistance(prev.lat, prev.lng, curr.lat, curr.lng);
            const speed = curr.speed || 5;
            totalTime += distance / speed + (curr.stayTime || 0);
        }
        return Math.round(totalTime / 60); // 转换为分钟
    }
    
    /**
     * 计算两点间距离
     */
    calculateDistance(lat1, lng1, lat2, lng2) {
        const R = 6371000;
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLng = (lng2 - lng1) * Math.PI / 180;
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                Math.sin(dLng/2) * Math.sin(dLng/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
    }
    
    /**
     * 下载文件
     */
    downloadFile(blob, filename, mimeType) {
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.style.display = 'none';
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // 清理URL对象
        setTimeout(() => URL.revokeObjectURL(url), 100);
    }
    
    /**
     * 导出航线报告
     */
    async exportReport(route, panels, missionName) {
        const stats = this.calculateRouteStatistics(route);
        const panelStats = this.calculatePanelStatistics(panels);
        
        const reportContent = `
# 光伏板巡检航线报告

## 任务信息
- **任务名称**: ${missionName}
- **生成时间**: ${new Date().toLocaleString()}
- **软件版本**: 光伏板场站巡检航线规划工具 v1.0

## 光伏板检测结果
- **检测到的光伏板数量**: ${panelStats.count}
- **总面积**: ${panelStats.totalArea.toFixed(2)} m²
- **平均面积**: ${panelStats.averageArea.toFixed(2)} m²
- **平均置信度**: ${(panelStats.averageConfidence * 100).toFixed(1)}%

## 航线统计
- **航点数量**: ${stats.waypointCount}
- **总飞行距离**: ${stats.totalDistance} m
- **预计飞行时间**: ${stats.estimatedTime} 分钟
- **覆盖面积**: ${stats.coverageArea} m²
- **平均飞行速度**: ${stats.averageSpeed.toFixed(1)} m/s
- **平均飞行高度**: ${stats.averageAltitude.toFixed(1)} m

## 航点详情
${route.map((point, index) => `
### 航点 ${index + 1}
- **坐标**: ${point.lat.toFixed(6)}, ${point.lng.toFixed(6)}
- **高度**: ${point.altitude} m
- **速度**: ${point.speed} m/s
- **航向**: ${point.heading}°
- **动作**: ${point.action}
- **停留时间**: ${point.stayTime || 0} 秒
`).join('')}

## 安全提醒
1. 飞行前请检查天气条件
2. 确保电池电量充足
3. 检查飞行区域是否有禁飞限制
4. 保持视距内飞行
5. 注意避让其他飞行器

---
*报告由光伏板场站巡检航线规划工具自动生成*
        `;
        
        const blob = new Blob([reportContent], { type: 'text/markdown' });
        this.downloadFile(blob, `${missionName}_report.md`, 'text/markdown');
    }
    
    /**
     * 计算航线统计信息
     */
    calculateRouteStatistics(route) {
        const totalDistance = this.calculateTotalDistance(route);
        const estimatedTime = this.calculateEstimatedTime(route);
        const averageSpeed = route.reduce((sum, p) => sum + (p.speed || 5), 0) / route.length;
        const averageAltitude = route.reduce((sum, p) => sum + p.altitude, 0) / route.length;
        
        return {
            waypointCount: route.length,
            totalDistance,
            estimatedTime,
            coverageArea: this.calculateCoverageArea(route),
            averageSpeed,
            averageAltitude
        };
    }
    
    /**
     * 计算光伏板统计信息
     */
    calculatePanelStatistics(panels) {
        if (!panels || panels.length === 0) {
            return { count: 0, totalArea: 0, averageArea: 0, averageConfidence: 0 };
        }
        
        const totalArea = panels.reduce((sum, panel) => sum + panel.area, 0);
        const averageArea = totalArea / panels.length;
        const averageConfidence = panels.reduce((sum, panel) => sum + panel.confidence, 0) / panels.length;
        
        return {
            count: panels.length,
            totalArea,
            averageArea,
            averageConfidence
        };
    }
    
    /**
     * 计算覆盖面积
     */
    calculateCoverageArea(route) {
        if (route.length < 2) return 0;
        
        let minLat = route[0].lat, maxLat = route[0].lat;
        let minLng = route[0].lng, maxLng = route[0].lng;
        
        route.forEach(point => {
            minLat = Math.min(minLat, point.lat);
            maxLat = Math.max(maxLat, point.lat);
            minLng = Math.min(minLng, point.lng);
            maxLng = Math.max(maxLng, point.lng);
        });
        
        const latDistance = this.calculateDistance(minLat, minLng, maxLat, minLng);
        const lngDistance = this.calculateDistance(minLat, minLng, minLat, maxLng);
        
        return Math.round(latDistance * lngDistance);
    }
}
