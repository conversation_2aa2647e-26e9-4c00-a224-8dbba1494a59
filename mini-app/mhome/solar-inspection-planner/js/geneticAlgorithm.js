/**
 * 遗传算法引擎 - 用于航线优化
 */
class GeneticAlgorithm {
    constructor(config) {
        this.config = {
            populationSize: config.populationSize || 100,
            mutationRate: config.mutationRate || 0.1,
            crossoverRate: config.crossoverRate || 0.8,
            elitismRate: config.elitismRate || 0.1,
            maxGenerations: config.maxGenerations || 1000,
            convergenceThreshold: config.convergenceThreshold || 0.001,
            selectionMethod: config.selectionMethod || 'tournament',
            crossoverMethod: config.crossoverMethod || 'order',
            mutationMethod: config.mutationMethod || 'swap'
        };
        
        this.population = [];
        this.fitness = [];
        this.bestIndividual = null;
        this.bestFitness = 0;
        this.generation = 0;
        this.convergenceHistory = [];
    }
    
    /**
     * 初始化种群
     */
    initializePopulation(waypoints) {
        this.population = [];
        
        for (let i = 0; i < this.config.populationSize; i++) {
            const individual = this.createRandomIndividual(waypoints);
            this.population.push(individual);
        }
        
        return this.population;
    }
    
    /**
     * 创建随机个体
     */
    createRandomIndividual(waypoints) {
        const individual = [...waypoints];
        
        // 保持起点和终点不变，只打乱中间的航点
        const middle = individual.slice(1, -1);
        this.shuffleArray(middle);
        
        return [individual[0], ...middle, individual[individual.length - 1]];
    }
    
    /**
     * 进化一代
     */
    evolve(population, fitness) {
        this.population = population;
        this.fitness = fitness;
        this.generation++;
        
        // 更新最佳个体
        this.updateBestIndividual();
        
        // 记录收敛历史
        this.recordConvergence();
        
        // 生成新种群
        const newPopulation = this.generateNewPopulation();
        
        return newPopulation;
    }
    
    /**
     * 更新最佳个体
     */
    updateBestIndividual() {
        const maxFitnessIndex = this.fitness.indexOf(Math.max(...this.fitness));
        
        if (this.fitness[maxFitnessIndex] > this.bestFitness) {
            this.bestFitness = this.fitness[maxFitnessIndex];
            this.bestIndividual = [...this.population[maxFitnessIndex]];
        }
    }
    
    /**
     * 记录收敛历史
     */
    recordConvergence() {
        const avgFitness = this.fitness.reduce((sum, f) => sum + f, 0) / this.fitness.length;
        const maxFitness = Math.max(...this.fitness);
        
        this.convergenceHistory.push({
            generation: this.generation,
            avgFitness,
            maxFitness,
            diversity: this.calculateDiversity()
        });
    }
    
    /**
     * 计算种群多样性
     */
    calculateDiversity() {
        let totalDistance = 0;
        let comparisons = 0;
        
        for (let i = 0; i < this.population.length; i++) {
            for (let j = i + 1; j < this.population.length; j++) {
                totalDistance += this.calculateIndividualDistance(
                    this.population[i], 
                    this.population[j]
                );
                comparisons++;
            }
        }
        
        return comparisons > 0 ? totalDistance / comparisons : 0;
    }
    
    /**
     * 计算个体间距离
     */
    calculateIndividualDistance(ind1, ind2) {
        let distance = 0;
        for (let i = 0; i < ind1.length; i++) {
            if (ind1[i].id !== ind2[i].id) {
                distance++;
            }
        }
        return distance / ind1.length;
    }
    
    /**
     * 生成新种群
     */
    generateNewPopulation() {
        const newPopulation = [];
        const eliteCount = Math.floor(this.config.populationSize * this.config.elitismRate);
        
        // 精英保留
        const elites = this.selectElites(eliteCount);
        newPopulation.push(...elites);
        
        // 生成剩余个体
        while (newPopulation.length < this.config.populationSize) {
            if (Math.random() < this.config.crossoverRate) {
                // 交叉产生后代
                const parent1 = this.selection();
                const parent2 = this.selection();
                const offspring = this.crossover(parent1, parent2);
                
                // 变异
                const mutatedOffspring = offspring.map(child => 
                    Math.random() < this.config.mutationRate ? this.mutate(child) : child
                );
                
                newPopulation.push(...mutatedOffspring);
            } else {
                // 直接选择
                newPopulation.push(this.selection());
            }
        }
        
        // 确保种群大小正确
        return newPopulation.slice(0, this.config.populationSize);
    }
    
    /**
     * 选择精英个体
     */
    selectElites(count) {
        const indexed = this.population.map((ind, idx) => ({ ind, fitness: this.fitness[idx] }));
        indexed.sort((a, b) => b.fitness - a.fitness);
        
        return indexed.slice(0, count).map(item => [...item.ind]);
    }
    
    /**
     * 选择操作
     */
    selection() {
        switch (this.config.selectionMethod) {
            case 'tournament':
                return this.tournamentSelection();
            case 'roulette':
                return this.rouletteWheelSelection();
            case 'rank':
                return this.rankSelection();
            default:
                return this.tournamentSelection();
        }
    }
    
    /**
     * 锦标赛选择
     */
    tournamentSelection(tournamentSize = 3) {
        const tournament = [];
        
        for (let i = 0; i < tournamentSize; i++) {
            const randomIndex = Math.floor(Math.random() * this.population.length);
            tournament.push({
                individual: this.population[randomIndex],
                fitness: this.fitness[randomIndex]
            });
        }
        
        tournament.sort((a, b) => b.fitness - a.fitness);
        return [...tournament[0].individual];
    }
    
    /**
     * 轮盘赌选择
     */
    rouletteWheelSelection() {
        const totalFitness = this.fitness.reduce((sum, f) => sum + f, 0);
        const randomValue = Math.random() * totalFitness;
        
        let cumulativeFitness = 0;
        for (let i = 0; i < this.population.length; i++) {
            cumulativeFitness += this.fitness[i];
            if (cumulativeFitness >= randomValue) {
                return [...this.population[i]];
            }
        }
        
        return [...this.population[this.population.length - 1]];
    }
    
    /**
     * 排名选择
     */
    rankSelection() {
        const indexed = this.population.map((ind, idx) => ({ ind, fitness: this.fitness[idx], idx }));
        indexed.sort((a, b) => b.fitness - a.fitness);
        
        // 分配排名权重
        const totalRank = (this.population.length * (this.population.length + 1)) / 2;
        const randomValue = Math.random() * totalRank;
        
        let cumulativeRank = 0;
        for (let i = 0; i < indexed.length; i++) {
            cumulativeRank += (this.population.length - i);
            if (cumulativeRank >= randomValue) {
                return [...indexed[i].ind];
            }
        }
        
        return [...indexed[0].ind];
    }
    
    /**
     * 交叉操作
     */
    crossover(parent1, parent2) {
        switch (this.config.crossoverMethod) {
            case 'order':
                return this.orderCrossover(parent1, parent2);
            case 'pmx':
                return this.partiallyMappedCrossover(parent1, parent2);
            case 'cycle':
                return this.cycleCrossover(parent1, parent2);
            case 'edge':
                return this.edgeRecombination(parent1, parent2);
            default:
                return this.orderCrossover(parent1, parent2);
        }
    }
    
    /**
     * 顺序交叉 (Order Crossover, OX)
     */
    orderCrossover(parent1, parent2) {
        const length = parent1.length;
        const start = 1; // 跳过起点
        const end = length - 1; // 跳过终点
        
        // 随机选择交叉区间
        const crossoverStart = start + Math.floor(Math.random() * (end - start - 1));
        const crossoverEnd = crossoverStart + 1 + Math.floor(Math.random() * (end - crossoverStart - 1));
        
        const child1 = new Array(length);
        const child2 = new Array(length);
        
        // 保持起点和终点
        child1[0] = parent1[0];
        child1[length - 1] = parent1[length - 1];
        child2[0] = parent2[0];
        child2[length - 1] = parent2[length - 1];
        
        // 复制交叉区间
        for (let i = crossoverStart; i <= crossoverEnd; i++) {
            child1[i] = parent1[i];
            child2[i] = parent2[i];
        }
        
        // 填充剩余位置
        this.fillRemainingPositions(child1, parent2, crossoverStart, crossoverEnd);
        this.fillRemainingPositions(child2, parent1, crossoverStart, crossoverEnd);
        
        return [child1, child2];
    }
    
    /**
     * 填充剩余位置
     */
    fillRemainingPositions(child, parent, crossoverStart, crossoverEnd) {
        const used = new Set();
        
        // 记录已使用的航点
        child.forEach(waypoint => {
            if (waypoint) used.add(waypoint.id);
        });
        
        let parentIndex = 1; // 从第二个位置开始
        for (let i = 1; i < child.length - 1; i++) {
            if (child[i] === undefined) {
                // 找到下一个未使用的航点
                while (parentIndex < parent.length - 1 && used.has(parent[parentIndex].id)) {
                    parentIndex++;
                }
                
                if (parentIndex < parent.length - 1) {
                    child[i] = parent[parentIndex];
                    used.add(parent[parentIndex].id);
                    parentIndex++;
                }
            }
        }
    }
    
    /**
     * 部分映射交叉 (Partially Mapped Crossover, PMX)
     */
    partiallyMappedCrossover(parent1, parent2) {
        const length = parent1.length;
        const child1 = [...parent1];
        const child2 = [...parent2];
        
        // 随机选择交叉区间（跳过起点和终点）
        const start = 1 + Math.floor(Math.random() * (length - 3));
        const end = start + 1 + Math.floor(Math.random() * (length - start - 2));
        
        // 创建映射关系
        const mapping1 = new Map();
        const mapping2 = new Map();
        
        for (let i = start; i <= end; i++) {
            mapping1.set(parent1[i].id, parent2[i]);
            mapping2.set(parent2[i].id, parent1[i]);
            
            child1[i] = parent2[i];
            child2[i] = parent1[i];
        }
        
        // 解决冲突
        this.resolvePMXConflicts(child1, mapping1, start, end);
        this.resolvePMXConflicts(child2, mapping2, start, end);
        
        return [child1, child2];
    }
    
    /**
     * 解决PMX冲突
     */
    resolvePMXConflicts(child, mapping, start, end) {
        for (let i = 1; i < child.length - 1; i++) {
            if (i >= start && i <= end) continue;
            
            let current = child[i];
            while (mapping.has(current.id)) {
                current = mapping.get(current.id);
            }
            child[i] = current;
        }
    }
    
    /**
     * 循环交叉 (Cycle Crossover, CX)
     */
    cycleCrossover(parent1, parent2) {
        const length = parent1.length;
        const child1 = new Array(length);
        const child2 = new Array(length);
        
        // 保持起点和终点
        child1[0] = parent1[0];
        child1[length - 1] = parent1[length - 1];
        child2[0] = parent2[0];
        child2[length - 1] = parent2[length - 1];
        
        const visited = new Array(length).fill(false);
        visited[0] = visited[length - 1] = true;
        
        let cycle = 0;
        
        for (let start = 1; start < length - 1; start++) {
            if (visited[start]) continue;
            
            // 开始新循环
            let current = start;
            const isEvenCycle = cycle % 2 === 0;
            
            do {
                visited[current] = true;
                
                if (isEvenCycle) {
                    child1[current] = parent1[current];
                    child2[current] = parent2[current];
                } else {
                    child1[current] = parent2[current];
                    child2[current] = parent1[current];
                }
                
                // 找到下一个位置
                const targetId = isEvenCycle ? parent2[current].id : parent1[current].id;
                current = parent1.findIndex((wp, idx) => idx > 0 && idx < length - 1 && wp.id === targetId);
                
            } while (current !== start && current !== -1);
            
            cycle++;
        }
        
        return [child1, child2];
    }
    
    /**
     * 边重组交叉 (Edge Recombination)
     */
    edgeRecombination(parent1, parent2) {
        // 构建边表
        const edgeTable = this.buildEdgeTable(parent1, parent2);
        
        const child = [parent1[0]]; // 从起点开始
        let current = parent1[0];
        
        while (child.length < parent1.length - 1) {
            const neighbors = edgeTable.get(current.id) || [];
            
            // 移除当前节点
            this.removeFromEdgeTable(edgeTable, current.id);
            
            // 选择下一个节点
            let next = null;
            if (neighbors.length > 0) {
                // 选择邻居数最少的节点
                next = neighbors.reduce((min, neighbor) => {
                    const neighborCount = (edgeTable.get(neighbor.id) || []).length;
                    const minCount = (edgeTable.get(min.id) || []).length;
                    return neighborCount < minCount ? neighbor : min;
                });
            } else {
                // 随机选择未访问的节点
                const unvisited = parent1.slice(1, -1).filter(wp => 
                    !child.some(visited => visited.id === wp.id)
                );
                if (unvisited.length > 0) {
                    next = unvisited[Math.floor(Math.random() * unvisited.length)];
                }
            }
            
            if (next) {
                child.push(next);
                current = next;
            } else {
                break;
            }
        }
        
        child.push(parent1[parent1.length - 1]); // 添加终点
        
        return [child, child]; // 边重组通常只产生一个后代
    }
    
    /**
     * 构建边表
     */
    buildEdgeTable(parent1, parent2) {
        const edgeTable = new Map();
        
        const addEdge = (from, to) => {
            if (!edgeTable.has(from.id)) {
                edgeTable.set(from.id, []);
            }
            const neighbors = edgeTable.get(from.id);
            if (!neighbors.some(n => n.id === to.id)) {
                neighbors.push(to);
            }
        };
        
        // 添加parent1的边
        for (let i = 0; i < parent1.length - 1; i++) {
            addEdge(parent1[i], parent1[i + 1]);
            addEdge(parent1[i + 1], parent1[i]);
        }
        
        // 添加parent2的边
        for (let i = 0; i < parent2.length - 1; i++) {
            addEdge(parent2[i], parent2[i + 1]);
            addEdge(parent2[i + 1], parent2[i]);
        }
        
        return edgeTable;
    }
    
    /**
     * 从边表中移除节点
     */
    removeFromEdgeTable(edgeTable, nodeId) {
        edgeTable.delete(nodeId);
        
        for (const [key, neighbors] of edgeTable) {
            const index = neighbors.findIndex(n => n.id === nodeId);
            if (index !== -1) {
                neighbors.splice(index, 1);
            }
        }
    }
    
    /**
     * 变异操作
     */
    mutate(individual) {
        switch (this.config.mutationMethod) {
            case 'swap':
                return this.swapMutation(individual);
            case 'insert':
                return this.insertMutation(individual);
            case 'invert':
                return this.inversionMutation(individual);
            case 'scramble':
                return this.scrambleMutation(individual);
            default:
                return this.swapMutation(individual);
        }
    }
    
    /**
     * 交换变异
     */
    swapMutation(individual) {
        const mutated = [...individual];
        const length = mutated.length;
        
        if (length <= 2) return mutated;
        
        // 随机选择两个位置（跳过起点和终点）
        const pos1 = 1 + Math.floor(Math.random() * (length - 2));
        let pos2 = 1 + Math.floor(Math.random() * (length - 2));
        
        while (pos2 === pos1) {
            pos2 = 1 + Math.floor(Math.random() * (length - 2));
        }
        
        // 交换
        [mutated[pos1], mutated[pos2]] = [mutated[pos2], mutated[pos1]];
        
        return mutated;
    }
    
    /**
     * 插入变异
     */
    insertMutation(individual) {
        const mutated = [...individual];
        const length = mutated.length;
        
        if (length <= 2) return mutated;
        
        // 随机选择一个元素和插入位置
        const fromPos = 1 + Math.floor(Math.random() * (length - 2));
        const toPos = 1 + Math.floor(Math.random() * (length - 2));
        
        if (fromPos !== toPos) {
            const element = mutated.splice(fromPos, 1)[0];
            mutated.splice(toPos, 0, element);
        }
        
        return mutated;
    }
    
    /**
     * 倒置变异
     */
    inversionMutation(individual) {
        const mutated = [...individual];
        const length = mutated.length;
        
        if (length <= 2) return mutated;
        
        // 随机选择一个区间
        const start = 1 + Math.floor(Math.random() * (length - 2));
        const end = start + Math.floor(Math.random() * (length - start - 1));
        
        // 倒置区间
        const segment = mutated.slice(start, end + 1).reverse();
        mutated.splice(start, end - start + 1, ...segment);
        
        return mutated;
    }
    
    /**
     * 扰乱变异
     */
    scrambleMutation(individual) {
        const mutated = [...individual];
        const length = mutated.length;
        
        if (length <= 2) return mutated;
        
        // 随机选择一个区间
        const start = 1 + Math.floor(Math.random() * (length - 2));
        const end = start + Math.floor(Math.random() * (length - start - 1));
        
        // 扰乱区间
        const segment = mutated.slice(start, end + 1);
        this.shuffleArray(segment);
        mutated.splice(start, end - start + 1, ...segment);
        
        return mutated;
    }
    
    /**
     * 检查收敛
     */
    hasConverged() {
        if (this.convergenceHistory.length < 10) return false;
        
        const recent = this.convergenceHistory.slice(-10);
        const fitnessVariance = this.calculateVariance(recent.map(h => h.maxFitness));
        
        return fitnessVariance < this.config.convergenceThreshold;
    }
    
    /**
     * 计算方差
     */
    calculateVariance(values) {
        const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
        const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
        return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;
    }
    
    /**
     * 获取统计信息
     */
    getStatistics() {
        return {
            generation: this.generation,
            bestFitness: this.bestFitness,
            bestIndividual: this.bestIndividual,
            convergenceHistory: this.convergenceHistory,
            hasConverged: this.hasConverged()
        };
    }
    
    /**
     * 工具函数：打乱数组
     */
    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { GeneticAlgorithm };
}
