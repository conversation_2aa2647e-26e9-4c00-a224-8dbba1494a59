/**
 * 光伏板场站巡检航线规划工具 - 主应用程序
 * 负责应用程序的初始化和全局状态管理
 */

class SolarInspectionApp {
    constructor() {
        this.mapManager = null;
        this.panelDetection = null;
        this.routePlanner = null;
        this.fileExporter = null;
        
        // 应用状态
        this.state = {
            imageLoaded: false,
            panelsDetected: false,
            routeGenerated: false,
            currentImage: null,
            detectedPanels: [],
            flightRoute: [],
            settings: {
                showGridLines: false,
                showCoordinates: true,
                autoThreshold: 0.6,
                defaultHeight: 50,
                safetyDistance: 10
            }
        };
        
        this.init();
    }
    
    /**
     * 初始化应用程序
     */
    init() {
        this.initializeComponents();
        this.bindEvents();
        this.loadSettings();
        this.updateUI();
        this.showStatus('应用程序已就绪');
    }
    
    /**
     * 初始化各个组件
     */
    initializeComponents() {
        // 初始化地图管理器
        this.mapManager = new MapManager('map');
        
        // 初始化光伏板检测器
        this.panelDetection = new PanelDetection();
        
        // 初始化航线规划器
        this.routePlanner = new RoutePlanner();
        
        // 初始化文件导出器
        this.fileExporter = new FileExporter();
        
        // 设置组件间的引用
        this.panelDetection.setMapManager(this.mapManager);
        this.routePlanner.setMapManager(this.mapManager);
        this.fileExporter.setMapManager(this.mapManager);
    }
    
    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 文件上传
        document.getElementById('imageUpload').addEventListener('change', (e) => {
            this.handleImageUpload(e);
        });
        
        // 光伏板检测
        document.getElementById('detectPanelsBtn').addEventListener('click', () => {
            this.detectPanels();
        });
        
        // 敏感度滑块
        document.getElementById('sensitivitySlider').addEventListener('input', (e) => {
            document.getElementById('sensitivityValue').textContent = e.target.value;
        });
        
        // 航线生成
        document.getElementById('generateRouteBtn').addEventListener('click', () => {
            this.generateRoute();
        });
        
        // 航线编辑
        document.getElementById('addWaypointBtn').addEventListener('click', () => {
            this.addWaypoint();
        });
        
        document.getElementById('deleteWaypointBtn').addEventListener('click', () => {
            this.deleteWaypoint();
        });
        
        document.getElementById('optimizeRouteBtn').addEventListener('click', () => {
            this.optimizeRoute();
        });
        
        // 文件导出
        document.getElementById('exportBtn').addEventListener('click', () => {
            this.exportRoute();
        });
        
        // 地图控件
        document.getElementById('zoomInBtn').addEventListener('click', () => {
            this.mapManager.zoomIn();
        });
        
        document.getElementById('zoomOutBtn').addEventListener('click', () => {
            this.mapManager.zoomOut();
        });
        
        document.getElementById('resetViewBtn').addEventListener('click', () => {
            this.mapManager.resetView();
        });
        
        document.getElementById('toggleLayersBtn').addEventListener('click', () => {
            this.toggleLayers();
        });
        
        // 模态对话框
        this.bindModalEvents();
    }
    
    /**
     * 绑定模态对话框事件
     */
    bindModalEvents() {
        // 帮助按钮
        document.getElementById('helpBtn').addEventListener('click', () => {
            document.getElementById('helpModal').style.display = 'block';
        });
        
        // 设置按钮
        document.getElementById('settingsBtn').addEventListener('click', () => {
            document.getElementById('settingsModal').style.display = 'block';
        });
        
        // 关闭按钮
        document.querySelectorAll('.close').forEach(closeBtn => {
            closeBtn.addEventListener('click', (e) => {
                e.target.closest('.modal').style.display = 'none';
            });
        });
        
        // 点击模态框外部关闭
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.style.display = 'none';
            }
        });
        
        // 设置项变更
        document.getElementById('showGridLines').addEventListener('change', (e) => {
            this.state.settings.showGridLines = e.target.checked;
            this.mapManager.toggleGridLines(e.target.checked);
        });
        
        document.getElementById('showCoordinates').addEventListener('change', (e) => {
            this.state.settings.showCoordinates = e.target.checked;
            this.mapManager.toggleCoordinates(e.target.checked);
        });
    }
    
    /**
     * 处理图像上传
     */
    async handleImageUpload(event) {
        const files = event.target.files;
        if (files.length === 0) return;
        
        this.showProgress('正在加载图像...', 0);
        
        try {
            const file = files[0];
            this.state.currentImage = file;
            
            // 加载图像到地图
            await this.mapManager.loadImage(file);
            
            this.state.imageLoaded = true;
            this.updateUI();
            this.showStatus(`已加载图像: ${file.name}`);
            this.hideProgress();
            
        } catch (error) {
            console.error('图像加载失败:', error);
            this.showStatus('图像加载失败: ' + error.message);
            this.hideProgress();
        }
    }
    
    /**
     * 检测光伏板
     */
    async detectPanels() {
        if (!this.state.imageLoaded) {
            this.showStatus('请先加载图像');
            return;
        }
        
        this.showProgress('正在识别光伏板...', 0);
        
        try {
            const sensitivity = parseFloat(document.getElementById('sensitivitySlider').value);
            const minArea = parseInt(document.getElementById('minAreaInput').value);
            
            const panels = await this.panelDetection.detectPanels(
                this.state.currentImage,
                { sensitivity, minArea }
            );
            
            this.state.detectedPanels = panels;
            this.state.panelsDetected = true;
            
            // 在地图上显示检测结果
            this.mapManager.displayPanels(panels);
            
            this.updateUI();
            this.showStatus(`检测到 ${panels.length} 个光伏板组串`);
            this.hideProgress();
            
        } catch (error) {
            console.error('光伏板检测失败:', error);
            this.showStatus('光伏板检测失败: ' + error.message);
            this.hideProgress();
        }
    }
    
    /**
     * 生成航线
     */
    async generateRoute() {
        if (!this.state.panelsDetected) {
            this.showStatus('请先检测光伏板');
            return;
        }
        
        this.showProgress('正在生成航线...', 0);
        
        try {
            const params = {
                height: parseInt(document.getElementById('flightHeight').value),
                speed: parseFloat(document.getElementById('flightSpeed').value),
                overlap: parseInt(document.getElementById('overlapRate').value),
                pattern: document.getElementById('flightPattern').value
            };
            
            const route = await this.routePlanner.generateRoute(
                this.state.detectedPanels,
                params
            );
            
            this.state.flightRoute = route;
            this.state.routeGenerated = true;
            
            // 在地图上显示航线
            this.mapManager.displayRoute(route);
            
            this.updateRouteInfo(route);
            this.updateUI();
            this.showStatus(`航线生成完成，共 ${route.length} 个航点`);
            this.hideProgress();
            
        } catch (error) {
            console.error('航线生成失败:', error);
            this.showStatus('航线生成失败: ' + error.message);
            this.hideProgress();
        }
    }
    
    /**
     * 添加航点
     */
    addWaypoint() {
        if (!this.state.routeGenerated) {
            this.showStatus('请先生成航线');
            return;
        }
        
        this.mapManager.enableWaypointAddition();
        this.showStatus('点击地图添加航点');
    }
    
    /**
     * 删除航点
     */
    deleteWaypoint() {
        if (!this.state.routeGenerated) {
            this.showStatus('请先生成航线');
            return;
        }
        
        this.mapManager.enableWaypointDeletion();
        this.showStatus('点击航点进行删除');
    }
    
    /**
     * 优化航线
     */
    async optimizeRoute() {
        if (!this.state.routeGenerated) {
            this.showStatus('请先生成航线');
            return;
        }

        this.showProgress('正在优化航线...', 0);

        try {
            const optimizedRoute = await this.routePlanner.optimizeRoute(this.state.flightRoute, {
                useAI: true,
                method: 'genetic'
            });
            this.state.flightRoute = optimizedRoute;

            this.mapManager.displayRoute(optimizedRoute);
            this.updateRouteInfo(optimizedRoute);
            this.showStatus('航线优化完成');
            this.hideProgress();

        } catch (error) {
            console.error('航线优化失败:', error);
            this.showStatus('航线优化失败: ' + error.message);
            this.hideProgress();
        }
    }
    
    /**
     * 导出航线
     */
    async exportRoute() {
        if (!this.state.routeGenerated) {
            this.showStatus('请先生成航线');
            return;
        }
        
        this.showProgress('正在导出文件...', 0);
        
        try {
            const missionName = document.getElementById('missionName').value || '光伏巡检任务';
            const format = document.getElementById('exportFormat').value;
            
            await this.fileExporter.exportRoute(
                this.state.flightRoute,
                missionName,
                format
            );
            
            this.showStatus('文件导出成功');
            this.hideProgress();
            
        } catch (error) {
            console.error('文件导出失败:', error);
            this.showStatus('文件导出失败: ' + error.message);
            this.hideProgress();
        }
    }
    
    /**
     * 切换图层显示
     */
    toggleLayers() {
        // 实现图层切换逻辑
        this.mapManager.toggleLayers();
    }
    
    /**
     * 更新航线信息显示
     */
    updateRouteInfo(route) {
        const waypointCount = route.length;
        const totalDistance = this.calculateTotalDistance(route);
        const speed = parseFloat(document.getElementById('flightSpeed').value);
        const estimatedTime = Math.round(totalDistance / speed / 60); // 分钟
        
        document.getElementById('waypointCount').textContent = waypointCount;
        document.getElementById('totalDistance').textContent = Math.round(totalDistance) + '米';
        document.getElementById('estimatedTime').textContent = estimatedTime + '分钟';
    }
    
    /**
     * 计算总距离
     */
    calculateTotalDistance(route) {
        let totalDistance = 0;
        for (let i = 1; i < route.length; i++) {
            const prev = route[i - 1];
            const curr = route[i];
            totalDistance += this.calculateDistance(prev.lat, prev.lng, curr.lat, curr.lng);
        }
        return totalDistance;
    }
    
    /**
     * 计算两点间距离（米）
     */
    calculateDistance(lat1, lng1, lat2, lng2) {
        const R = 6371000; // 地球半径（米）
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLng = (lng2 - lng1) * Math.PI / 180;
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                Math.sin(dLng/2) * Math.sin(dLng/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
    }
    
    /**
     * 更新UI状态
     */
    updateUI() {
        document.getElementById('detectPanelsBtn').disabled = !this.state.imageLoaded;
        document.getElementById('generateRouteBtn').disabled = !this.state.panelsDetected;
        document.getElementById('exportBtn').disabled = !this.state.routeGenerated;
        
        // 更新检测状态显示
        const statusText = this.state.panelsDetected 
            ? `已检测到 ${this.state.detectedPanels.length} 个光伏板组串`
            : this.state.imageLoaded ? '等待检测' : '请先加载图像';
        document.getElementById('detectionStatus').textContent = statusText;
    }
    
    /**
     * 显示状态消息
     */
    showStatus(message) {
        document.getElementById('statusMessage').textContent = message;
    }
    
    /**
     * 显示进度
     */
    showProgress(message, percent) {
        this.showStatus(message);
        document.getElementById('progressContainer').style.display = 'flex';
        document.getElementById('progressFill').style.width = percent + '%';
        document.getElementById('progressText').textContent = Math.round(percent) + '%';
    }
    
    /**
     * 隐藏进度
     */
    hideProgress() {
        document.getElementById('progressContainer').style.display = 'none';
    }
    
    /**
     * 加载设置
     */
    loadSettings() {
        const saved = localStorage.getItem('solarInspectionSettings');
        if (saved) {
            this.state.settings = { ...this.state.settings, ...JSON.parse(saved) };
        }
        
        // 应用设置到UI
        document.getElementById('showGridLines').checked = this.state.settings.showGridLines;
        document.getElementById('showCoordinates').checked = this.state.settings.showCoordinates;
        document.getElementById('autoThreshold').value = this.state.settings.autoThreshold;
        document.getElementById('defaultHeight').value = this.state.settings.defaultHeight;
        document.getElementById('safetyDistance').value = this.state.settings.safetyDistance;
    }
    
    /**
     * 保存设置
     */
    saveSettings() {
        localStorage.setItem('solarInspectionSettings', JSON.stringify(this.state.settings));
    }
}

// 应用程序启动
document.addEventListener('DOMContentLoaded', () => {
    window.solarApp = new SolarInspectionApp();
});
