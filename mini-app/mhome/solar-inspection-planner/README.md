# 光伏板场站巡检航线规划工具

一个基于Web的光伏板场站巡检航线规划工具，用于根据正射影像地图自动识别光伏板并生成大疆无人机可用的巡检航线。

## 功能特性

### 🎯 核心功能
- **正射影像导入**：支持JPG、PNG、TIFF、GeoTIFF等格式
- **AI智能识别**：集成深度学习、神经网络和传统算法的光伏板识别
- **智能航线规划**：支持遗传算法、模拟退火、蚁群算法等多种优化方法
- **可视化编辑**：交互式地图界面，支持航点的添加、删除和编辑
- **多格式导出**：支持KMZ、Waypoints、Litchi CSV等格式

### 📊 高级功能
- **智能优化算法**：集成遗传算法、模拟退火、蚁群算法等多种优化方法
- **AI增强识别**：深度学习模型、神经网络和集成学习提升识别精度
- **多目标优化**：同时优化飞行距离、能耗、安全性和覆盖率
- **自适应参数**：根据场景自动调整算法参数
- **实时性能监控**：算法收敛过程可视化和性能统计
- **设置保存**：本地保存用户偏好设置

## 技术架构

### 前端技术栈
- **HTML5/CSS3**：现代化响应式界面
- **JavaScript ES6+**：模块化架构设计
- **Leaflet.js**：交互式地图显示
- **Canvas API**：图像处理和分析
- **JSZip**：KMZ文件生成

### 核心模块
1. **MapManager**：地图显示和交互管理
2. **PanelDetection**：AI增强的光伏板识别算法
3. **RoutePlanner**：智能航线规划和多算法优化
4. **FileExporter**：多格式文件导出
5. **AIEnhanced**：AI算法引擎和智能优化
6. **NeuralNetwork**：神经网络和深度学习模型
7. **GeneticAlgorithm**：遗传算法优化引擎
8. **SolarInspectionApp**：主应用程序控制器

## 安装和使用

### 环境要求
- 现代浏览器（Chrome 80+、Firefox 75+、Safari 13+）
- 支持HTML5 Canvas和File API
- 网络连接（用于加载地图瓦片）

### 快速开始

1. **克隆项目**
```bash
git clone <repository-url>
cd solar-inspection-planner
```

2. **启动本地服务器**
```bash
# 使用Python
python -m http.server 8000

# 或使用Node.js
npx http-server

# 或使用PHP
php -S localhost:8000
```

3. **打开浏览器**
访问 `http://localhost:8000` 即可使用

### 使用步骤

#### 1. 导入正射影像
- 点击"选择正射影像文件"按钮
- 支持的格式：JPG、PNG、TIFF、GeoTIFF
- 建议使用高分辨率图像以获得更好的识别效果

#### 2. 光伏板识别
- 调整识别敏感度（0.1-1.0）
- 设置最小面积阈值（平方米）
- 点击"自动识别光伏板"开始检测
- 检测结果将在地图上以橙色矩形显示

#### 3. 航线规划
- 设置飞行参数：
  - **飞行高度**：10-500米
  - **飞行速度**：1-15米/秒
  - **重叠率**：50-90%
- 选择航线模式：
  - **之字形**：适合大面积规则扫描
  - **网格状**：双向扫描，覆盖更全面
  - **周边巡检**：沿边界飞行
- 点击"生成航线"

#### 4. 航线编辑
- **添加航点**：点击"添加航点"后在地图上点击
- **删除航点**：点击"删除航点"后点击要删除的航点
- **优化航线**：自动优化航点顺序

#### 5. 导出文件
- 输入任务名称
- 选择导出格式：
  - **KMZ**：大疆无人机标准格式
  - **Waypoints**：通用JSON格式
  - **Litchi CSV**：Litchi应用格式
- 点击"导出航线文件"

## 配置选项

### 地图设置
- **显示网格线**：在地图上显示参考网格
- **显示坐标**：实时显示鼠标位置坐标

### 识别设置
- **自动识别阈值**：调整自动识别的敏感度
- **最小面积**：过滤小于指定面积的检测结果

### 航线设置
- **默认飞行高度**：新建航线的默认高度
- **安全距离**：航点间的最小安全距离

## 文件格式说明

### KMZ格式
标准的Google Earth格式，包含：
- 航点坐标和属性
- 航线路径
- 大疆无人机扩展信息

### Waypoints JSON格式
```json
{
  "mission": {
    "name": "任务名称",
    "type": "solar_inspection",
    "created": "2024-01-15T10:30:00.000Z"
  },
  "waypoints": [
    {
      "id": 1,
      "latitude": 39.904200,
      "longitude": 116.407400,
      "altitude": 50,
      "speed": 5,
      "action": "photo",
      "heading": 90
    }
  ]
}
```

### Litchi CSV格式
兼容Litchi应用的CSV格式，包含完整的航点参数。

## 算法说明

### 光伏板识别算法
1. **图像预处理**
   - 灰度转换
   - 高斯模糊降噪
   - Sobel边缘检测

2. **特征检测**
   - 二值化处理
   - 连通组件分析
   - 矩形特征提取

3. **结果过滤**
   - 面积阈值过滤
   - 长宽比验证
   - 重叠检测去除

### 航线规划算法
1. **覆盖区域计算**
   - 基于检测结果计算边界
   - 考虑相机视野和重叠率

2. **航点生成**
   - 根据模式生成初始航点
   - 添加起飞和降落点
   - 设置航点属性

3. **路径优化**
   - 最近邻算法优化顺序
   - 减少总飞行距离
   - 保证飞行安全

## 注意事项

### 使用限制
- 图像文件大小建议不超过50MB
- 浏览器内存限制可能影响大图像处理
- 识别精度依赖于图像质量和分辨率

### 安全提醒
- 飞行前请检查当地法规和禁飞区
- 确保电池电量充足
- 保持视距内飞行
- 注意天气条件

### 最佳实践
- 使用高分辨率正射影像
- 在光照条件良好时拍摄的图像效果更好
- 根据实际场地调整飞行参数
- 导出前验证航线的合理性

## 故障排除

### 常见问题

**Q: 图像加载失败**
A: 检查文件格式是否支持，文件是否损坏

**Q: 光伏板识别效果不佳**
A: 调整识别敏感度，确保图像质量良好

**Q: 航线生成失败**
A: 确保已检测到光伏板，检查飞行参数设置

**Q: 文件导出失败**
A: 检查浏览器是否支持文件下载，清除浏览器缓存

### 性能优化
- 对于大图像，建议先缩放到合适尺寸
- 关闭不必要的浏览器标签页
- 使用现代浏览器以获得更好性能

## 开发说明

### 项目结构
```
solar-inspection-planner/
├── index.html              # 主页面
├── css/
│   └── styles.css          # 样式文件
├── js/
│   ├── app.js              # 主应用程序
│   ├── mapManager.js       # 地图管理
│   ├── panelDetection.js   # 光伏板识别
│   ├── routePlanner.js     # 航线规划
│   └── fileExporter.js     # 文件导出
└── README.md               # 说明文档
```

### 扩展开发
- 添加新的识别算法
- 支持更多导出格式
- 集成机器学习模型
- 添加云端处理能力

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱：[<EMAIL>]
- GitHub：[your-github-username]

---

*光伏板场站巡检航线规划工具 - 让无人机巡检更智能、更高效*
