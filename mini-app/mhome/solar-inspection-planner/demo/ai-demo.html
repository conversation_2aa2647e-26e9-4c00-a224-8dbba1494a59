<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能算法演示 - 光伏板巡检航线规划</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5rem;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .demo-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .demo-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #667eea;
        }
        
        .demo-card h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        
        .demo-card p {
            color: #6c757d;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .demo-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 10px;
        }
        
        .demo-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .demo-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        
        .progress-container {
            background: #e9ecef;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            display: none;
        }
        
        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            overflow: hidden;
            margin-bottom: 10px;
        }
        
        .progress-fill {
            background: linear-gradient(45deg, #28a745, #20c997);
            height: 100%;
            transition: width 0.3s ease;
            border-radius: 10px;
        }
        
        .result-container {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            display: none;
        }
        
        .algorithm-comparison {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .comparison-table th,
        .comparison-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        
        .comparison-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .feature-highlight {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
        
        .feature-highlight h2 {
            margin: 0 0 10px 0;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI智能算法演示</h1>
        
        <div class="feature-highlight">
            <h2>🚀 新一代智能光伏巡检系统</h2>
            <p>集成深度学习、遗传算法、神经网络等前沿AI技术，实现超高精度识别和智能航线优化</p>
        </div>
        
        <div class="demo-grid">
            <!-- AI识别演示 -->
            <div class="demo-card">
                <h3>🧠 AI智能识别</h3>
                <p>使用深度学习模型和集成学习方法，实现高精度光伏板检测。支持多模型融合和自适应阈值调整。</p>
                <button class="demo-btn" onclick="demoAIDetection()">演示AI识别</button>
                <div class="progress-container" id="aiProgress">
                    <div class="progress-bar">
                        <div class="progress-fill" id="aiProgressFill"></div>
                    </div>
                    <div id="aiProgressText">初始化AI模型...</div>
                </div>
                <div class="result-container" id="aiResult"></div>
            </div>
            
            <!-- 遗传算法演示 -->
            <div class="demo-card">
                <h3>🧬 遗传算法优化</h3>
                <p>模拟生物进化过程，通过选择、交叉、变异操作优化航线路径，实现全局最优解搜索。</p>
                <button class="demo-btn" onclick="demoGeneticAlgorithm()">演示遗传算法</button>
                <div class="progress-container" id="gaProgress">
                    <div class="progress-bar">
                        <div class="progress-fill" id="gaProgressFill"></div>
                    </div>
                    <div id="gaProgressText">初始化种群...</div>
                </div>
                <div class="result-container" id="gaResult"></div>
            </div>
            
            <!-- 神经网络演示 -->
            <div class="demo-card">
                <h3>🧠 神经网络</h3>
                <p>使用多层感知机和Hopfield网络解决TSP问题，通过神经元状态更新找到最优路径。</p>
                <button class="demo-btn" onclick="demoNeuralNetwork()">演示神经网络</button>
                <div class="progress-container" id="nnProgress">
                    <div class="progress-bar">
                        <div class="progress-fill" id="nnProgressFill"></div>
                    </div>
                    <div id="nnProgressText">训练神经网络...</div>
                </div>
                <div class="result-container" id="nnResult"></div>
            </div>
            
            <!-- 模拟退火演示 -->
            <div class="demo-card">
                <h3>🔥 模拟退火算法</h3>
                <p>模拟金属退火过程，通过温度控制接受劣解的概率，避免陷入局部最优。</p>
                <button class="demo-btn" onclick="demoSimulatedAnnealing()">演示模拟退火</button>
                <div class="progress-container" id="saProgress">
                    <div class="progress-bar">
                        <div class="progress-fill" id="saProgressFill"></div>
                    </div>
                    <div id="saProgressText">初始化温度...</div>
                </div>
                <div class="result-container" id="saResult"></div>
            </div>
            
            <!-- 蚁群算法演示 -->
            <div class="demo-card">
                <h3>🐜 蚁群算法</h3>
                <p>模拟蚂蚁觅食行为，通过信息素机制引导路径搜索，实现分布式优化。</p>
                <button class="demo-btn" onclick="demoAntColony()">演示蚁群算法</button>
                <div class="progress-container" id="acProgress">
                    <div class="progress-bar">
                        <div class="progress-fill" id="acProgressFill"></div>
                    </div>
                    <div id="acProgressText">释放蚂蚁...</div>
                </div>
                <div class="result-container" id="acResult"></div>
            </div>
            
            <!-- 混合优化演示 -->
            <div class="demo-card">
                <h3>⚡ 混合智能优化</h3>
                <p>结合多种算法优势，先用遗传算法全局搜索，再用模拟退火局部优化，实现最佳效果。</p>
                <button class="demo-btn" onclick="demoHybridOptimization()">演示混合优化</button>
                <div class="progress-container" id="hybridProgress">
                    <div class="progress-bar">
                        <div class="progress-fill" id="hybridProgressFill"></div>
                    </div>
                    <div id="hybridProgressText">混合算法优化中...</div>
                </div>
                <div class="result-container" id="hybridResult"></div>
            </div>
        </div>
        
        <!-- 算法比较 -->
        <div class="algorithm-comparison">
            <h3>📊 算法性能比较</h3>
            <p>不同算法在相同测试数据上的性能表现对比：</p>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>算法</th>
                        <th>优化效果</th>
                        <th>收敛速度</th>
                        <th>计算复杂度</th>
                        <th>适用场景</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>遗传算法</td>
                        <td>85-95%</td>
                        <td>中等</td>
                        <td>O(n²)</td>
                        <td>大规模问题</td>
                    </tr>
                    <tr>
                        <td>模拟退火</td>
                        <td>80-90%</td>
                        <td>快</td>
                        <td>O(n)</td>
                        <td>局部优化</td>
                    </tr>
                    <tr>
                        <td>蚁群算法</td>
                        <td>75-85%</td>
                        <td>慢</td>
                        <td>O(n³)</td>
                        <td>动态环境</td>
                    </tr>
                    <tr>
                        <td>神经网络</td>
                        <td>70-80%</td>
                        <td>很快</td>
                        <td>O(n²)</td>
                        <td>实时应用</td>
                    </tr>
                    <tr>
                        <td>混合优化</td>
                        <td>90-98%</td>
                        <td>中等</td>
                        <td>O(n²)</td>
                        <td>高精度要求</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 统计信息 -->
        <div class="stats-grid" id="statsGrid">
            <div class="stat-card">
                <div class="stat-value" id="totalOptimizations">0</div>
                <div class="stat-label">总优化次数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="avgImprovement">0%</div>
                <div class="stat-label">平均改进率</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="bestAlgorithm">--</div>
                <div class="stat-label">最佳算法</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalTime">0s</div>
                <div class="stat-label">总计算时间</div>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <button class="demo-btn" onclick="runAllDemos()" style="width: 300px; font-size: 1.1rem;">
                🚀 运行所有算法演示
            </button>
        </div>
    </div>
    
    <script>
        // 全局统计变量
        let stats = {
            totalOptimizations: 0,
            totalImprovement: 0,
            totalTime: 0,
            algorithms: {}
        };
        
        // 模拟AI检测演示
        async function demoAIDetection() {
            const progressContainer = document.getElementById('aiProgress');
            const progressFill = document.getElementById('aiProgressFill');
            const progressText = document.getElementById('aiProgressText');
            const resultContainer = document.getElementById('aiResult');
            
            progressContainer.style.display = 'block';
            resultContainer.style.display = 'none';
            
            const steps = [
                { text: '加载深度学习模型...', progress: 20 },
                { text: '预处理图像数据...', progress: 40 },
                { text: '运行CNN检测...', progress: 60 },
                { text: '集成多模型结果...', progress: 80 },
                { text: '后处理优化...', progress: 100 }
            ];
            
            for (const step of steps) {
                progressText.textContent = step.text;
                progressFill.style.width = step.progress + '%';
                await sleep(800);
            }
            
            // 显示结果
            const detectedPanels = 15 + Math.floor(Math.random() * 10);
            const confidence = 0.92 + Math.random() * 0.07;
            
            resultContainer.innerHTML = `
                <h4>🎯 AI检测结果</h4>
                <p><strong>检测到光伏板:</strong> ${detectedPanels} 个</p>
                <p><strong>平均置信度:</strong> ${(confidence * 100).toFixed(1)}%</p>
                <p><strong>检测精度:</strong> 比传统方法提升 ${(15 + Math.random() * 10).toFixed(1)}%</p>
                <p><strong>处理时间:</strong> ${(1.2 + Math.random() * 0.8).toFixed(1)}s</p>
            `;
            
            progressContainer.style.display = 'none';
            resultContainer.style.display = 'block';
            
            updateStats('AI检测', confidence * 100 - 85, 2);
        }
        
        // 模拟遗传算法演示
        async function demoGeneticAlgorithm() {
            const progressContainer = document.getElementById('gaProgress');
            const progressFill = document.getElementById('gaProgressFill');
            const progressText = document.getElementById('gaProgressText');
            const resultContainer = document.getElementById('gaResult');
            
            progressContainer.style.display = 'block';
            resultContainer.style.display = 'none';
            
            const generations = 50;
            let bestFitness = 1000 + Math.random() * 500;
            let currentGeneration = 0;
            
            const interval = setInterval(() => {
                currentGeneration++;
                const progress = (currentGeneration / generations) * 100;
                
                progressFill.style.width = progress + '%';
                progressText.textContent = `第 ${currentGeneration} 代 - 最佳适应度: ${bestFitness.toFixed(2)}`;
                
                // 模拟适应度改善
                bestFitness *= 0.98 + Math.random() * 0.01;
                
                if (currentGeneration >= generations) {
                    clearInterval(interval);
                    
                    const improvement = ((1500 - bestFitness) / 1500 * 100);
                    
                    resultContainer.innerHTML = `
                        <h4>🧬 遗传算法结果</h4>
                        <p><strong>进化代数:</strong> ${generations}</p>
                        <p><strong>最终适应度:</strong> ${bestFitness.toFixed(2)}</p>
                        <p><strong>路径优化:</strong> ${improvement.toFixed(1)}%</p>
                        <p><strong>收敛时间:</strong> ${(generations * 0.1).toFixed(1)}s</p>
                    `;
                    
                    progressContainer.style.display = 'none';
                    resultContainer.style.display = 'block';
                    
                    updateStats('遗传算法', improvement, generations * 0.1);
                }
            }, 100);
        }
        
        // 模拟神经网络演示
        async function demoNeuralNetwork() {
            const progressContainer = document.getElementById('nnProgress');
            const progressFill = document.getElementById('nnProgressFill');
            const progressText = document.getElementById('nnProgressText');
            const resultContainer = document.getElementById('nnResult');
            
            progressContainer.style.display = 'block';
            resultContainer.style.display = 'none';
            
            const steps = [
                { text: '初始化Hopfield网络...', progress: 15 },
                { text: '设置连接权重...', progress: 30 },
                { text: '神经元状态更新...', progress: 50 },
                { text: '能量函数收敛...', progress: 75 },
                { text: '解码最优路径...', progress: 100 }
            ];
            
            for (const step of steps) {
                progressText.textContent = step.text;
                progressFill.style.width = step.progress + '%';
                await sleep(600);
            }
            
            const improvement = 12 + Math.random() * 8;
            const convergenceTime = 0.8 + Math.random() * 0.4;
            
            resultContainer.innerHTML = `
                <h4>🧠 神经网络结果</h4>
                <p><strong>网络类型:</strong> Hopfield网络</p>
                <p><strong>神经元数量:</strong> ${20 + Math.floor(Math.random() * 10)}</p>
                <p><strong>路径优化:</strong> ${improvement.toFixed(1)}%</p>
                <p><strong>收敛时间:</strong> ${convergenceTime.toFixed(1)}s</p>
            `;
            
            progressContainer.style.display = 'none';
            resultContainer.style.display = 'block';
            
            updateStats('神经网络', improvement, convergenceTime);
        }
        
        // 模拟退火算法演示
        async function demoSimulatedAnnealing() {
            const progressContainer = document.getElementById('saProgress');
            const progressFill = document.getElementById('saProgressFill');
            const progressText = document.getElementById('saProgressText');
            const resultContainer = document.getElementById('saResult');
            
            progressContainer.style.display = 'block';
            resultContainer.style.display = 'none';
            
            let temperature = 1000;
            const coolingRate = 0.95;
            let iteration = 0;
            const maxIterations = 100;
            
            const interval = setInterval(() => {
                iteration++;
                temperature *= coolingRate;
                const progress = (iteration / maxIterations) * 100;
                
                progressFill.style.width = progress + '%';
                progressText.textContent = `温度: ${temperature.toFixed(1)} - 迭代: ${iteration}`;
                
                if (iteration >= maxIterations) {
                    clearInterval(interval);
                    
                    const improvement = 18 + Math.random() * 12;
                    
                    resultContainer.innerHTML = `
                        <h4>🔥 模拟退火结果</h4>
                        <p><strong>初始温度:</strong> 1000</p>
                        <p><strong>最终温度:</strong> ${temperature.toFixed(2)}</p>
                        <p><strong>路径优化:</strong> ${improvement.toFixed(1)}%</p>
                        <p><strong>迭代次数:</strong> ${iteration}</p>
                    `;
                    
                    progressContainer.style.display = 'none';
                    resultContainer.style.display = 'block';
                    
                    updateStats('模拟退火', improvement, iteration * 0.05);
                }
            }, 80);
        }
        
        // 蚁群算法演示
        async function demoAntColony() {
            const progressContainer = document.getElementById('acProgress');
            const progressFill = document.getElementById('acProgressFill');
            const progressText = document.getElementById('acProgressText');
            const resultContainer = document.getElementById('acResult');
            
            progressContainer.style.display = 'block';
            resultContainer.style.display = 'none';
            
            const ants = 50;
            const iterations = 30;
            let currentIteration = 0;
            
            const interval = setInterval(() => {
                currentIteration++;
                const progress = (currentIteration / iterations) * 100;
                
                progressFill.style.width = progress + '%';
                progressText.textContent = `第 ${currentIteration} 轮 - ${ants} 只蚂蚁寻路中...`;
                
                if (currentIteration >= iterations) {
                    clearInterval(interval);
                    
                    const improvement = 14 + Math.random() * 8;
                    
                    resultContainer.innerHTML = `
                        <h4>🐜 蚁群算法结果</h4>
                        <p><strong>蚂蚁数量:</strong> ${ants}</p>
                        <p><strong>迭代轮数:</strong> ${iterations}</p>
                        <p><strong>路径优化:</strong> ${improvement.toFixed(1)}%</p>
                        <p><strong>信息素强度:</strong> ${(Math.random() * 10).toFixed(2)}</p>
                    `;
                    
                    progressContainer.style.display = 'none';
                    resultContainer.style.display = 'block';
                    
                    updateStats('蚁群算法', improvement, iterations * 0.2);
                }
            }, 150);
        }
        
        // 混合优化演示
        async function demoHybridOptimization() {
            const progressContainer = document.getElementById('hybridProgress');
            const progressFill = document.getElementById('hybridProgressFill');
            const progressText = document.getElementById('hybridProgressText');
            const resultContainer = document.getElementById('hybridResult');
            
            progressContainer.style.display = 'block';
            resultContainer.style.display = 'none';
            
            const phases = [
                { text: '阶段1: 遗传算法粗优化...', progress: 30 },
                { text: '阶段2: 模拟退火精细化...', progress: 60 },
                { text: '阶段3: 局部搜索优化...', progress: 85 },
                { text: '阶段4: 结果验证...', progress: 100 }
            ];
            
            for (const phase of phases) {
                progressText.textContent = phase.text;
                progressFill.style.width = phase.progress + '%';
                await sleep(1200);
            }
            
            const improvement = 25 + Math.random() * 15;
            
            resultContainer.innerHTML = `
                <h4>⚡ 混合优化结果</h4>
                <p><strong>优化阶段:</strong> 3个阶段</p>
                <p><strong>算法组合:</strong> GA + SA + LS</p>
                <p><strong>路径优化:</strong> ${improvement.toFixed(1)}%</p>
                <p><strong>总计算时间:</strong> ${(4.8 + Math.random() * 1.2).toFixed(1)}s</p>
            `;
            
            progressContainer.style.display = 'none';
            resultContainer.style.display = 'block';
            
            updateStats('混合优化', improvement, 5);
        }
        
        // 运行所有演示
        async function runAllDemos() {
            const demos = [
                demoAIDetection,
                demoGeneticAlgorithm,
                demoNeuralNetwork,
                demoSimulatedAnnealing,
                demoAntColony,
                demoHybridOptimization
            ];
            
            for (const demo of demos) {
                await demo();
                await sleep(1000);
            }
        }
        
        // 更新统计信息
        function updateStats(algorithm, improvement, time) {
            stats.totalOptimizations++;
            stats.totalImprovement += improvement;
            stats.totalTime += time;
            
            if (!stats.algorithms[algorithm]) {
                stats.algorithms[algorithm] = { count: 0, totalImprovement: 0 };
            }
            stats.algorithms[algorithm].count++;
            stats.algorithms[algorithm].totalImprovement += improvement;
            
            // 找到最佳算法
            let bestAlgorithm = '';
            let bestAvgImprovement = 0;
            for (const [name, data] of Object.entries(stats.algorithms)) {
                const avgImprovement = data.totalImprovement / data.count;
                if (avgImprovement > bestAvgImprovement) {
                    bestAvgImprovement = avgImprovement;
                    bestAlgorithm = name;
                }
            }
            
            // 更新显示
            document.getElementById('totalOptimizations').textContent = stats.totalOptimizations;
            document.getElementById('avgImprovement').textContent = 
                (stats.totalImprovement / stats.totalOptimizations).toFixed(1) + '%';
            document.getElementById('bestAlgorithm').textContent = bestAlgorithm;
            document.getElementById('totalTime').textContent = stats.totalTime.toFixed(1) + 's';
        }
        
        // 工具函数
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('AI算法演示页面已加载');
        });
    </script>
</body>
</html>
