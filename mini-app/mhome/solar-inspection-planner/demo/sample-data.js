/**
 * 示例数据 - 用于演示和测试
 */

// 示例光伏板数据
const samplePanels = [
    {
        id: 1,
        bounds: { x: 100, y: 100, width: 80, height: 40 },
        area: 32.0,
        confidence: 0.95
    },
    {
        id: 2,
        bounds: { x: 200, y: 100, width: 80, height: 40 },
        area: 32.0,
        confidence: 0.92
    },
    {
        id: 3,
        bounds: { x: 300, y: 100, width: 80, height: 40 },
        area: 32.0,
        confidence: 0.88
    },
    {
        id: 4,
        bounds: { x: 100, y: 200, width: 80, height: 40 },
        area: 32.0,
        confidence: 0.91
    },
    {
        id: 5,
        bounds: { x: 200, y: 200, width: 80, height: 40 },
        area: 32.0,
        confidence: 0.94
    },
    {
        id: 6,
        bounds: { x: 300, y: 200, width: 80, height: 40 },
        area: 32.0,
        confidence: 0.89
    }
];

// 示例航线数据
const sampleRoute = [
    {
        id: 1,
        lat: 39.904200,
        lng: 116.407400,
        altitude: 0,
        speed: 5,
        action: 'takeoff',
        heading: 0,
        gimbalPitch: 0,
        stayTime: 0
    },
    {
        id: 2,
        lat: 39.904300,
        lng: 116.407500,
        altitude: 50,
        speed: 5,
        action: 'photo',
        heading: 90,
        gimbalPitch: -90,
        stayTime: 2
    },
    {
        id: 3,
        lat: 39.904300,
        lng: 116.407800,
        altitude: 50,
        speed: 5,
        action: 'photo',
        heading: 90,
        gimbalPitch: -90,
        stayTime: 2
    },
    {
        id: 4,
        lat: 39.904500,
        lng: 116.407800,
        altitude: 50,
        speed: 5,
        action: 'photo',
        heading: 270,
        gimbalPitch: -90,
        stayTime: 2
    },
    {
        id: 5,
        lat: 39.904500,
        lng: 116.407500,
        altitude: 50,
        speed: 5,
        action: 'photo',
        heading: 270,
        gimbalPitch: -90,
        stayTime: 2
    },
    {
        id: 6,
        lat: 39.904700,
        lng: 116.407500,
        altitude: 50,
        speed: 5,
        action: 'photo',
        heading: 90,
        gimbalPitch: -90,
        stayTime: 2
    },
    {
        id: 7,
        lat: 39.904700,
        lng: 116.407800,
        altitude: 50,
        speed: 5,
        action: 'photo',
        heading: 90,
        gimbalPitch: -90,
        stayTime: 2
    },
    {
        id: 8,
        lat: 39.904800,
        lng: 116.407900,
        altitude: 0,
        speed: 5,
        action: 'landing',
        heading: 0,
        gimbalPitch: 0,
        stayTime: 0
    }
];

// 示例任务配置
const sampleMissionConfig = {
    name: "光伏场站A区巡检",
    description: "对A区光伏板进行例行巡检",
    flightHeight: 50,
    flightSpeed: 5,
    overlapRate: 80,
    pattern: "zigzag",
    safetyDistance: 10,
    created: new Date().toISOString()
};

// 演示函数：加载示例数据
function loadSampleData() {
    if (window.solarApp) {
        // 模拟图像加载
        window.solarApp.state.imageLoaded = true;
        window.solarApp.state.currentImage = { name: "sample_solar_farm.jpg" };
        
        // 加载示例光伏板
        window.solarApp.state.detectedPanels = samplePanels;
        window.solarApp.state.panelsDetected = true;
        
        // 显示光伏板
        window.solarApp.mapManager.displayPanels(samplePanels);
        
        // 加载示例航线
        window.solarApp.state.flightRoute = sampleRoute;
        window.solarApp.state.routeGenerated = true;
        
        // 显示航线
        window.solarApp.mapManager.displayRoute(sampleRoute);
        
        // 更新UI
        window.solarApp.updateUI();
        window.solarApp.updateRouteInfo(sampleRoute);
        
        // 更新状态
        window.solarApp.showStatus('示例数据加载完成');
        
        console.log('示例数据已加载');
    }
}

// 演示函数：生成随机光伏板数据
function generateRandomPanels(count = 20) {
    const panels = [];
    
    for (let i = 0; i < count; i++) {
        panels.push({
            id: i + 1,
            bounds: {
                x: Math.random() * 800 + 100,
                y: Math.random() * 600 + 100,
                width: 60 + Math.random() * 40,
                height: 30 + Math.random() * 20
            },
            area: 20 + Math.random() * 30,
            confidence: 0.7 + Math.random() * 0.3
        });
    }
    
    return panels;
}

// 演示函数：生成测试航线
function generateTestRoute(bounds, height = 50) {
    const { minLat, maxLat, minLng, maxLng } = bounds;
    const route = [];
    
    // 起飞点
    route.push({
        id: 1,
        lat: minLat - 0.001,
        lng: minLng - 0.001,
        altitude: 0,
        speed: 5,
        action: 'takeoff',
        heading: 0
    });
    
    // 巡检航点
    const latStep = (maxLat - minLat) / 5;
    const lngStep = (maxLng - minLng) / 5;
    
    let id = 2;
    for (let i = 0; i <= 5; i++) {
        const lat = minLat + i * latStep;
        
        if (i % 2 === 0) {
            // 从西到东
            for (let j = 0; j <= 5; j++) {
                const lng = minLng + j * lngStep;
                route.push({
                    id: id++,
                    lat,
                    lng,
                    altitude: height,
                    speed: 5,
                    action: 'photo',
                    heading: 90,
                    gimbalPitch: -90,
                    stayTime: 2
                });
            }
        } else {
            // 从东到西
            for (let j = 5; j >= 0; j--) {
                const lng = minLng + j * lngStep;
                route.push({
                    id: id++,
                    lat,
                    lng,
                    altitude: height,
                    speed: 5,
                    action: 'photo',
                    heading: 270,
                    gimbalPitch: -90,
                    stayTime: 2
                });
            }
        }
    }
    
    // 降落点
    route.push({
        id: id,
        lat: maxLat + 0.001,
        lng: maxLng + 0.001,
        altitude: 0,
        speed: 5,
        action: 'landing',
        heading: 0
    });
    
    return route;
}

// 导出示例数据
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        samplePanels,
        sampleRoute,
        sampleMissionConfig,
        loadSampleData,
        generateRandomPanels,
        generateTestRoute
    };
}

// 在页面加载完成后添加演示按钮
document.addEventListener('DOMContentLoaded', () => {
    // 添加演示按钮到工具栏
    setTimeout(() => {
        const sidebar = document.querySelector('.sidebar');
        if (sidebar) {
            const demoSection = document.createElement('div');
            demoSection.className = 'tool-section';
            demoSection.innerHTML = `
                <h3>演示数据</h3>
                <button id="loadSampleBtn" class="btn btn-info" style="width: 100%; margin-bottom: 0.5rem;">
                    加载示例数据
                </button>
                <button id="generateRandomBtn" class="btn btn-secondary" style="width: 100%;">
                    生成随机数据
                </button>
                <div style="margin-top: 0.5rem; font-size: 0.85rem; color: #666;">
                    <small>用于演示和测试功能</small>
                </div>
            `;
            
            sidebar.appendChild(demoSection);
            
            // 绑定事件
            document.getElementById('loadSampleBtn').addEventListener('click', loadSampleData);
            
            document.getElementById('generateRandomBtn').addEventListener('click', () => {
                if (window.solarApp) {
                    const randomPanels = generateRandomPanels(15);
                    window.solarApp.state.detectedPanels = randomPanels;
                    window.solarApp.state.panelsDetected = true;
                    window.solarApp.state.imageLoaded = true;
                    
                    window.solarApp.mapManager.displayPanels(randomPanels);
                    window.solarApp.updateUI();
                    window.solarApp.showStatus(`生成了 ${randomPanels.length} 个随机光伏板`);
                }
            });
        }
    }, 1000);
});
