<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>光伏板场站巡检航线规划工具</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <h1>光伏板场站巡检航线规划工具</h1>
            <div class="header-controls">
                <button id="helpBtn" class="btn btn-info">帮助</button>
                <button id="settingsBtn" class="btn btn-secondary">设置</button>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 左侧工具栏 -->
            <div class="sidebar">
                <div class="tool-section">
                    <h3>1. 导入地图</h3>
                    <div class="file-upload">
                        <input type="file" id="imageUpload" accept="image/*,.tif,.tiff" multiple>
                        <label for="imageUpload" class="upload-label">
                            <i class="icon-upload"></i>
                            选择正射影像文件
                        </label>
                    </div>
                    <div class="upload-info">
                        <small>支持格式: JPG, PNG, TIFF, GeoTIFF</small>
                    </div>
                </div>

                <div class="tool-section">
                    <h3>2. 光伏板识别</h3>
                    <div class="detection-controls">
                        <label>识别方法:</label>
                        <select id="detectionMethod">
                            <option value="ai">AI智能识别</option>
                            <option value="traditional">传统算法</option>
                            <option value="hybrid">混合模式</option>
                        </select>
                    </div>
                    <div class="detection-controls">
                        <label>识别敏感度:</label>
                        <input type="range" id="sensitivitySlider" min="0.1" max="1.0" step="0.1" value="0.7">
                        <span id="sensitivityValue">0.7</span>
                    </div>
                    <div class="detection-controls">
                        <label>最小面积 (m²):</label>
                        <input type="number" id="minAreaInput" value="10" min="1" max="1000">
                    </div>
                    <div class="detection-controls">
                        <label>
                            <input type="checkbox" id="enableEnsemble" checked> 集成多模型
                        </label>
                    </div>
                    <button id="detectPanelsBtn" class="btn btn-primary" disabled>自动识别光伏板</button>
                    <div class="detection-results">
                        <span id="detectionStatus">未开始识别</span>
                    </div>
                </div>

                <div class="tool-section">
                    <h3>3. 航线规划</h3>
                    <div class="flight-params">
                        <label>飞行高度 (m):</label>
                        <input type="number" id="flightHeight" value="50" min="10" max="500">
                    </div>
                    <div class="flight-params">
                        <label>飞行速度 (m/s):</label>
                        <input type="number" id="flightSpeed" value="5" min="1" max="15" step="0.5">
                    </div>
                    <div class="flight-params">
                        <label>重叠率 (%):</label>
                        <input type="number" id="overlapRate" value="80" min="50" max="90">
                    </div>
                    <div class="flight-params">
                        <label>航线模式:</label>
                        <select id="flightPattern">
                            <option value="zigzag">之字形</option>
                            <option value="grid">网格状</option>
                            <option value="perimeter">周边巡检</option>
                        </select>
                    </div>
                    <button id="generateRouteBtn" class="btn btn-success" disabled>生成航线</button>
                </div>

                <div class="tool-section">
                    <h3>4. 航线编辑</h3>
                    <div class="edit-controls">
                        <label>优化算法:</label>
                        <select id="optimizationMethod">
                            <option value="genetic">遗传算法</option>
                            <option value="simulated_annealing">模拟退火</option>
                            <option value="ant_colony">蚁群算法</option>
                            <option value="neural">神经网络</option>
                            <option value="hybrid">混合优化</option>
                        </select>
                    </div>
                    <div class="edit-controls">
                        <button id="addWaypointBtn" class="btn btn-small">添加航点</button>
                        <button id="deleteWaypointBtn" class="btn btn-small btn-danger">删除航点</button>
                        <button id="optimizeRouteBtn" class="btn btn-small">智能优化</button>
                    </div>
                    <div class="route-info">
                        <div>航点数量: <span id="waypointCount">0</span></div>
                        <div>预计飞行时间: <span id="estimatedTime">0分钟</span></div>
                        <div>总距离: <span id="totalDistance">0米</span></div>
                        <div>优化效果: <span id="optimizationGain">--</span></div>
                    </div>
                </div>

                <div class="tool-section">
                    <h3>5. 导出航线</h3>
                    <div class="export-controls">
                        <label>任务名称:</label>
                        <input type="text" id="missionName" value="光伏巡检任务" placeholder="输入任务名称">
                    </div>
                    <div class="export-controls">
                        <label>导出格式:</label>
                        <select id="exportFormat">
                            <option value="kmz">KMZ (大疆)</option>
                            <option value="waypoints">Waypoints (通用)</option>
                            <option value="litchi">Litchi CSV</option>
                        </select>
                    </div>
                    <button id="exportBtn" class="btn btn-primary" disabled>导出航线文件</button>
                </div>
            </div>

            <!-- 地图区域 -->
            <div class="map-container">
                <div id="map"></div>
                <div class="map-controls">
                    <button id="zoomInBtn" class="map-btn">+</button>
                    <button id="zoomOutBtn" class="map-btn">-</button>
                    <button id="resetViewBtn" class="map-btn">⌂</button>
                    <button id="toggleLayersBtn" class="map-btn">图层</button>
                </div>
                <div class="map-info">
                    <div id="coordinateDisplay">坐标: --</div>
                    <div id="scaleDisplay">比例尺: --</div>
                </div>
            </div>
        </div>

        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span id="statusMessage">就绪</span>
            </div>
            <div class="status-right">
                <div class="progress-container" id="progressContainer" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <span id="progressText">0%</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态对话框 -->
    <div id="helpModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>使用帮助</h2>
            <div class="help-content">
                <h3>使用步骤：</h3>
                <ol>
                    <li><strong>导入地图：</strong>选择光伏场站的正射影像文件</li>
                    <li><strong>识别光伏板：</strong>调整参数后点击自动识别</li>
                    <li><strong>规划航线：</strong>设置飞行参数并生成航线</li>
                    <li><strong>编辑航线：</strong>手动调整航点位置</li>
                    <li><strong>导出文件：</strong>生成大疆无人机可用的KMZ文件</li>
                </ol>
                <h3>注意事项：</h3>
                <ul>
                    <li>建议使用高分辨率的正射影像</li>
                    <li>飞行高度应根据实际场地情况调整</li>
                    <li>导出前请检查航线的合理性</li>
                </ul>
            </div>
        </div>
    </div>

    <div id="settingsModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>设置</h2>
            <div class="settings-content">
                <div class="setting-group">
                    <h3>地图设置</h3>
                    <label>
                        <input type="checkbox" id="showGridLines"> 显示网格线
                    </label>
                    <label>
                        <input type="checkbox" id="showCoordinates" checked> 显示坐标
                    </label>
                </div>
                <div class="setting-group">
                    <h3>识别设置</h3>
                    <label>
                        自动识别阈值:
                        <input type="range" id="autoThreshold" min="0.1" max="1.0" step="0.05" value="0.6">
                    </label>
                </div>
                <div class="setting-group">
                    <h3>航线设置</h3>
                    <label>
                        默认飞行高度 (m):
                        <input type="number" id="defaultHeight" value="50" min="10" max="500">
                    </label>
                    <label>
                        安全距离 (m):
                        <input type="number" id="safetyDistance" value="10" min="5" max="50">
                    </label>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript 库 -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="js/neuralNetwork.js"></script>
    <script src="js/geneticAlgorithm.js"></script>
    <script src="js/aiEnhanced.js"></script>
    <script src="js/mapManager.js"></script>
    <script src="js/panelDetection.js"></script>
    <script src="js/routePlanner.js"></script>
    <script src="js/fileExporter.js"></script>
    <script src="js/app.js"></script>
    <script src="demo/sample-data.js"></script>
</body>
</html>
