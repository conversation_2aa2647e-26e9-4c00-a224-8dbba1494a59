/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

.header-controls {
    display: flex;
    gap: 0.5rem;
}

/* 主要内容区域 */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* 侧边栏样式 */
.sidebar {
    width: 320px;
    background: white;
    border-right: 1px solid #e0e0e0;
    overflow-y: auto;
    padding: 1rem;
}

.tool-section {
    margin-bottom: 1.5rem;
    padding: 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #fafafa;
}

.tool-section h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

/* 文件上传样式 */
.file-upload {
    position: relative;
    margin-bottom: 0.5rem;
}

.file-upload input[type="file"] {
    display: none;
}

.upload-label {
    display: block;
    padding: 0.75rem;
    background: #3498db;
    color: white;
    border-radius: 6px;
    text-align: center;
    cursor: pointer;
    transition: background-color 0.3s;
}

.upload-label:hover {
    background: #2980b9;
}

.upload-info {
    color: #666;
    font-size: 0.85rem;
}

/* 控件样式 */
.detection-controls,
.flight-params,
.edit-controls,
.export-controls {
    margin-bottom: 0.75rem;
}

.detection-controls label,
.flight-params label,
.export-controls label {
    display: block;
    margin-bottom: 0.25rem;
    font-weight: 500;
    color: #555;
}

input[type="number"],
input[type="text"],
input[type="range"],
select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
}

input[type="range"] {
    margin-bottom: 0.25rem;
}

/* 按钮样式 */
.btn {
    padding: 0.6rem 1.2rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #2980b9;
}

.btn-success {
    background: #27ae60;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: #229954;
}

.btn-danger {
    background: #e74c3c;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: #c0392b;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-small {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

.btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    opacity: 0.6;
}

/* 地图容器 */
.map-container {
    flex: 1;
    position: relative;
    background: #ecf0f1;
}

#map {
    width: 100%;
    height: 100%;
}

/* 地图控件 */
.map-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.map-btn {
    width: 40px;
    height: 40px;
    background: white;
    border: 1px solid #ccc;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1.2rem;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    transition: all 0.3s;
}

.map-btn:hover {
    background: #f8f9fa;
    transform: translateY(-1px);
}

/* 地图信息 */
.map-info {
    position: absolute;
    bottom: 10px;
    left: 10px;
    z-index: 1000;
    background: rgba(255,255,255,0.9);
    padding: 0.5rem;
    border-radius: 6px;
    font-size: 0.85rem;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

/* 状态栏 */
.status-bar {
    background: #34495e;
    color: white;
    padding: 0.5rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
}

/* 进度条 */
.progress-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.progress-bar {
    width: 200px;
    height: 20px;
    background: #2c3e50;
    border-radius: 10px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: #27ae60;
    transition: width 0.3s;
    border-radius: 10px;
}

/* 模态对话框 */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 2rem;
    border-radius: 10px;
    width: 80%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
}

.close {
    position: absolute;
    right: 1rem;
    top: 1rem;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    color: #aaa;
}

.close:hover {
    color: #000;
}

/* 帮助内容 */
.help-content h3 {
    color: #2c3e50;
    margin: 1rem 0 0.5rem 0;
}

.help-content ol,
.help-content ul {
    margin-left: 1.5rem;
    margin-bottom: 1rem;
}

.help-content li {
    margin-bottom: 0.5rem;
    line-height: 1.5;
}

/* 设置内容 */
.settings-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.setting-group h3 {
    color: #2c3e50;
    margin-bottom: 0.75rem;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 0.5rem;
}

.setting-group label {
    display: block;
    margin-bottom: 0.75rem;
    font-weight: 500;
}

.setting-group input[type="checkbox"] {
    width: auto;
    margin-right: 0.5rem;
}

/* 结果显示 */
.detection-results,
.route-info {
    background: #ecf0f1;
    padding: 0.75rem;
    border-radius: 6px;
    margin-top: 0.5rem;
}

.route-info div {
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        height: 300px;
        order: 2;
    }
    
    .map-container {
        order: 1;
        height: 400px;
    }
    
    .header h1 {
        font-size: 1.2rem;
    }
    
    .modal-content {
        width: 95%;
        margin: 2% auto;
        padding: 1rem;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.tool-section {
    animation: fadeIn 0.5s ease-out;
}

/* 自定义滚动条 */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.sidebar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
