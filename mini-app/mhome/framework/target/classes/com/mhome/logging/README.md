# 原生日志工具

一个功能强大的原生Java日志工具，无需依赖第三方库，提供完整的日志功能和性能监控。

## 主要特性

### 🎯 核心功能
- **自动代码位置定位**：自动获取调用日志的类名、方法名、文件名和行号
- **完整异常跟踪**：提供详细的异常堆栈信息
- **多级别日志**：支持 TRACE、DEBUG、INFO、WARN、ERROR、FATAL 六个级别
- **线程安全**：支持多线程环境下的并发日志记录
- **格式化输出**：支持类似 printf 的格式化日志输出

### 📊 高级功能
- **性能监控**：自动监控方法执行时间和性能统计
- **包级别配置**：为不同包设置不同的日志级别
- **多种输出方式**：支持控制台、文件或同时输出
- **日志文件滚动**：支持按日期和按文件大小自动滚动日志文件
- **智能文件管理**：自动清理过期日志文件，支持文件数量限制
- **方法调用跟踪**：提供详细的方法进入/退出跟踪

## 快速开始

### 1. 基本使用

```java
import com.mhome.logging.Logger;

public class MyClass {
    private static final Logger logger = Logger.getLogger(MyClass.class);
    
    public void myMethod() {
        logger.info("这是一条信息日志");
        logger.debug("调试信息: 参数值=%s", "test");
        logger.warn("这是一条警告");
        
        try {
            // 一些可能抛异常的代码
            riskyOperation();
        } catch (Exception e) {
            logger.error("操作失败", e);
        }
    }
}
```

### 2. 配置日志

```java
import com.mhome.logging.Logger;
import com.mhome.logging.LoggerConfig;

public class Application {
    public static void main(String[] args) {
        // 初始化默认配置
        LoggerConfig.initializeDefaults();
        
        // 设置全局日志级别
        Logger.setGlobalLevel(Logger.Level.DEBUG);
        
        // 设置输出方式（控制台+文件）
        LoggerConfig.setOutputTarget(LoggerConfig.OutputTarget.BOTH);
        
        // 设置日志目录
        LoggerConfig.setLogDirectory("logs");
        
        // 为特定包设置日志级别
        LoggerConfig.setPackageLevel("com.mycompany.service", Logger.Level.INFO);
        LoggerConfig.setPackageLevel("com.mycompany.dao", Logger.Level.WARN);
    }
}
```

### 3. 性能监控

```java
import com.mhome.logging.PerformanceLogger;

public class BusinessService {
    
    public void processData() {
        // 方式1：使用 try-with-resources（推荐）
        try (PerformanceLogger.PerformanceContext ctx = PerformanceLogger.start("processData")) {
            // 业务逻辑代码
            doSomeWork();
        }
        
        // 方式2：手动记录执行时间
        long startTime = System.nanoTime();
        doOtherWork();
        PerformanceLogger.logExecutionTime("doOtherWork", startTime);
    }
    
    public void methodWithTracing() {
        PerformanceLogger.enter("methodWithTracing", "param1", "param2");
        
        // 业务逻辑
        String result = "success";
        
        PerformanceLogger.exit("methodWithTracing", result);
    }
}
```

## 详细功能说明

### 日志文件滚动策略

#### 按日期滚动
```java
// 启用按日期滚动（默认启用）
LoggerConfig.setDateRollingEnabled(true);
LoggerConfig.setLogFilePrefix("myapp");

// 生成的文件名格式：myapp-2024-01-15.log
```

#### 按文件大小滚动
```java
// 启用按文件大小滚动
LoggerConfig.setSizeRollingEnabled(true);
LoggerConfig.setMaxFileSizeMB(10);  // 每个文件最大10MB

// 生成的文件名格式（按时间新旧排序）：
// myapp.log     <- 最新的日志文件 (索引 0)
// myapp-1.log   <- 较旧的日志文件 (索引 1)
// myapp-2.log   <- 更旧的日志文件 (索引 2)
// myapp-3.log   <- 最旧的日志文件 (索引 3)
```

#### 混合滚动策略
```java
// 同时启用日期和大小滚动
LoggerConfig.setDateRollingEnabled(true);
LoggerConfig.setSizeRollingEnabled(true);
LoggerConfig.setMaxFileSizeMB(5);

// 生成的文件名格式（按时间新旧排序）：
// myapp-2024-01-15.log     <- 当天最新的日志文件
// myapp-2024-01-15-1.log   <- 当天较旧的日志文件
// myapp-2024-01-15-2.log   <- 当天更旧的日志文件
```

#### 文件滚动机制说明

当启用文件大小滚动时，文件滚动遵循以下规则：

1. **文件命名规则**：
   - 无索引文件（如 `app.log`）始终是最新的日志文件
   - 带索引文件（如 `app-1.log`, `app-2.log`）按索引大小表示时间先后
   - 索引越大，文件越旧

2. **滚动过程**：
   ```
   当前状态：
   app.log      <- 最新文件，即将达到大小限制
   app-1.log    <- 较旧文件
   app-2.log    <- 更旧文件

   滚动后：
   app.log      <- 新创建的空文件（最新）
   app-1.log    <- 原来的 app.log（较新）
   app-2.log    <- 原来的 app-1.log（较旧）
   app-3.log    <- 原来的 app-2.log（最旧）
   ```

3. **文件清理**：
   - 根据 `maxLogFiles` 设置保留文件数量
   - 自动删除索引最大（最旧）的文件

#### 文件清理策略
```java
// 设置最大保留文件数
LoggerConfig.setMaxLogFiles(30);

// 对于按日期滚动：保留最近30天的日志
// 对于按大小滚动：保留最新的30个文件
// 对于混合滚动：保留最近30天内的所有文件
```

### 日志级别

| 级别  | 数值 | 用途 |
|-------|------|------|
| TRACE | 0    | 最详细的调试信息，通常只在开发时使用 |
| DEBUG | 1    | 调试信息，用于问题诊断 |
| INFO  | 2    | 一般信息，记录程序运行状态 |
| WARN  | 3    | 警告信息，程序可以继续运行但需要注意 |
| ERROR | 4    | 错误信息，程序遇到错误但可以继续运行 |
| FATAL | 5    | 致命错误，程序可能无法继续运行 |

### 输出格式

日志输出格式：
```
2024-01-15 14:30:25.123 [main] INFO  com.example.MyClass.myMethod(MyClass.java:25) - 这是一条日志消息
```

格式说明：
- `2024-01-15 14:30:25.123`：时间戳（精确到毫秒）
- `[main]`：线程名
- `INFO`：日志级别
- `com.example.MyClass.myMethod(MyClass.java:25)`：调用位置（类.方法(文件:行号)）
- `这是一条日志消息`：日志内容

### 配置选项

#### 输出目标
```java
LoggerConfig.setOutputTarget(LoggerConfig.OutputTarget.CONSOLE); // 仅控制台
LoggerConfig.setOutputTarget(LoggerConfig.OutputTarget.FILE);    // 仅文件
LoggerConfig.setOutputTarget(LoggerConfig.OutputTarget.BOTH);    // 控制台+文件
```

#### 日志文件配置
```java
LoggerConfig.setLogDirectory("logs");           // 设置日志目录
LoggerConfig.setLogFilePrefix("myapp");         // 设置文件前缀

// 按日期滚动配置
LoggerConfig.setDateRollingEnabled(true);       // 启用按日期滚动

// 按文件大小滚动配置
LoggerConfig.setSizeRollingEnabled(true);       // 启用按文件大小滚动
LoggerConfig.setMaxFileSize(10 * 1024 * 1024);  // 设置最大文件大小（字节）
LoggerConfig.setMaxFileSizeMB(10);              // 或者直接设置MB数

// 文件管理配置
LoggerConfig.setMaxLogFiles(30);                // 保留最近30天的日志或最多30个文件
```

#### 包级别配置
```java
// 为不同包设置不同的日志级别
LoggerConfig.setPackageLevel("com.mycompany", Logger.Level.DEBUG);
LoggerConfig.setPackageLevel("com.mycompany.service", Logger.Level.INFO);
LoggerConfig.setPackageLevel("org.springframework", Logger.Level.WARN);
```

### 性能监控功能

#### 自动性能监控
```java
// 自动监控方法执行时间
try (PerformanceLogger.PerformanceContext ctx = PerformanceLogger.start("methodName")) {
    // 方法执行代码
    // 执行时间会自动记录和统计
}
```

#### 方法调用跟踪
```java
public String processUser(String userId) {
    PerformanceLogger.enter("processUser", userId);
    
    // 业务逻辑
    String result = "processed";
    
    PerformanceLogger.exit("processUser", result);
    return result;
}
```

#### 性能统计报告
```java
// 获取性能统计报告
String report = PerformanceLogger.getPerformanceReport();
System.out.println(report);

// 清除统计数据
PerformanceLogger.clearStats();
```

## 运行示例

### 编译和运行演示
```bash
# 编译
javac -d target/classes src/main/java/com/mhome/logging/*.java

# 运行基本演示
java -cp target/classes com.mhome.logging.LoggerDemo

# 运行完整测试
java -cp target/classes com.mhome.logging.LoggerTest
```

### 示例输出
```
2024-01-15 14:30:25.123 [main] INFO  com.mhome.logging.LoggerDemo.demonstrateBasicLogging(LoggerDemo.java:65) - 这是INFO级别的日志
2024-01-15 14:30:25.125 [main] WARN  com.mhome.logging.LoggerDemo.demonstrateBasicLogging(LoggerDemo.java:66) - 这是WARN级别的日志
2024-01-15 14:30:25.126 [main] ERROR com.mhome.logging.LoggerDemo.demonstrateBasicLogging(LoggerDemo.java:67) - 这是ERROR级别的日志
2024-01-15 14:30:25.127 [main] DEBUG com.mhome.logging.LoggerDemo.methodB(LoggerDemo.java:82) - 在methodB中记录调试信息
2024-01-15 14:30:25.128 [main] INFO  com.mhome.logging.LoggerDemo$InnerClass.logFromInnerClass(LoggerDemo.java:91) [com.mhome.logging.LoggerDemo$InnerClass] - 来自内部类的日志消息
```

## 最佳实践

1. **使用类级别的静态Logger**：
   ```java
   private static final Logger logger = Logger.getLogger(MyClass.class);
   ```

2. **合理使用日志级别**：
   - 生产环境建议使用 INFO 级别
   - 开发环境可以使用 DEBUG 级别
   - 只在必要时使用 TRACE 级别

3. **异常处理**：
   ```java
   try {
       riskyOperation();
   } catch (Exception e) {
       logger.error("操作失败: {}", operation, e);
       // 处理异常
   }
   ```

4. **性能监控**：
   - 对关键业务方法使用性能监控
   - 定期查看性能统计报告
   - 及时优化执行时间较长的方法

5. **配置管理**：
   - 在应用启动时统一配置日志
   - 根据环境（开发/测试/生产）设置不同的日志级别
   - 合理设置日志文件保留策略

## 注意事项

- 日志工具是线程安全的，可以在多线程环境中使用
- 文件输出会自动创建目录和文件
- 性能监控数据存储在内存中，应用重启后会丢失
- 建议在应用关闭时调用 `LoggerConfig.shutdown()` 释放资源
