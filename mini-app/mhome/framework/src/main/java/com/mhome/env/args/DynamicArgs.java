package com.mhome.env.args;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class DynamicArgs {

    private final Map<String,Object> longOptions;

    private final Map<String,Object> shortOptions;

    private final Set<String> flags;

    private final List<String> positionalArgs;


    DynamicArgs(Map<String,Object> longOptions,Map<String,Object> shortOptions,Set<String> flags,
                List<String> postionalArgs){
        this.longOptions = longOptions;
        this.shortOptions = shortOptions;
        this.flags = flags;
        this.positionalArgs = postionalArgs;
    }

    public Map<String,Object> getLongOptions(){
        return new HashMap<>(longOptions);
    }

    public Map<String,Object> getShortOptions(){
        return new HashMap<>(shortOptions);
    }

    public Set<String> getFlags(){
        return new HashSet<>(flags);
    }


    private Object getValue(String key){
        if(longOptions.containsKey(key)){
            return longOptions.get(key);
        }
        if(shortOptions.containsKey(key)){
            return shortOptions.get(key);
        }
        if(flags.contains(key)){
            return true;
        }
        return null;
    }

    public String getString(String key) {
        Object value = getValue(key);
        return value != null ? value.toString() : null;
    }

    public int getInt(String key) {
        Object value = getValue(key);
        try{
            return Integer.parseInt(value.toString());
        }catch(Exception e){
            return 0;
        }
        
    }

    public double getDouble(String key) {
        Object value = getValue(key);
         try{
            return Double.parseDouble(value.toString());
        }catch(Exception e){
            return 0.;
        }
    }

    public boolean getBoolean(String key) {
        Object value = getValue(key);
        return value instanceof Boolean ? (Boolean) value : flags.contains(key);
    }

    public List<String> getPositionalArgs() {
        return new ArrayList<>(positionalArgs);
    }

    public String getPositionalArg(int index) {
        return index < positionalArgs.size() ? positionalArgs.get(index) : null;
    }

    // 获取所有选项名称
    public Set<String> getAllOptionNames() {
        Set<String> allNames = new HashSet<>();
        allNames.addAll(longOptions.keySet());
        allNames.addAll(shortOptions.keySet());
        allNames.addAll(flags);
        return allNames;
    }
}
