package com.mhome.logging;

import java.io.IOException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 日志工具使用示例
 * 演示各种日志功能：
 * 1. 基本日志输出
 * 2. 代码位置定位
 * 3. 异常跟踪
 * 4. 多线程日志
 * 5. 不同级别的日志
 * 6. 格式化输出
 */
public class LoggerDemo {
    
    // 获取当前类的日志器
    private static final Logger logger = Logger.getLogger(LoggerDemo.class);
    
    public static void main(String[] args) {
        System.out.println("=== 原生日志工具演示 ===\n");
        
        // 初始化日志配置
        initializeLogging();
        
        // 演示基本日志功能
        demonstrateBasicLogging();
        
        // 演示异常日志
        demonstrateExceptionLogging();
        
        // 演示多线程日志
        demonstrateMultiThreadLogging();
        
        // 演示不同包的日志级别
        demonstratePackageLevelLogging();
        
        // 演示格式化日志
        demonstrateFormattedLogging();
        
        // 显示配置信息
        showConfigurationInfo();
        
        System.out.println("\n=== 演示完成 ===");
    }
    
    /**
     * 初始化日志配置
     */
    private static void initializeLogging() {
        logger.info("初始化日志配置...");
        
        // 初始化默认配置
        LoggerConfig.initializeDefaults();
        
        // 设置输出到控制台和文件
        LoggerConfig.setOutputTarget(LoggerConfig.OutputTarget.BOTH);
        
        // 设置日志目录
        LoggerConfig.setLogDirectory("logs");
        
        // 设置全局日志级别为DEBUG
        Logger.setGlobalLevel(Logger.Level.DEBUG);
        
        logger.info("日志配置初始化完成");
    }
    
    /**
     * 演示基本日志功能
     */
    private static void demonstrateBasicLogging() {
        logger.info("=== 演示基本日志功能 ===");
        
        // 不同级别的日志
        logger.trace("这是TRACE级别的日志");
        logger.debug("这是DEBUG级别的日志");
        logger.info("这是INFO级别的日志");
        logger.warn("这是WARN级别的日志");
        logger.error("这是ERROR级别的日志");
        logger.fatal("这是FATAL级别的日志");
        
        // 调用其他方法来演示代码位置定位
        methodA();
    }
    
    /**
     * 方法A - 用于演示代码位置定位
     */
    private static void methodA() {
        logger.info("在methodA中记录日志");
        methodB();
    }
    
    /**
     * 方法B - 用于演示代码位置定位
     */
    private static void methodB() {
        logger.debug("在methodB中记录调试信息");
        new InnerClass().logFromInnerClass();
    }
    
    /**
     * 内部类 - 用于演示不同类的日志定位
     */
    static class InnerClass {
        private final Logger innerLogger = Logger.getLogger(InnerClass.class);
        
        void logFromInnerClass() {
            innerLogger.info("来自内部类的日志消息");
        }
    }
    
    /**
     * 演示异常日志
     */
    private static void demonstrateExceptionLogging() {
        logger.info("=== 演示异常日志功能 ===");
        
        try {
            // 故意制造一个异常
            throwException();
        } catch (Exception e) {
            logger.error("捕获到异常", e);
        }
        
        try {
            // 嵌套异常
            throwNestedExceptions();
        } catch (Exception e) {
            logger.fatal("捕获到嵌套异常", e);
        }
    }
    
    /**
     * 抛出简单异常
     */
    private static void throwException() throws IOException {
        throw new IOException("这是一个模拟的IO异常");
    }
    
    /**
     * 抛出嵌套异常
     */
    private static void throwNestedExceptions() throws RuntimeException {
        try {
            throw new IllegalArgumentException("原始异常");
        } catch (IllegalArgumentException e) {
            throw new RuntimeException("包装异常", e);
        }
    }
    
    /**
     * 演示多线程日志
     */
    private static void demonstrateMultiThreadLogging() {
        logger.info("=== 演示多线程日志功能 ===");
        
        ExecutorService executor = Executors.newFixedThreadPool(3);
        
        // 提交多个任务
        for (int i = 1; i <= 5; i++) {
            final int taskId = i;
            executor.submit(() -> {
                Logger taskLogger = Logger.getLogger("TaskLogger-" + taskId);
                taskLogger.info("任务 %d 开始执行", taskId);
                
                try {
                    // 模拟一些工作
                    Thread.sleep(100 + (taskId * 50));
                    taskLogger.debug("任务 %d 正在处理数据...", taskId);
                    
                    if (taskId == 3) {
                        // 在任务3中模拟一个警告
                        taskLogger.warn("任务 %d 遇到了一些问题，但可以继续", taskId);
                    }
                    
                    taskLogger.info("任务 %d 执行完成", taskId);
                } catch (InterruptedException e) {
                    taskLogger.error("任务 %d 被中断", taskId, e);
                    Thread.currentThread().interrupt();
                }
            });
        }
        
        executor.shutdown();
        try {
            executor.awaitTermination(5, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            logger.error("等待线程池关闭时被中断", e);
        }
    }
    
    /**
     * 演示不同包的日志级别配置
     */
    private static void demonstratePackageLevelLogging() {
        logger.info("=== 演示包级别日志配置 ===");
        
        // 为不同包设置不同的日志级别
        LoggerConfig.setPackageLevel("com.mhome.logging", Logger.Level.TRACE);
        LoggerConfig.setPackageLevel("com.mhome.service", Logger.Level.WARN);
        
        // 创建不同包的日志器
        Logger serviceLogger = Logger.getLogger("com.mhome.service.UserService");
        Logger daoLogger = Logger.getLogger("com.mhome.dao.UserDao");
        
        // 测试不同级别
        logger.trace("logging包的TRACE消息 - 应该显示");
        serviceLogger.debug("service包的DEBUG消息 - 不应该显示");
        serviceLogger.warn("service包的WARN消息 - 应该显示");
        daoLogger.info("dao包的INFO消息 - 应该显示（使用全局级别）");
    }
    
    /**
     * 演示格式化日志
     */
    private static void demonstrateFormattedLogging() {
        logger.info("=== 演示格式化日志功能 ===");
        
        String userName = "张三";
        int age = 25;
        double salary = 8500.50;
        
        logger.info("用户信息: 姓名=%s, 年龄=%d, 薪资=%.2f", userName, age, salary);
        logger.debug("处理用户 %s 的请求，参数数量: %d", userName, 3);
        logger.warn("用户 %s 的薪资 %.2f 低于平均水平", userName, salary);
        
        // 复杂格式化
        logger.info("系统状态: CPU使用率=%.1f%%, 内存使用率=%.1f%%, 磁盘使用率=%.1f%%", 
                   45.6, 78.2, 23.8);
    }
    
    /**
     * 显示配置信息
     */
    private static void showConfigurationInfo() {
        logger.info("=== 当前日志配置信息 ===");
        String configInfo = LoggerConfig.getConfigInfo();
        System.out.println(configInfo);
    }
}
