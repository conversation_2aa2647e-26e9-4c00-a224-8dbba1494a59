package com.mhome;

import java.util.Arrays;

import com.mhome.env.ProcessUtils;
import com.mhome.env.args.DynamicArgs;
import com.mhome.env.args.DynamicArgsParser;

public class Main {
    public static void main(String[] args) {


        System.out.println(Arrays.asList(args));
        System.out.println("Hello world!");

        Runtime.getRuntime().addShutdownHook(new Thread(Main::test));
        DynamicArgs da = DynamicArgsParser.parse(args);
        System.out.println(da.getAllOptionNames());
        int loop = da.getInt("test");
        if(loop > 10) System.exit(0);
        System.out.println("loop = " + loop);
        loop = loop + 1;
        try{
            Thread.sleep(5000);
            ProcessUtils.restartWithExtraArgs(Main.class,"--test=" + loop);
        }catch (InterruptedException e){
            e.printStackTrace();
        }
        //ProcessUtils.restartWithExtraArgs(Main.class,"--test=" + loop);
        System.exit(1);
        try{
        Thread.currentThread().join();
        }catch (InterruptedException e){
            e.printStackTrace();
        }
    }

    private static void test(){
        System.out.println("end test");
    }
}


