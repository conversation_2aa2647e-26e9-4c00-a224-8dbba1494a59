package com.mhome.env.args;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class DynamicArgsParser {


    public static DynamicArgs parse(String[] args) {
        Map<String,Object> longOptions = new HashMap<>();
        Map<String,Object> shortOptions = new HashMap<>();
        Set<String> flags = new HashSet<>();
        List<String> positionalArgs = new ArrayList<>();
        boolean endOfOptions = false;
        if(args ==null) return new DynamicArgs(longOptions, shortOptions, flags, positionalArgs);
        
        for (int i = 0; i < args.length; i++) {
            String arg = args[i];

            // 处理 -- 分隔符
            if ("--".equals(arg)) {
                endOfOptions = true;
                continue;
            }

            if (!endOfOptions && arg.startsWith("--")) {
                // 长选项
                i = parseLongOption(args, i, longOptions, flags);
            } else if (!endOfOptions && arg.startsWith("-") && arg.length() > 1) {
                // 短选项
                i = parseShortOptions(args, i, shortOptions, flags);
            } else {
                // 位置参数
                positionalArgs.add(arg);
            }
        }

        return new DynamicArgs(longOptions, shortOptions, flags, positionalArgs);
    }

    private static int parseLongOption(String[] args, int index, Map<String, Object> longOptions, Set<String> flags) {
            String arg = args[index];
            String optionName;
            String optionValue = null;

            // 处理 --option=value 格式
            if (arg.contains("=")) {
                String[] parts = arg.split("=", 2);
                optionName = parts[0].substring(2); // 去掉 --
                optionValue = parts[1];
            } else {
                optionName = arg.substring(2); // 去掉 --
            }

            if (optionValue != null) {
                // 有值的选项
                longOptions.put(optionName, optionValue);
            } else if (index + 1 < args.length && !args[index + 1].startsWith("-")) {
                // 下一个参数作为值
                String value = args[index + 1];
                longOptions.put(optionName, value);
                return index + 1;
            } else {
                // 标志选项
                flags.add(optionName);
            }

            return index;
        }

        private static int parseShortOptions(String[] args, int index, Map<String, Object> shortOptions, Set<String> flags) {
            String arg = args[index];
            String shortOpts = arg.substring(1);

            for (int j = 0; j < shortOpts.length(); j++) {
                String shortOpt = String.valueOf(shortOpts.charAt(j));

                if (j == shortOpts.length() - 1 && index + 1 < args.length && !args[index + 1].startsWith("-")) {
                    // 最后一个短选项，且后面有值
                    String value = args[index + 1];
                    shortOptions.put(shortOpt, value);
                    return index + 1;
                } else {
                    // 标志选项
                    flags.add(shortOpt);
                }
            }

            return index;
        }


        public static void main(String[] args) {
             String[] testCases = {
            "--host localhost --port 8080 --verbose file1.txt file2.txt",
            "-h ************* -p 9000 -vdc file1.txt file2.txt -m",
            "--host=localhost --port=8080 --config=/path/to/config input.txt",
            "-v --host=localhost -p 8080 file1.txt file2.txt",
            "--host localhost -v -- --not-an-option file.txt",
            "input.txt output.txt backup.txt",
            "--enable-ssl --timeout=30 --threads 4 -x -y -z file.txt",
            "--database-url ****************************** --user admin --password secret",
            "-abc --long-option-name value --another=test file1 file2 file3"
        };

            for(int i = 2 ; i<3;i++){
                System.out.println("=== 测试用例 " + (i + 1) + " ===");
                System.out.println(testCases[i]);
                String[] testArgs = testCases[i].split("\\s+");
                DynamicArgs result = parse(testArgs);

                System.out.println(result.getLongOptions());
                System.out.println(result.getShortOptions());
                System.out.println(result.getFlags());
                System.out.println(result.getPositionalArgs());
            
                System.out.println(result.getString("p"));
            }
        }
}
