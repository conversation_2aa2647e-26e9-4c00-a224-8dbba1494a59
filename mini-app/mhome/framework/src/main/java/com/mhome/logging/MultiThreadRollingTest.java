package com.mhome.logging;

import java.io.File;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 多线程环境下日志文件滚动测试
 * 验证在多线程并发写入时，文件索引保持连续（1,2,3,4,5,6...）
 */
public class MultiThreadRollingTest {
    
    private static final AtomicInteger messageCounter = new AtomicInteger(0);
    
    public static void main(String[] args) {
        System.out.println("=== 多线程日志文件滚动测试 ===");
        System.out.println("验证多线程环境下文件索引的连续性\n");
        
        try {
            // 测试基本多线程滚动
            testBasicMultiThreadRolling();
            
            // 测试高并发滚动
            testHighConcurrencyRolling();
            
            // 验证文件连续性
            verifyFileContinuity();
            
            System.out.println("\n=== 多线程滚动测试完成 ===");
            
        } catch (Exception e) {
            System.err.println("测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        } finally {
            LoggerConfig.shutdown();
        }
    }
    
    /**
     * 测试基本多线程滚动
     */
    private static void testBasicMultiThreadRolling() {
        System.out.println("1. 测试基本多线程滚动...");
        
        // 配置日志
        LoggerConfig.initializeDefaults();
        LoggerConfig.setOutputTarget(LoggerConfig.OutputTarget.FILE);
        LoggerConfig.setLogDirectory("multithread-rolling");
        LoggerConfig.setLogFilePrefix("thread-demo");
        
        // 启用文件大小滚动
        LoggerConfig.setDateRollingEnabled(false);
        LoggerConfig.setSizeRollingEnabled(true);
        LoggerConfig.setMaxFileSize(1024); // 1KB，用于快速滚动
        LoggerConfig.setMaxLogFiles(8);    // 最多保留8个文件
        
        Logger.setGlobalLevel(Logger.Level.INFO);
        
        // 创建3个线程并发写入
        ExecutorService executor = Executors.newFixedThreadPool(3);
        CountDownLatch latch = new CountDownLatch(3);
        
        for (int threadId = 1; threadId <= 3; threadId++) {
            final int id = threadId;
            executor.submit(() -> {
                try {
                    Logger threadLogger = Logger.getLogger("Thread-" + id);
                    
                    for (int i = 1; i <= 50; i++) {
                        int msgNum = messageCounter.incrementAndGet();
                        threadLogger.info("线程%d-消息%d [全局消息%d] 这是用于测试多线程文件滚动连续性的日志消息，内容较长以便快速达到文件大小限制", 
                            id, i, msgNum);
                        
                        // 短暂休眠模拟真实场景
                        Thread.sleep(5);
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    latch.countDown();
                }
            });
        }
        
        try {
            latch.await(30, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        executor.shutdown();
        try {
            executor.awaitTermination(10, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        System.out.println("   ✓ 基本多线程滚动测试完成");
        showCurrentFiles("基本测试");
    }
    
    /**
     * 测试高并发滚动
     */
    private static void testHighConcurrencyRolling() {
        System.out.println("\n2. 测试高并发滚动...");
        
        // 重置消息计数器
        messageCounter.set(0);
        
        // 创建更多线程进行高并发测试
        ExecutorService executor = Executors.newFixedThreadPool(5);
        CountDownLatch latch = new CountDownLatch(5);
        
        for (int threadId = 1; threadId <= 5; threadId++) {
            final int id = threadId;
            executor.submit(() -> {
                try {
                    Logger threadLogger = Logger.getLogger("HighConcurrency-" + id);
                    
                    for (int i = 1; i <= 30; i++) {
                        int msgNum = messageCounter.incrementAndGet();
                        threadLogger.info("高并发线程%d-消息%d [全局%d] 高并发测试消息，用于验证在大量并发写入时文件滚动的正确性和连续性", 
                            id, i, msgNum);
                        
                        // 更短的休眠时间增加并发度
                        if (i % 10 == 0) {
                            Thread.sleep(1);
                        }
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    latch.countDown();
                }
            });
        }
        
        try {
            latch.await(30, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        executor.shutdown();
        try {
            executor.awaitTermination(10, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        System.out.println("   ✓ 高并发滚动测试完成");
        showCurrentFiles("高并发测试");
    }
    
    /**
     * 验证文件连续性
     */
    private static void verifyFileContinuity() {
        System.out.println("\n3. 验证文件索引连续性...");
        
        File logDir = new File("multithread-rolling");
        if (!logDir.exists()) {
            System.err.println("   ✗ 日志目录不存在");
            return;
        }
        
        File[] logFiles = logDir.listFiles((dir, name) -> 
            name.startsWith("thread-demo") && name.endsWith(".log"));
        
        if (logFiles == null || logFiles.length == 0) {
            System.err.println("   ✗ 未找到日志文件");
            return;
        }
        
        // 提取所有文件的索引
        java.util.Set<Integer> indices = new java.util.TreeSet<>();
        for (File file : logFiles) {
            int index = extractFileIndex(file.getName());
            indices.add(index);
        }
        
        System.out.printf("   找到 %d 个日志文件，索引: %s\n", logFiles.length, indices);
        
        // 验证索引连续性
        boolean isContinuous = true;
        Integer[] indexArray = indices.toArray(new Integer[0]);
        
        for (int i = 0; i < indexArray.length; i++) {
            if (indexArray[i] != i) {
                isContinuous = false;
                System.out.printf("   ✗ 索引不连续: 期望 %d，实际 %d\n", i, indexArray[i]);
            }
        }
        
        if (isContinuous) {
            System.out.println("   ✓ 文件索引连续性验证通过");
        } else {
            System.out.println("   ✗ 文件索引存在不连续");
        }
        
        // 验证文件命名规范
        System.out.println("\n   文件命名验证:");
        boolean namingCorrect = true;
        for (File file : logFiles) {
            String name = file.getName();
            int index = extractFileIndex(name);
            String expectedName = index == 0 ? "thread-demo.log" : "thread-demo-" + index + ".log";
            
            if (!name.equals(expectedName)) {
                System.out.printf("   ✗ 文件命名错误: 实际=%s, 期望=%s\n", name, expectedName);
                namingCorrect = false;
            }
        }
        
        if (namingCorrect) {
            System.out.println("   ✓ 文件命名规范验证通过");
        }
        
        // 显示文件大小分布
        System.out.println("\n   文件大小分布:");
        java.util.Arrays.sort(logFiles, (f1, f2) -> {
            int idx1 = extractFileIndex(f1.getName());
            int idx2 = extractFileIndex(f2.getName());
            return Integer.compare(idx1, idx2);
        });
        
        for (File file : logFiles) {
            long size = file.length();
            String sizeStr = formatFileSize(size);
            System.out.printf("   %s - %s\n", file.getName(), sizeStr);
        }
    }
    
    /**
     * 显示当前文件状态
     */
    private static void showCurrentFiles(String phase) {
        File logDir = new File("multithread-rolling");
        if (!logDir.exists()) {
            return;
        }
        
        File[] files = logDir.listFiles((dir, name) -> 
            name.startsWith("thread-demo") && name.endsWith(".log"));
        
        if (files == null || files.length == 0) {
            System.out.printf("   %s后: 未找到日志文件\n", phase);
            return;
        }
        
        // 按索引排序
        java.util.Arrays.sort(files, (f1, f2) -> {
            int idx1 = extractFileIndex(f1.getName());
            int idx2 = extractFileIndex(f2.getName());
            return Integer.compare(idx1, idx2);
        });
        
        System.out.printf("   %s后文件状态: %d 个文件\n", phase, files.length);
        for (File file : files) {
            long size = file.length();
            System.out.printf("     %s - %s\n", file.getName(), formatFileSize(size));
        }
    }
    
    /**
     * 从文件名提取索引
     */
    private static int extractFileIndex(String fileName) {
        if (fileName.equals("thread-demo.log")) {
            return 0;
        }
        if (fileName.startsWith("thread-demo-") && fileName.endsWith(".log")) {
            String indexStr = fileName.substring("thread-demo-".length(), fileName.length() - ".log".length());
            try {
                return Integer.parseInt(indexStr);
            } catch (NumberFormatException e) {
                return -1;
            }
        }
        return -1;
    }
    
    /**
     * 格式化文件大小
     */
    private static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        }
    }
    
    /**
     * 演示正确的多线程滚动结果
     */
    private static void demonstrateExpectedResult() {
        System.out.println("\n4. 期望的文件滚动结果:");
        System.out.println("   thread-demo.log     <- 最新文件 (索引 0)");
        System.out.println("   thread-demo-1.log   <- 索引 1");
        System.out.println("   thread-demo-2.log   <- 索引 2");
        System.out.println("   thread-demo-3.log   <- 索引 3");
        System.out.println("   thread-demo-4.log   <- 索引 4");
        System.out.println("   thread-demo-5.log   <- 索引 5");
        System.out.println("   thread-demo-6.log   <- 最旧文件 (索引 6)");
        System.out.println();
        System.out.println("   关键要求:");
        System.out.println("   - 索引必须连续：0, 1, 2, 3, 4, 5, 6...");
        System.out.println("   - 不能出现跳跃：如 0, 1, 3, 5...");
        System.out.println("   - 多线程环境下保持一致性");
    }
}
