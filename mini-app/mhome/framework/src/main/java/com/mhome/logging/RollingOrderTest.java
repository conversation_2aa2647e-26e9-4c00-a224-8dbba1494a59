package com.mhome.logging;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 测试日志文件滚动顺序
 * 验证文件滚动后，0表示最新文件，数字越大表示越旧的文件
 */
public class RollingOrderTest {
    
    private static final Logger logger = Logger.getLogger(RollingOrderTest.class);
    
    public static void main(String[] args) {
        System.out.println("=== 日志文件滚动顺序测试 ===\n");
        
        try {
            // 测试基本滚动顺序
            testBasicRollingOrder();
            
            // 验证文件内容和时间顺序
            verifyFileOrder();
            
            // 测试文件清理
            testFileCleanup();
            
            System.out.println("\n=== 滚动顺序测试完成 ===");
            
        } catch (Exception e) {
            System.err.println("测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        } finally {
            LoggerConfig.shutdown();
        }
    }
    
    /**
     * 测试基本滚动顺序
     */
    private static void testBasicRollingOrder() {
        System.out.println("1. 测试基本滚动顺序...");
        
        // 配置日志
        LoggerConfig.initializeDefaults();
        LoggerConfig.setOutputTarget(LoggerConfig.OutputTarget.FILE);
        LoggerConfig.setLogDirectory("rolling-order-test");
        LoggerConfig.setLogFilePrefix("order-test");
        
        // 禁用日期滚动，启用大小滚动
        LoggerConfig.setDateRollingEnabled(false);
        LoggerConfig.setSizeRollingEnabled(true);
        LoggerConfig.setMaxFileSize(500); // 500字节，很小的文件用于快速测试
        LoggerConfig.setMaxLogFiles(5);
        
        Logger.setGlobalLevel(Logger.Level.INFO);
        
        // 生成日志，每批之间有明显的时间标识
        for (int batch = 1; batch <= 6; batch++) {
            System.out.printf("   生成第 %d 批日志...\n", batch);
            
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss.SSS"));
            
            for (int i = 1; i <= 10; i++) {
                logger.info("批次 %d 消息 %d - 时间戳: %s - 这是一条用于测试文件滚动顺序的日志消息", 
                    batch, i, timestamp);
            }
            
            // 短暂休眠以确保时间戳不同
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        System.out.println("   ✓ 基本滚动顺序测试完成");
    }
    
    /**
     * 验证文件顺序
     */
    private static void verifyFileOrder() {
        System.out.println("\n2. 验证文件顺序...");
        
        File logDir = new File("rolling-order-test");
        if (!logDir.exists()) {
            System.err.println("   ✗ 日志目录不存在");
            return;
        }
        
        File[] logFiles = logDir.listFiles((dir, name) -> 
            name.startsWith("order-test") && name.endsWith(".log"));
        
        if (logFiles == null || logFiles.length == 0) {
            System.err.println("   ✗ 未找到日志文件");
            return;
        }
        
        // 按文件名排序（按索引排序）
        java.util.Arrays.sort(logFiles, (f1, f2) -> {
            int index1 = extractIndexFromFileName(f1.getName());
            int index2 = extractIndexFromFileName(f2.getName());
            return Integer.compare(index1, index2);
        });
        
        System.out.printf("   ✓ 找到 %d 个日志文件:\n", logFiles.length);
        
        // 验证文件内容和时间顺序
        for (int i = 0; i < logFiles.length; i++) {
            File file = logFiles[i];
            long fileSize = file.length();
            
            System.out.printf("     %s - %s", file.getName(), formatFileSize(fileSize));
            
            // 读取文件的第一行和最后一行来验证内容
            try {
                java.util.List<String> lines = Files.readAllLines(Paths.get(file.getPath()));
                if (!lines.isEmpty()) {
                    String firstLine = lines.get(0);
                    String lastLine = lines.get(lines.size() - 1);
                    
                    // 提取批次信息
                    int firstBatch = extractBatchFromLogLine(firstLine);
                    int lastBatch = extractBatchFromLogLine(lastLine);
                    
                    if (firstBatch > 0 && lastBatch > 0) {
                        System.out.printf(" (批次 %d-%d)", firstBatch, lastBatch);
                    }
                    
                    System.out.printf(" [%d行]\n", lines.size());
                } else {
                    System.out.println(" (空文件)");
                }
            } catch (Exception e) {
                System.out.printf(" (读取失败: %s)\n", e.getMessage());
            }
        }
        
        // 验证顺序是否正确
        System.out.println("\n   验证文件顺序:");
        if (logFiles.length >= 2) {
            // 检查最新文件（索引0）是否包含最新的内容
            File newestFile = logFiles[0]; // order-test.log
            File olderFile = logFiles[1];   // order-test-1.log
            
            try {
                java.util.List<String> newestLines = Files.readAllLines(Paths.get(newestFile.getPath()));
                java.util.List<String> olderLines = Files.readAllLines(Paths.get(olderFile.getPath()));
                
                if (!newestLines.isEmpty() && !olderLines.isEmpty()) {
                    int newestBatch = extractBatchFromLogLine(newestLines.get(0));
                    int olderBatch = extractBatchFromLogLine(olderLines.get(0));
                    
                    if (newestBatch > olderBatch) {
                        System.out.println("   ✓ 文件顺序正确: 索引0包含最新内容");
                    } else {
                        System.out.println("   ✗ 文件顺序错误: 索引0应该包含最新内容");
                    }
                }
            } catch (Exception e) {
                System.err.println("   ✗ 验证文件顺序时发生错误: " + e.getMessage());
            }
        }
    }
    
    /**
     * 测试文件清理
     */
    private static void testFileCleanup() {
        System.out.println("\n3. 测试文件清理功能...");
        
        // 生成更多日志来触发文件清理
        for (int batch = 7; batch <= 10; batch++) {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss.SSS"));
            
            for (int i = 1; i <= 8; i++) {
                logger.info("清理测试批次 %d 消息 %d - 时间戳: %s", batch, i, timestamp);
            }
            
            try {
                Thread.sleep(50);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        // 检查文件数量是否符合限制
        File logDir = new File("rolling-order-test");
        File[] logFiles = logDir.listFiles((dir, name) -> 
            name.startsWith("order-test") && name.endsWith(".log"));
        
        if (logFiles != null) {
            System.out.printf("   当前文件数量: %d (限制: %d)\n", logFiles.length, 5);
            
            if (logFiles.length <= 5) {
                System.out.println("   ✓ 文件清理功能正常工作");
            } else {
                System.out.println("   ⚠ 文件数量超过限制，清理功能可能有问题");
            }
        }
    }
    
    /**
     * 从文件名中提取索引
     */
    private static int extractIndexFromFileName(String fileName) {
        // order-test.log -> 0
        // order-test-1.log -> 1
        // order-test-2.log -> 2
        
        if (fileName.equals("order-test.log")) {
            return 0;
        }
        
        if (fileName.startsWith("order-test-") && fileName.endsWith(".log")) {
            String indexPart = fileName.substring("order-test-".length(), fileName.length() - ".log".length());
            try {
                return Integer.parseInt(indexPart);
            } catch (NumberFormatException e) {
                return 0;
            }
        }
        
        return 0;
    }
    
    /**
     * 从日志行中提取批次号
     */
    private static int extractBatchFromLogLine(String logLine) {
        // 查找 "批次 X" 模式
        int batchIndex = logLine.indexOf("批次 ");
        if (batchIndex >= 0) {
            int start = batchIndex + "批次 ".length();
            int end = logLine.indexOf(" ", start);
            if (end > start) {
                try {
                    return Integer.parseInt(logLine.substring(start, end));
                } catch (NumberFormatException e) {
                    return 0;
                }
            }
        }
        return 0;
    }
    
    /**
     * 格式化文件大小
     */
    private static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else {
            return String.format("%.1f KB", bytes / 1024.0);
        }
    }
    
    /**
     * 演示正确的文件顺序概念
     */
    private static void demonstrateFileOrderConcept() {
        System.out.println("\n4. 文件滚动顺序说明:");
        System.out.println("   order-test.log     <- 最新的日志文件 (索引 0)");
        System.out.println("   order-test-1.log   <- 较旧的日志文件 (索引 1)");
        System.out.println("   order-test-2.log   <- 更旧的日志文件 (索引 2)");
        System.out.println("   order-test-3.log   <- 最旧的日志文件 (索引 3)");
        System.out.println();
        System.out.println("   当 order-test.log 达到大小限制时:");
        System.out.println("   1. order-test-2.log -> order-test-3.log");
        System.out.println("   2. order-test-1.log -> order-test-2.log");
        System.out.println("   3. order-test.log   -> order-test-1.log");
        System.out.println("   4. 创建新的 order-test.log");
    }
}
