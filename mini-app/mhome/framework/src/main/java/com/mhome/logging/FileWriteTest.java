package com.mhome.logging;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 文件写入功能测试
 * 验证日志工具的文件写入功能是否正常工作
 */
public class FileWriteTest {
    
    private static final Logger logger = Logger.getLogger(FileWriteTest.class);
    
    public static void main(String[] args) {
        System.out.println("=== 日志文件写入功能测试 ===\n");
        
        try {
            // 测试基本文件写入
            testBasicFileWrite();
            
            // 测试不同输出模式
            testOutputModes();
            
            // 测试异常日志写入
            testExceptionLogging();
            
            // 验证文件内容
            verifyLogFiles();
            
            System.out.println("\n=== 文件写入测试完成 ===");
            
        } catch (Exception e) {
            System.err.println("测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 清理资源
            LoggerConfig.shutdown();
        }
    }
    
    /**
     * 测试基本文件写入功能
     */
    private static void testBasicFileWrite() {
        System.out.println("1. 测试基本文件写入功能...");
        
        // 初始化日志配置
        LoggerConfig.initializeDefaults();
        
        // 设置输出到文件
        LoggerConfig.setOutputTarget(LoggerConfig.OutputTarget.FILE);
        
        // 设置日志目录
        String logDir = "test-logs";
        LoggerConfig.setLogDirectory(logDir);
        
        // 设置文件前缀
        LoggerConfig.setLogFilePrefix("test-app");
        
        // 设置全局日志级别
        Logger.setGlobalLevel(Logger.Level.DEBUG);
        
        // 记录不同级别的日志
        logger.info("这是一条测试信息日志");
        logger.debug("这是一条调试日志，包含参数: %s", "test-value");
        logger.warn("这是一条警告日志");
        logger.error("这是一条错误日志");
        
        System.out.println("   ✓ 基本文件写入测试完成");
    }
    
    /**
     * 测试不同输出模式
     */
    private static void testOutputModes() {
        System.out.println("2. 测试不同输出模式...");
        
        // 测试仅控制台输出
        LoggerConfig.setOutputTarget(LoggerConfig.OutputTarget.CONSOLE);
        logger.info("这条日志只输出到控制台");
        
        // 测试仅文件输出
        LoggerConfig.setOutputTarget(LoggerConfig.OutputTarget.FILE);
        logger.info("这条日志只输出到文件");
        
        // 测试同时输出到控制台和文件
        LoggerConfig.setOutputTarget(LoggerConfig.OutputTarget.BOTH);
        logger.info("这条日志同时输出到控制台和文件");
        
        System.out.println("   ✓ 输出模式测试完成");
    }
    
    /**
     * 测试异常日志写入
     */
    private static void testExceptionLogging() {
        System.out.println("3. 测试异常日志写入...");
        
        try {
            // 故意制造一个异常
            throw new RuntimeException("这是一个测试异常");
        } catch (Exception e) {
            logger.error("捕获到测试异常", e);
        }
        
        try {
            // 制造嵌套异常
            throwNestedExceptions();
        } catch (Exception e) {
            logger.fatal("捕获到嵌套异常", e);
        }
        
        System.out.println("   ✓ 异常日志写入测试完成");
    }
    
    /**
     * 抛出嵌套异常用于测试
     */
    private static void throwNestedExceptions() {
        try {
            throw new IllegalArgumentException("原始异常");
        } catch (IllegalArgumentException e) {
            throw new RuntimeException("包装异常", e);
        }
    }
    
    /**
     * 验证日志文件内容
     */
    private static void verifyLogFiles() {
        System.out.println("4. 验证日志文件内容...");
        
        try {
            String logDir = "test-logs";
            String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String logFileName = "test-app-" + today + ".log";
            String logFilePath = logDir + File.separator + logFileName;
            
            File logFile = new File(logFilePath);
            if (logFile.exists()) {
                System.out.println("   ✓ 日志文件已创建: " + logFilePath);
                System.out.println("   ✓ 文件大小: " + logFile.length() + " 字节");
                
                // 读取并显示文件内容的前几行
                try {
                    String content = new String(Files.readAllBytes(Paths.get(logFilePath)));
                    String[] lines = content.split("\n");
                    
                    System.out.println("   ✓ 文件内容预览（前5行）:");
                    for (int i = 0; i < Math.min(5, lines.length); i++) {
                        System.out.println("     " + lines[i]);
                    }
                    
                    if (lines.length > 5) {
                        System.out.println("     ... (共 " + lines.length + " 行)");
                    }
                    
                } catch (IOException e) {
                    System.err.println("   ✗ 读取日志文件失败: " + e.getMessage());
                }
                
            } else {
                System.err.println("   ✗ 日志文件不存在: " + logFilePath);
            }
            
        } catch (Exception e) {
            System.err.println("   ✗ 验证日志文件时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 测试多线程文件写入
     */
    private static void testMultiThreadFileWrite() {
        System.out.println("5. 测试多线程文件写入...");
        
        // 创建多个线程同时写入日志
        Thread[] threads = new Thread[3];
        
        for (int i = 0; i < threads.length; i++) {
            final int threadId = i + 1;
            threads[i] = new Thread(() -> {
                Logger threadLogger = Logger.getLogger("Thread-" + threadId);
                
                for (int j = 1; j <= 5; j++) {
                    threadLogger.info("线程 %d 的第 %d 条日志消息", threadId, j);
                    
                    try {
                        Thread.sleep(10); // 短暂休眠
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            });
        }
        
        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        System.out.println("   ✓ 多线程文件写入测试完成");
    }
    
    /**
     * 显示配置信息
     */
    private static void showConfigInfo() {
        System.out.println("\n当前日志配置:");
        System.out.println(LoggerConfig.getConfigInfo());
    }
}
