package com.mhome.logging;

import java.io.File;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 日志滚动功能完整演示
 * 展示按日期滚动、按文件大小滚动和混合滚动的使用方法
 */
public class RollingDemo {
    
    private static final Logger logger = Logger.getLogger(RollingDemo.class);
    
    public static void main(String[] args) {
        System.out.println("=== 日志滚动功能完整演示 ===\n");
        
        try {
            // 演示按日期滚动
            demonstrateDateRolling();
            
            // 演示按文件大小滚动
            demonstrateSizeRolling();
            
            // 演示混合滚动策略
            demonstrateMixedRolling();
            
            // 演示多线程环境下的滚动
            demonstrateMultiThreadRolling();
            
            // 显示所有生成的文件
            showGeneratedFiles();
            
            System.out.println("\n=== 滚动功能演示完成 ===");
            
        } catch (Exception e) {
            System.err.println("演示过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 清理资源
            LoggerConfig.shutdown();
        }
    }
    
    /**
     * 演示按日期滚动
     */
    private static void demonstrateDateRolling() {
        System.out.println("1. 演示按日期滚动功能...");
        
        // 配置按日期滚动
        LoggerConfig.initializeDefaults();
        LoggerConfig.setOutputTarget(LoggerConfig.OutputTarget.FILE);
        LoggerConfig.setLogDirectory("demo-logs/date-rolling");
        LoggerConfig.setLogFilePrefix("date-demo");
        
        // 启用日期滚动，禁用大小滚动
        LoggerConfig.setDateRollingEnabled(true);
        LoggerConfig.setSizeRollingEnabled(false);
        
        Logger.setGlobalLevel(Logger.Level.DEBUG);
        
        // 生成一些日志
        for (int i = 1; i <= 20; i++) {
            logger.info("按日期滚动演示消息 %d: 当前时间 = %s", i, 
                java.time.LocalDateTime.now().toString());
            logger.debug("调试信息 %d", i);
        }
        
        System.out.println("   ✓ 按日期滚动演示完成");
        System.out.println("   ✓ 文件格式: date-demo-yyyy-MM-dd.log");
    }
    
    /**
     * 演示按文件大小滚动
     */
    private static void demonstrateSizeRolling() {
        System.out.println("\n2. 演示按文件大小滚动功能...");
        
        // 配置按文件大小滚动
        LoggerConfig.setLogDirectory("demo-logs/size-rolling");
        LoggerConfig.setLogFilePrefix("size-demo");
        
        // 禁用日期滚动，启用大小滚动
        LoggerConfig.setDateRollingEnabled(false);
        LoggerConfig.setSizeRollingEnabled(true);
        
        // 设置很小的文件大小用于演示（2KB）
        LoggerConfig.setMaxFileSize(2048);
        
        // 设置最多保留5个文件
        LoggerConfig.setMaxLogFiles(5);
        
        // 生成大量日志来触发文件滚动
        for (int i = 1; i <= 100; i++) {
            logger.info("按文件大小滚动演示消息 %d: 这是一条比较长的日志消息，用于快速达到文件大小限制，触发文件滚动功能。消息内容包含时间戳: %d", 
                i, System.currentTimeMillis());
            
            if (i % 20 == 0) {
                System.out.printf("   已生成 %d 条日志消息\n", i);
            }
        }
        
        System.out.println("   ✓ 按文件大小滚动演示完成");
        System.out.println("   ✓ 文件格式: size-demo.log, size-demo-1.log, size-demo-2.log, ...");
    }
    
    /**
     * 演示混合滚动策略
     */
    private static void demonstrateMixedRolling() {
        System.out.println("\n3. 演示混合滚动策略...");
        
        // 配置混合滚动
        LoggerConfig.setLogDirectory("demo-logs/mixed-rolling");
        LoggerConfig.setLogFilePrefix("mixed-demo");
        
        // 同时启用日期和大小滚动
        LoggerConfig.setDateRollingEnabled(true);
        LoggerConfig.setSizeRollingEnabled(true);
        
        // 设置文件大小限制（3KB）
        LoggerConfig.setMaxFileSize(3072);
        
        // 生成日志
        for (int i = 1; i <= 80; i++) {
            logger.info("混合滚动策略演示消息 %d: 这条消息用于演示同时按日期和文件大小进行滚动的功能。当文件达到指定大小时会创建新文件，同时保持日期信息。时间戳: %d", 
                i, System.currentTimeMillis());
            logger.warn("警告消息 %d: 系统资源使用情况需要关注", i);
            
            if (i % 15 == 0) {
                System.out.printf("   已生成 %d 条混合滚动日志消息\n", i);
            }
        }
        
        System.out.println("   ✓ 混合滚动策略演示完成");
        System.out.println("   ✓ 文件格式: mixed-demo-yyyy-MM-dd.log, mixed-demo-yyyy-MM-dd-1.log, ...");
    }
    
    /**
     * 演示多线程环境下的滚动
     */
    private static void demonstrateMultiThreadRolling() {
        System.out.println("\n4. 演示多线程环境下的滚动功能...");

        // 配置多线程滚动测试
        LoggerConfig.setLogDirectory("demo-logs/multithread-rolling");
        LoggerConfig.setLogFilePrefix("thread-demo");

        // 启用大小滚动，确保文件索引连续
        LoggerConfig.setDateRollingEnabled(false);
        LoggerConfig.setSizeRollingEnabled(true);
        LoggerConfig.setMaxFileSize(2048); // 2KB，更小的文件用于演示
        LoggerConfig.setMaxLogFiles(6);    // 最多保留6个文件，生成 thread-demo.log 到 thread-demo-5.log

        ExecutorService executor = Executors.newFixedThreadPool(4);

        // 启动多个线程同时写入日志
        for (int threadId = 1; threadId <= 4; threadId++) {
            final int id = threadId;
            executor.submit(() -> {
                Logger threadLogger = Logger.getLogger("Thread-" + id);

                for (int i = 1; i <= 40; i++) {
                    threadLogger.info("线程%d-消息%d: 多线程环境下的日志滚动测试，验证文件索引连续性。线程ID=%d，消息编号=%d，时间戳=%d",
                        id, i, id, i, System.currentTimeMillis());

                    try {
                        Thread.sleep(5); // 短暂休眠
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            });
        }

        executor.shutdown();
        try {
            executor.awaitTermination(30, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        System.out.println("   ✓ 多线程滚动演示完成");

        // 验证生成的文件索引连续性
        verifyMultiThreadFiles();
    }

    /**
     * 验证多线程生成的文件索引连续性
     */
    private static void verifyMultiThreadFiles() {
        System.out.println("   验证多线程文件索引连续性:");

        File dir = new File("demo-logs/multithread-rolling");
        if (!dir.exists()) {
            System.out.println("     目录不存在");
            return;
        }

        File[] files = dir.listFiles((d, name) ->
            name.startsWith("thread-demo") && name.endsWith(".log"));

        if (files == null || files.length == 0) {
            System.out.println("     未找到文件");
            return;
        }

        // 提取并排序索引
        java.util.Set<Integer> indices = new java.util.TreeSet<>();
        for (File file : files) {
            String name = file.getName();
            int index = extractThreadDemoIndex(name);
            if (index >= 0) {
                indices.add(index);
            }
        }

        System.out.printf("     找到文件索引: %s\n", indices);

        // 检查连续性
        Integer[] indexArray = indices.toArray(new Integer[0]);
        boolean continuous = true;
        for (int i = 0; i < indexArray.length; i++) {
            if (indexArray[i] != i) {
                continuous = false;
                break;
            }
        }

        if (continuous) {
            System.out.println("     ✓ 文件索引连续");
        } else {
            System.out.println("     ✗ 文件索引不连续");
        }
    }

    /**
     * 从thread-demo文件名中提取索引
     */
    private static int extractThreadDemoIndex(String fileName) {
        if (fileName.equals("thread-demo.log")) {
            return 0;
        }
        if (fileName.startsWith("thread-demo-") && fileName.endsWith(".log")) {
            String indexStr = fileName.substring("thread-demo-".length(), fileName.length() - ".log".length());
            try {
                return Integer.parseInt(indexStr);
            } catch (NumberFormatException e) {
                return -1;
            }
        }
        return -1;
    }
    
    /**
     * 显示所有生成的文件
     */
    private static void showGeneratedFiles() {
        System.out.println("\n5. 生成的日志文件概览:");
        
        String[] directories = {
            "demo-logs/date-rolling",
            "demo-logs/size-rolling", 
            "demo-logs/mixed-rolling",
            "demo-logs/multithread-rolling"
        };
        
        for (String dir : directories) {
            showFilesInDirectory(dir);
        }
        
        // 显示当前配置
        System.out.println("\n当前日志配置:");
        System.out.println(LoggerConfig.getConfigInfo());
    }
    
    /**
     * 显示指定目录中的文件
     */
    private static void showFilesInDirectory(String directory) {
        System.out.println("\n   目录: " + directory);
        
        File dir = new File(directory);
        if (!dir.exists()) {
            System.out.println("     (目录不存在)");
            return;
        }
        
        File[] files = dir.listFiles((d, name) -> name.endsWith(".log"));
        if (files == null || files.length == 0) {
            System.out.println("     (无日志文件)");
            return;
        }
        
        // 按文件名排序
        java.util.Arrays.sort(files, (f1, f2) -> f1.getName().compareTo(f2.getName()));
        
        long totalSize = 0;
        for (File file : files) {
            long size = file.length();
            totalSize += size;
            System.out.printf("     %s - %s\n", file.getName(), formatFileSize(size));
        }
        
        System.out.printf("     总计: %d 个文件, %s\n", files.length, formatFileSize(totalSize));
    }
    
    /**
     * 格式化文件大小
     */
    private static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        }
    }
    
    /**
     * 演示不同的配置组合
     */
    private static void demonstrateConfigurations() {
        System.out.println("\n6. 不同配置组合示例:");
        
        System.out.println("   配置1: 仅按日期滚动");
        System.out.println("   LoggerConfig.setDateRollingEnabled(true);");
        System.out.println("   LoggerConfig.setSizeRollingEnabled(false);");
        
        System.out.println("\n   配置2: 仅按大小滚动");
        System.out.println("   LoggerConfig.setDateRollingEnabled(false);");
        System.out.println("   LoggerConfig.setSizeRollingEnabled(true);");
        System.out.println("   LoggerConfig.setMaxFileSizeMB(10);");
        
        System.out.println("\n   配置3: 混合滚动");
        System.out.println("   LoggerConfig.setDateRollingEnabled(true);");
        System.out.println("   LoggerConfig.setSizeRollingEnabled(true);");
        System.out.println("   LoggerConfig.setMaxFileSizeMB(5);");
        
        System.out.println("\n   配置4: 生产环境推荐");
        System.out.println("   LoggerConfig.setDateRollingEnabled(true);");
        System.out.println("   LoggerConfig.setSizeRollingEnabled(true);");
        System.out.println("   LoggerConfig.setMaxFileSizeMB(50);");
        System.out.println("   LoggerConfig.setMaxLogFiles(30);");
    }
}
