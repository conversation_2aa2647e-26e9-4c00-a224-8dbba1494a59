package com.mhome.logging;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 原生日志工具类
 * 功能特性：
 * 1. 自动定位调用日志的代码位置（类名、方法名、行号）
 * 2. 提供完整的错误跟踪信息
 * 3. 支持多种日志级别
 * 4. 线程安全
 * 5. 支持格式化输出
 */
public class Logger {
    
    /**
     * 日志级别枚举
     */
    public enum Level {
        TRACE(0, "TRACE"),
        DEBUG(1, "DEBUG"),
        INFO(2, "INFO"),
        WARN(3, "WARN"),
        ERROR(4, "ERROR"),
        FATAL(5, "FATAL");
        
        private final int value;
        private final String name;
        
        Level(int value, String name) {
            this.value = value;
            this.name = name;
        }
        
        public int getValue() {
            return value;
        }
        
        public String getName() {
            return name;
        }
    }
    
    // 日志器缓存，避免重复创建
    private static final ConcurrentMap<String, Logger> loggerCache = new ConcurrentHashMap<>();
    
    // 全局日志级别，默认为INFO
    private static volatile Level globalLevel = Level.INFO;
    
    // 时间格式化器
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    
    // 日志器名称
    private final String name;
    
    /**
     * 私有构造函数
     */
    private Logger(String name) {
        this.name = name;
    }
    
    /**
     * 获取日志器实例
     * @param clazz 调用类
     * @return 日志器实例
     */
    public static Logger getLogger(Class<?> clazz) {
        return getLogger(clazz.getName());
    }
    
    /**
     * 获取日志器实例
     * @param name 日志器名称
     * @return 日志器实例
     */
    public static Logger getLogger(String name) {
        return loggerCache.computeIfAbsent(name, Logger::new);
    }
    
    /**
     * 设置全局日志级别
     * @param level 日志级别
     */
    public static void setGlobalLevel(Level level) {
        globalLevel = level;
    }
    
    /**
     * 获取全局日志级别
     * @return 当前全局日志级别
     */
    public static Level getGlobalLevel() {
        return globalLevel;
    }
    
    /**
     * TRACE级别日志
     */
    public void trace(String message) {
        log(Level.TRACE, message, null);
    }
    
    public void trace(String format, Object... args) {
        log(Level.TRACE, String.format(format, args), null);
    }
    
    public void trace(String message, Throwable throwable) {
        log(Level.TRACE, message, throwable);
    }
    
    /**
     * DEBUG级别日志
     */
    public void debug(String message) {
        log(Level.DEBUG, message, null);
    }
    
    public void debug(String format, Object... args) {
        log(Level.DEBUG, String.format(format, args), null);
    }
    
    public void debug(String message, Throwable throwable) {
        log(Level.DEBUG, message, throwable);
    }
    
    /**
     * INFO级别日志
     */
    public void info(String message) {
        log(Level.INFO, message, null);
    }
    
    public void info(String format, Object... args) {
        log(Level.INFO, String.format(format, args), null);
    }
    
    public void info(String message, Throwable throwable) {
        log(Level.INFO, message, throwable);
    }
    
    /**
     * WARN级别日志
     */
    public void warn(String message) {
        log(Level.WARN, message, null);
    }
    
    public void warn(String format, Object... args) {
        log(Level.WARN, String.format(format, args), null);
    }
    
    public void warn(String message, Throwable throwable) {
        log(Level.WARN, message, throwable);
    }
    
    /**
     * ERROR级别日志
     */
    public void error(String message) {
        log(Level.ERROR, message, null);
    }
    
    public void error(String format, Object... args) {
        log(Level.ERROR, String.format(format, args), null);
    }
    
    public void error(String message, Throwable throwable) {
        log(Level.ERROR, message, throwable);
    }
    
    /**
     * FATAL级别日志
     */
    public void fatal(String message) {
        log(Level.FATAL, message, null);
    }
    
    public void fatal(String format, Object... args) {
        log(Level.FATAL, String.format(format, args), null);
    }
    
    public void fatal(String message, Throwable throwable) {
        log(Level.FATAL, message, throwable);
    }
    
    /**
     * 检查指定级别是否启用
     */
    public boolean isEnabled(Level level) {
        Level effectiveLevel = LoggerConfig.getEffectiveLevel(this.name);
        return level.getValue() >= effectiveLevel.getValue();
    }
    
    public boolean isTraceEnabled() {
        return isEnabled(Level.TRACE);
    }
    
    public boolean isDebugEnabled() {
        return isEnabled(Level.DEBUG);
    }
    
    public boolean isInfoEnabled() {
        return isEnabled(Level.INFO);
    }
    
    public boolean isWarnEnabled() {
        return isEnabled(Level.WARN);
    }
    
    public boolean isErrorEnabled() {
        return isEnabled(Level.ERROR);
    }
    
    public boolean isFatalEnabled() {
        return isEnabled(Level.FATAL);
    }
    
    /**
     * 核心日志方法
     */
    private void log(Level level, String message, Throwable throwable) {
        if (!isEnabled(level)) {
            return;
        }
        
        // 获取调用位置信息
        StackTraceElement caller = getCallerInfo();
        
        // 构建日志消息
        String logMessage = buildLogMessage(level, message, caller, throwable);
        
        // 通过LoggerConfig输出日志
        LoggerConfig.output(logMessage);
    }
    
    /**
     * 获取调用者信息
     * 通过分析堆栈跟踪来定位真正的调用位置
     */
    private StackTraceElement getCallerInfo() {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        
        // 跳过以下方法：
        // 0: Thread.getStackTrace()
        // 1: Logger.getCallerInfo()
        // 2: Logger.log()
        // 3: Logger.info/debug/error等方法
        // 4: 真正的调用者
        
        for (int i = 4; i < stackTrace.length; i++) {
            StackTraceElement element = stackTrace[i];
            // 跳过Logger类本身的方法
            if (!element.getClassName().equals(Logger.class.getName())) {
                return element;
            }
        }
        
        // 如果没找到，返回第4个元素（通常情况下的调用者）
        return stackTrace.length > 4 ? stackTrace[4] : stackTrace[stackTrace.length - 1];
    }
    
    /**
     * 构建完整的日志消息
     */
    private String buildLogMessage(Level level, String message, StackTraceElement caller, Throwable throwable) {
        StringBuilder sb = new StringBuilder();
        
        // 时间戳
        sb.append(LocalDateTime.now().format(TIME_FORMATTER));
        
        // 线程名
        sb.append(" [").append(Thread.currentThread().getName()).append("]");
        
        // 日志级别
        sb.append(" ").append(String.format("%-5s", level.getName()));
        
        // 调用位置信息
        if (caller != null) {
            sb.append(" ").append(caller.getClassName());
            sb.append(".").append(caller.getMethodName());
            sb.append("(").append(caller.getFileName()).append(":").append(caller.getLineNumber()).append(")");
        }
        
        // 日志器名称（如果与调用类不同）
        if (caller == null || !name.equals(caller.getClassName())) {
            sb.append(" [").append(name).append("]");
        }
        
        // 消息内容
        sb.append(" - ").append(message);
        
        // 异常信息
        if (throwable != null) {
            sb.append("\n").append(getStackTrace(throwable));
        }
        
        return sb.toString();
    }
    
    /**
     * 获取异常的完整堆栈跟踪信息
     */
    private String getStackTrace(Throwable throwable) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        throwable.printStackTrace(pw);
        return sw.toString();
    }
}
