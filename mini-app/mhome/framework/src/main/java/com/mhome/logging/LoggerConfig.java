package com.mhome.logging;

import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 日志配置和输出管理类
 * 支持：
 * 1. 控制台输出
 * 2. 文件输出
 * 3. 按日期滚动日志文件
 * 4. 不同包/类的独立日志级别配置
 */
public class LoggerConfig {
    
    /**
     * 日志输出目标
     */
    public enum OutputTarget {
        CONSOLE,    // 仅控制台
        FILE,       // 仅文件
        BOTH        // 控制台和文件
    }
    
    // 全局配置
    private static volatile OutputTarget outputTarget = OutputTarget.CONSOLE;
    private static volatile String logDirectory = "logs";
    private static volatile String logFilePrefix = "application";
    private static volatile boolean enableDateRolling = true;
    private static volatile int maxLogFiles = 30; // 保留最近30天的日志
    
    // 包级别的日志级别配置
    private static final ConcurrentMap<String, Logger.Level> packageLevels = new ConcurrentHashMap<>();
    
    // 文件写入器缓存
    private static final ConcurrentMap<String, PrintWriter> fileWriters = new ConcurrentHashMap<>();
    
    // 当前日志文件日期
    private static volatile String currentLogDate = "";
    
    /**
     * 设置输出目标
     */
    public static void setOutputTarget(OutputTarget target) {
        outputTarget = target;
    }
    
    /**
     * 设置日志目录
     */
    public static void setLogDirectory(String directory) {
        logDirectory = directory;
        // 确保目录存在
        try {
            Files.createDirectories(Paths.get(directory));
        } catch (IOException e) {
            System.err.println("创建日志目录失败: " + e.getMessage());
        }
    }
    
    /**
     * 设置日志文件前缀
     */
    public static void setLogFilePrefix(String prefix) {
        logFilePrefix = prefix;
    }
    
    /**
     * 启用/禁用按日期滚动
     */
    public static void setDateRollingEnabled(boolean enabled) {
        enableDateRolling = enabled;
    }
    
    /**
     * 设置最大保留日志文件数
     */
    public static void setMaxLogFiles(int maxFiles) {
        maxLogFiles = maxFiles;
    }
    
    /**
     * 为指定包设置日志级别
     */
    public static void setPackageLevel(String packageName, Logger.Level level) {
        packageLevels.put(packageName, level);
    }
    
    /**
     * 获取指定类的有效日志级别
     */
    public static Logger.Level getEffectiveLevel(String className) {
        // 从最具体的包开始查找
        String currentPackage = className;
        while (currentPackage.contains(".")) {
            Logger.Level level = packageLevels.get(currentPackage);
            if (level != null) {
                return level;
            }
            // 移除最后一个包名部分
            int lastDot = currentPackage.lastIndexOf('.');
            currentPackage = currentPackage.substring(0, lastDot);
        }
        
        // 检查根包
        Logger.Level rootLevel = packageLevels.get(currentPackage);
        if (rootLevel != null) {
            return rootLevel;
        }
        
        // 返回全局级别
        return Logger.getGlobalLevel();
    }
    
    /**
     * 输出日志消息
     */
    public static void output(String message) {
        switch (outputTarget) {
            case CONSOLE:
                outputToConsole(message);
                break;
            case FILE:
                outputToFile(message);
                break;
            case BOTH:
                outputToConsole(message);
                outputToFile(message);
                break;
        }
    }
    
    /**
     * 输出到控制台
     */
    private static void outputToConsole(String message) {
        System.out.println(message);
    }
    
    /**
     * 输出到文件
     */
    private static void outputToFile(String message) {
        try {
            PrintWriter writer = getFileWriter();
            if (writer != null) {
                writer.println(message);
                writer.flush(); // 立即刷新，确保日志及时写入
            }
        } catch (Exception e) {
            System.err.println("写入日志文件失败: " + e.getMessage());
            // 降级到控制台输出
            outputToConsole(message);
        }
    }
    
    /**
     * 获取文件写入器
     */
    private static PrintWriter getFileWriter() {
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        
        // 检查是否需要滚动日志文件
        if (enableDateRolling && !today.equals(currentLogDate)) {
            // 关闭旧的写入器
            closeAllWriters();
            currentLogDate = today;
            
            // 清理过期日志文件
            cleanupOldLogFiles();
        }
        
        String logFileName = enableDateRolling ? 
            logFilePrefix + "-" + today + ".log" : 
            logFilePrefix + ".log";
            
        return fileWriters.computeIfAbsent(logFileName, fileName -> {
            try {
                Path logPath = Paths.get(logDirectory, fileName);
                Files.createDirectories(logPath.getParent());
                return new PrintWriter(new FileWriter(logPath.toFile(), true));
            } catch (IOException e) {
                System.err.println("创建日志文件写入器失败: " + e.getMessage());
                return null;
            }
        });
    }
    
    /**
     * 关闭所有文件写入器
     */
    private static void closeAllWriters() {
        fileWriters.values().forEach(writer -> {
            try {
                writer.close();
            } catch (Exception e) {
                System.err.println("关闭日志文件写入器失败: " + e.getMessage());
            }
        });
        fileWriters.clear();
    }
    
    /**
     * 清理过期的日志文件
     */
    private static void cleanupOldLogFiles() {
        try {
            Path logDir = Paths.get(logDirectory);
            if (!Files.exists(logDir)) {
                return;
            }
            
            LocalDate cutoffDate = LocalDate.now().minusDays(maxLogFiles);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            
            Files.list(logDir)
                .filter(path -> {
                    String fileName = path.getFileName().toString();
                    if (!fileName.startsWith(logFilePrefix) || !fileName.endsWith(".log")) {
                        return false;
                    }
                    
                    // 提取日期部分
                    String datePart = fileName.substring(logFilePrefix.length() + 1, fileName.length() - 4);
                    try {
                        LocalDate fileDate = LocalDate.parse(datePart, formatter);
                        return fileDate.isBefore(cutoffDate);
                    } catch (Exception e) {
                        return false; // 无法解析日期的文件不删除
                    }
                })
                .forEach(path -> {
                    try {
                        Files.delete(path);
                        System.out.println("删除过期日志文件: " + path.getFileName());
                    } catch (IOException e) {
                        System.err.println("删除过期日志文件失败: " + path.getFileName() + " - " + e.getMessage());
                    }
                });
                
        } catch (IOException e) {
            System.err.println("清理过期日志文件时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 关闭日志配置，释放资源
     */
    public static void shutdown() {
        closeAllWriters();
    }
    
    /**
     * 获取当前配置信息
     */
    public static String getConfigInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append("日志配置信息:\n");
        sb.append("  输出目标: ").append(outputTarget).append("\n");
        sb.append("  日志目录: ").append(logDirectory).append("\n");
        sb.append("  文件前缀: ").append(logFilePrefix).append("\n");
        sb.append("  按日期滚动: ").append(enableDateRolling).append("\n");
        sb.append("  最大保留文件数: ").append(maxLogFiles).append("\n");
        sb.append("  全局日志级别: ").append(Logger.getGlobalLevel()).append("\n");
        
        if (!packageLevels.isEmpty()) {
            sb.append("  包级别配置:\n");
            packageLevels.forEach((pkg, level) -> 
                sb.append("    ").append(pkg).append(" = ").append(level).append("\n"));
        }
        
        return sb.toString();
    }
    
    /**
     * 初始化默认配置
     */
    public static void initializeDefaults() {
        // 设置一些常用包的默认日志级别
        setPackageLevel("com.mhome", Logger.Level.DEBUG);
        setPackageLevel("org.springframework", Logger.Level.INFO);
        setPackageLevel("org.hibernate", Logger.Level.WARN);
        setPackageLevel("org.apache", Logger.Level.WARN);
        
        // 确保日志目录存在
        setLogDirectory(logDirectory);
        
        // 添加JVM关闭钩子，确保资源正确释放
        Runtime.getRuntime().addShutdownHook(new Thread(LoggerConfig::shutdown));
    }
}
