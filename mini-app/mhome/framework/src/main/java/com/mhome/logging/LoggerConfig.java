package com.mhome.logging;

import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 日志配置和输出管理类
 * 支持：
 * 1. 控制台输出
 * 2. 文件输出
 * 3. 按日期滚动日志文件
 * 4. 不同包/类的独立日志级别配置
 */
public class LoggerConfig {
    
    /**
     * 日志输出目标
     */
    public enum OutputTarget {
        CONSOLE,    // 仅控制台
        FILE,       // 仅文件
        BOTH        // 控制台和文件
    }
    
    // 全局配置
    private static volatile OutputTarget outputTarget = OutputTarget.CONSOLE;
    private static volatile String logDirectory = "logs";
    private static volatile String logFilePrefix = "application";
    private static volatile boolean enableDateRolling = true;
    private static volatile boolean enableSizeRolling = false;
    private static volatile long maxFileSize = 10 * 1024 * 1024; // 默认10MB
    private static volatile int maxLogFiles = 30; // 保留最近30天的日志或最多30个文件
    
    // 包级别的日志级别配置
    private static final ConcurrentMap<String, Logger.Level> packageLevels = new ConcurrentHashMap<>();
    
    // 文件写入器缓存
    private static final ConcurrentMap<String, PrintWriter> fileWriters = new ConcurrentHashMap<>();

    // 当前日志文件信息
    private static volatile String currentLogDate = "";
    private static volatile String currentLogFile = "";
    private static volatile int currentFileIndex = 0;
    
    /**
     * 设置输出目标
     */
    public static void setOutputTarget(OutputTarget target) {
        outputTarget = target;
    }
    
    /**
     * 设置日志目录
     */
    public static void setLogDirectory(String directory) {
        logDirectory = directory;
        // 确保目录存在
        try {
            Files.createDirectories(Paths.get(directory));
        } catch (IOException e) {
            System.err.println("创建日志目录失败: " + e.getMessage());
        }
    }
    
    /**
     * 设置日志文件前缀
     */
    public static void setLogFilePrefix(String prefix) {
        logFilePrefix = prefix;
    }
    
    /**
     * 启用/禁用按日期滚动
     */
    public static void setDateRollingEnabled(boolean enabled) {
        enableDateRolling = enabled;
    }

    /**
     * 启用/禁用按文件大小滚动
     */
    public static void setSizeRollingEnabled(boolean enabled) {
        enableSizeRolling = enabled;
    }

    /**
     * 设置最大文件大小（字节）
     */
    public static void setMaxFileSize(long maxSize) {
        maxFileSize = maxSize;
    }

    /**
     * 设置最大文件大小（MB）
     */
    public static void setMaxFileSizeMB(int maxSizeMB) {
        maxFileSize = maxSizeMB * 1024L * 1024L;
    }

    /**
     * 设置最大保留日志文件数
     */
    public static void setMaxLogFiles(int maxFiles) {
        maxLogFiles = maxFiles;
    }
    
    /**
     * 为指定包设置日志级别
     */
    public static void setPackageLevel(String packageName, Logger.Level level) {
        packageLevels.put(packageName, level);
    }
    
    /**
     * 获取指定类的有效日志级别
     */
    public static Logger.Level getEffectiveLevel(String className) {
        // 从最具体的包开始查找
        String currentPackage = className;
        while (currentPackage.contains(".")) {
            Logger.Level level = packageLevels.get(currentPackage);
            if (level != null) {
                return level;
            }
            // 移除最后一个包名部分
            int lastDot = currentPackage.lastIndexOf('.');
            currentPackage = currentPackage.substring(0, lastDot);
        }
        
        // 检查根包
        Logger.Level rootLevel = packageLevels.get(currentPackage);
        if (rootLevel != null) {
            return rootLevel;
        }
        
        // 返回全局级别
        return Logger.getGlobalLevel();
    }
    
    /**
     * 输出日志消息
     */
    public static void output(String message) {
        switch (outputTarget) {
            case CONSOLE:
                outputToConsole(message);
                break;
            case FILE:
                outputToFile(message);
                break;
            case BOTH:
                outputToConsole(message);
                outputToFile(message);
                break;
        }
    }
    
    /**
     * 输出到控制台
     */
    private static void outputToConsole(String message) {
        System.out.println(message);
    }
    
    /**
     * 输出到文件
     */
    private static void outputToFile(String message) {
        try {
            PrintWriter writer = getFileWriter();
            if (writer != null) {
                writer.println(message);
                writer.flush(); // 立即刷新，确保日志及时写入
            }
        } catch (Exception e) {
            System.err.println("写入日志文件失败: " + e.getMessage());
            // 降级到控制台输出
            outputToConsole(message);
        }
    }
    
    /**
     * 获取文件写入器
     */
    private static PrintWriter getFileWriter() {
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        // 检查是否需要按日期滚动日志文件
        boolean needDateRolling = enableDateRolling && !today.equals(currentLogDate);

        if (needDateRolling) {
            // 关闭旧的写入器
            closeAllWriters();
            currentLogDate = today;
            currentFileIndex = 0;

            // 清理过期日志文件
            cleanupOldLogFiles();
        }

        // 生成日志文件名
        String logFileName = generateLogFileName(today);

        // 检查是否需要按文件大小滚动
        if (enableSizeRolling && !needDateRolling) {
            Path currentLogPath = Paths.get(logDirectory, logFileName);
            if (Files.exists(currentLogPath)) {
                try {
                    long fileSize = Files.size(currentLogPath);
                    if (fileSize >= maxFileSize) {
                        // 需要滚动到新文件
                        closeFileWriter(logFileName);
                        currentFileIndex++;
                        logFileName = generateLogFileName(today);

                        // 清理过期的按大小滚动的文件
                        cleanupSizeRolledFiles();
                    }
                } catch (IOException e) {
                    System.err.println("检查日志文件大小失败: " + e.getMessage());
                }
            }
        }

        // 更新当前日志文件名
        currentLogFile = logFileName;

        return fileWriters.computeIfAbsent(logFileName, fileName -> {
            try {
                Path logPath = Paths.get(logDirectory, fileName);
                Files.createDirectories(logPath.getParent());
                return new PrintWriter(new FileWriter(logPath.toFile(), true));
            } catch (IOException e) {
                System.err.println("创建日志文件写入器失败: " + e.getMessage());
                return null;
            }
        });
    }

    /**
     * 生成日志文件名
     */
    private static String generateLogFileName(String date) {
        StringBuilder fileName = new StringBuilder(logFilePrefix);

        if (enableDateRolling) {
            fileName.append("-").append(date);
        }

        if (enableSizeRolling && currentFileIndex > 0) {
            fileName.append("-").append(currentFileIndex);
        }

        fileName.append(".log");
        return fileName.toString();
    }

    /**
     * 关闭指定文件的写入器
     */
    private static void closeFileWriter(String fileName) {
        PrintWriter writer = fileWriters.remove(fileName);
        if (writer != null) {
            try {
                writer.close();
            } catch (Exception e) {
                System.err.println("关闭日志文件写入器失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 关闭所有文件写入器
     */
    private static void closeAllWriters() {
        fileWriters.values().forEach(writer -> {
            try {
                writer.close();
            } catch (Exception e) {
                System.err.println("关闭日志文件写入器失败: " + e.getMessage());
            }
        });
        fileWriters.clear();
    }
    
    /**
     * 清理过期的日志文件（按日期滚动）
     */
    private static void cleanupOldLogFiles() {
        if (!enableDateRolling) {
            return;
        }

        try {
            Path logDir = Paths.get(logDirectory);
            if (!Files.exists(logDir)) {
                return;
            }

            LocalDate cutoffDate = LocalDate.now().minusDays(maxLogFiles);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            Files.list(logDir)
                .filter(path -> {
                    String fileName = path.getFileName().toString();
                    if (!fileName.startsWith(logFilePrefix) || !fileName.endsWith(".log")) {
                        return false;
                    }

                    // 提取日期部分
                    String baseName = fileName.substring(0, fileName.length() - 4); // 去掉.log
                    String[] parts = baseName.split("-");

                    // 查找日期部分（格式：yyyy-MM-dd）
                    for (int i = 1; i < parts.length - 1; i++) {
                        if (parts[i].length() == 4 && parts[i + 1].length() == 2 && i + 2 < parts.length && parts[i + 2].length() == 2) {
                            try {
                                String dateStr = parts[i] + "-" + parts[i + 1] + "-" + parts[i + 2];
                                LocalDate fileDate = LocalDate.parse(dateStr, formatter);
                                return fileDate.isBefore(cutoffDate);
                            } catch (Exception e) {
                                // 继续尝试下一个可能的日期组合
                            }
                        }
                    }
                    return false;
                })
                .forEach(path -> {
                    try {
                        Files.delete(path);
                        System.out.println("删除过期日志文件: " + path.getFileName());
                    } catch (IOException e) {
                        System.err.println("删除过期日志文件失败: " + path.getFileName() + " - " + e.getMessage());
                    }
                });

        } catch (IOException e) {
            System.err.println("清理过期日志文件时发生错误: " + e.getMessage());
        }
    }

    /**
     * 清理过期的按大小滚动的文件
     */
    private static void cleanupSizeRolledFiles() {
        if (!enableSizeRolling) {
            return;
        }

        try {
            Path logDir = Paths.get(logDirectory);
            if (!Files.exists(logDir)) {
                return;
            }

            String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String baseFileName = enableDateRolling ? logFilePrefix + "-" + today : logFilePrefix;

            // 收集所有相关的日志文件
            Files.list(logDir)
                .filter(path -> {
                    String fileName = path.getFileName().toString();
                    return fileName.startsWith(baseFileName) && fileName.endsWith(".log");
                })
                .sorted((p1, p2) -> {
                    // 按文件索引排序（较大的索引在前）
                    int index1 = extractFileIndex(p1.getFileName().toString());
                    int index2 = extractFileIndex(p2.getFileName().toString());
                    return Integer.compare(index2, index1);
                })
                .skip(maxLogFiles) // 保留最新的maxLogFiles个文件
                .forEach(path -> {
                    try {
                        Files.delete(path);
                        System.out.println("删除过期的按大小滚动日志文件: " + path.getFileName());
                    } catch (IOException e) {
                        System.err.println("删除过期日志文件失败: " + path.getFileName() + " - " + e.getMessage());
                    }
                });

        } catch (IOException e) {
            System.err.println("清理按大小滚动的日志文件时发生错误: " + e.getMessage());
        }
    }

    /**
     * 从文件名中提取文件索引
     */
    private static int extractFileIndex(String fileName) {
        // 文件名格式：prefix-date-index.log 或 prefix-index.log
        String baseName = fileName.substring(0, fileName.length() - 4); // 去掉.log
        String[] parts = baseName.split("-");

        // 最后一个部分如果是数字，就是索引
        if (parts.length > 1) {
            try {
                return Integer.parseInt(parts[parts.length - 1]);
            } catch (NumberFormatException e) {
                // 不是数字，说明没有索引，返回0
            }
        }
        return 0;
    }
    
    /**
     * 关闭日志配置，释放资源
     */
    public static void shutdown() {
        closeAllWriters();
    }
    
    /**
     * 获取当前配置信息
     */
    public static String getConfigInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append("日志配置信息:\n");
        sb.append("  输出目标: ").append(outputTarget).append("\n");
        sb.append("  日志目录: ").append(logDirectory).append("\n");
        sb.append("  文件前缀: ").append(logFilePrefix).append("\n");
        sb.append("  按日期滚动: ").append(enableDateRolling).append("\n");
        sb.append("  按大小滚动: ").append(enableSizeRolling).append("\n");
        if (enableSizeRolling) {
            sb.append("  最大文件大小: ").append(formatFileSize(maxFileSize)).append("\n");
        }
        sb.append("  最大保留文件数: ").append(maxLogFiles).append("\n");
        sb.append("  全局日志级别: ").append(Logger.getGlobalLevel()).append("\n");

        if (!packageLevels.isEmpty()) {
            sb.append("  包级别配置:\n");
            packageLevels.forEach((pkg, level) ->
                sb.append("    ").append(pkg).append(" = ").append(level).append("\n"));
        }

        return sb.toString();
    }

    /**
     * 格式化文件大小显示
     */
    private static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }
    
    /**
     * 初始化默认配置
     */
    public static void initializeDefaults() {
        // 设置一些常用包的默认日志级别
        setPackageLevel("com.mhome", Logger.Level.DEBUG);
        setPackageLevel("org.springframework", Logger.Level.INFO);
        setPackageLevel("org.hibernate", Logger.Level.WARN);
        setPackageLevel("org.apache", Logger.Level.WARN);
        
        // 确保日志目录存在
        setLogDirectory(logDirectory);
        
        // 添加JVM关闭钩子，确保资源正确释放
        Runtime.getRuntime().addShutdownHook(new Thread(LoggerConfig::shutdown));
    }
}
