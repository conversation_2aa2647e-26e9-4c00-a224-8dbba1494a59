package com.mhome.logging;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 性能监控日志工具
 * 功能：
 * 1. 方法执行时间监控
 * 2. 方法调用跟踪
 * 3. 性能统计
 * 4. 自动代码位置定位
 */
public class PerformanceLogger {
    
    private static final Logger logger = Logger.getLogger(PerformanceLogger.class);
    
    // 存储方法执行时间统计
    private static final ConcurrentMap<String, MethodStats> methodStats = new ConcurrentHashMap<>();
    
    /**
     * 方法统计信息
     */
    public static class MethodStats {
        private volatile long totalTime = 0;
        private volatile long callCount = 0;
        private volatile long minTime = Long.MAX_VALUE;
        private volatile long maxTime = 0;
        
        public synchronized void addExecution(long executionTime) {
            totalTime += executionTime;
            callCount++;
            minTime = Math.min(minTime, executionTime);
            maxTime = Math.max(maxTime, executionTime);
        }
        
        public double getAverageTime() {
            return callCount > 0 ? (double) totalTime / callCount : 0;
        }
        
        public long getTotalTime() { return totalTime; }
        public long getCallCount() { return callCount; }
        public long getMinTime() { return minTime == Long.MAX_VALUE ? 0 : minTime; }
        public long getMaxTime() { return maxTime; }
    }
    
    /**
     * 性能监控上下文
     */
    public static class PerformanceContext implements AutoCloseable {
        private final String methodName;
        private final long startTime;
        private final StackTraceElement caller;
        
        private PerformanceContext(String methodName, StackTraceElement caller) {
            this.methodName = methodName;
            this.caller = caller;
            this.startTime = System.nanoTime();
            
            if (logger.isDebugEnabled()) {
                logger.debug("开始执行方法: %s (%s:%d)", 
                    methodName, caller.getFileName(), caller.getLineNumber());
            }
        }
        
        @Override
        public void close() {
            long endTime = System.nanoTime();
            long executionTime = endTime - startTime;
            double executionTimeMs = executionTime / 1_000_000.0;
            
            // 记录统计信息
            String statsKey = caller.getClassName() + "." + caller.getMethodName();
            methodStats.computeIfAbsent(statsKey, k -> new MethodStats())
                      .addExecution(executionTime);
            
            // 记录日志
            if (executionTimeMs > 1000) { // 超过1秒记录为WARN
                logger.warn("方法执行完成: %s, 耗时: %.2f ms (较慢) (%s:%d)", 
                    methodName, executionTimeMs, caller.getFileName(), caller.getLineNumber());
            } else if (executionTimeMs > 100) { // 超过100ms记录为INFO
                logger.info("方法执行完成: %s, 耗时: %.2f ms (%s:%d)", 
                    methodName, executionTimeMs, caller.getFileName(), caller.getLineNumber());
            } else if (logger.isDebugEnabled()) { // 其他情况记录为DEBUG
                logger.debug("方法执行完成: %s, 耗时: %.2f ms (%s:%d)", 
                    methodName, executionTimeMs, caller.getFileName(), caller.getLineNumber());
            }
        }
    }
    
    /**
     * 开始监控方法执行
     * 使用方式：
     * try (PerformanceContext ctx = PerformanceLogger.start("methodName")) {
     *     // 方法执行代码
     * }
     */
    public static PerformanceContext start(String methodName) {
        StackTraceElement caller = getCaller();
        return new PerformanceContext(methodName, caller);
    }
    
    /**
     * 开始监控当前方法执行（自动获取方法名）
     */
    public static PerformanceContext start() {
        StackTraceElement caller = getCaller();
        return new PerformanceContext(caller.getMethodName(), caller);
    }
    
    /**
     * 记录一个操作的执行时间
     */
    public static void logExecutionTime(String operationName, long startTimeNanos) {
        long endTime = System.nanoTime();
        long executionTime = endTime - startTimeNanos;
        double executionTimeMs = executionTime / 1_000_000.0;
        
        StackTraceElement caller = getCaller();
        String statsKey = caller.getClassName() + "." + operationName;
        methodStats.computeIfAbsent(statsKey, k -> new MethodStats())
                  .addExecution(executionTime);
        
        if (executionTimeMs > 1000) {
            logger.warn("操作完成: %s, 耗时: %.2f ms (较慢) (%s:%d)", 
                operationName, executionTimeMs, caller.getFileName(), caller.getLineNumber());
        } else if (executionTimeMs > 100) {
            logger.info("操作完成: %s, 耗时: %.2f ms (%s:%d)", 
                operationName, executionTimeMs, caller.getFileName(), caller.getLineNumber());
        } else if (logger.isDebugEnabled()) {
            logger.debug("操作完成: %s, 耗时: %.2f ms (%s:%d)", 
                operationName, executionTimeMs, caller.getFileName(), caller.getLineNumber());
        }
    }
    
    /**
     * 获取调用者信息
     */
    private static StackTraceElement getCaller() {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        
        // 跳过：
        // 0: Thread.getStackTrace()
        // 1: PerformanceLogger.getCaller()
        // 2: PerformanceLogger.start() 或其他方法
        // 3: 真正的调用者
        
        for (int i = 3; i < stackTrace.length; i++) {
            StackTraceElement element = stackTrace[i];
            if (!element.getClassName().equals(PerformanceLogger.class.getName())) {
                return element;
            }
        }
        
        return stackTrace.length > 3 ? stackTrace[3] : stackTrace[stackTrace.length - 1];
    }
    
    /**
     * 获取性能统计报告
     */
    public static String getPerformanceReport() {
        if (methodStats.isEmpty()) {
            return "暂无性能统计数据";
        }
        
        StringBuilder report = new StringBuilder();
        report.append("=== 性能统计报告 ===\n");
        report.append(String.format("%-50s %8s %12s %12s %12s %12s\n", 
            "方法名", "调用次数", "总耗时(ms)", "平均耗时(ms)", "最小耗时(ms)", "最大耗时(ms)"));
        report.append("-".repeat(120)).append("\n");
        
        methodStats.entrySet().stream()
            .sorted((e1, e2) -> Long.compare(e2.getValue().getTotalTime(), e1.getValue().getTotalTime()))
            .forEach(entry -> {
                String methodName = entry.getKey();
                MethodStats stats = entry.getValue();
                
                report.append(String.format("%-50s %8d %12.2f %12.2f %12.2f %12.2f\n",
                    methodName.length() > 50 ? methodName.substring(0, 47) + "..." : methodName,
                    stats.getCallCount(),
                    stats.getTotalTime() / 1_000_000.0,
                    stats.getAverageTime() / 1_000_000.0,
                    stats.getMinTime() / 1_000_000.0,
                    stats.getMaxTime() / 1_000_000.0
                ));
            });
        
        return report.toString();
    }
    
    /**
     * 清除所有统计数据
     */
    public static void clearStats() {
        methodStats.clear();
        logger.info("性能统计数据已清除");
    }
    
    /**
     * 获取指定方法的统计信息
     */
    public static MethodStats getMethodStats(String className, String methodName) {
        return methodStats.get(className + "." + methodName);
    }
    
    /**
     * 记录方法调用跟踪
     */
    public static void trace(String message) {
        StackTraceElement caller = getCaller();
        logger.trace("[TRACE] %s (%s.%s:%d)", 
            message, caller.getClassName(), caller.getMethodName(), caller.getLineNumber());
    }
    
    /**
     * 记录方法调用跟踪（带参数）
     */
    public static void trace(String format, Object... args) {
        StackTraceElement caller = getCaller();
        String message = String.format(format, args);
        logger.trace("[TRACE] %s (%s.%s:%d)", 
            message, caller.getClassName(), caller.getMethodName(), caller.getLineNumber());
    }
    
    /**
     * 记录方法进入
     */
    public static void enter(String methodName, Object... params) {
        StackTraceElement caller = getCaller();
        if (params.length > 0) {
            StringBuilder paramStr = new StringBuilder();
            for (int i = 0; i < params.length; i++) {
                if (i > 0) paramStr.append(", ");
                paramStr.append(params[i]);
            }
            logger.trace("[ENTER] %s(%s) (%s:%d)", 
                methodName, paramStr.toString(), caller.getFileName(), caller.getLineNumber());
        } else {
            logger.trace("[ENTER] %s() (%s:%d)", 
                methodName, caller.getFileName(), caller.getLineNumber());
        }
    }
    
    /**
     * 记录方法退出
     */
    public static void exit(String methodName, Object returnValue) {
        StackTraceElement caller = getCaller();
        if (returnValue != null) {
            logger.trace("[EXIT] %s() -> %s (%s:%d)", 
                methodName, returnValue, caller.getFileName(), caller.getLineNumber());
        } else {
            logger.trace("[EXIT] %s() (%s:%d)", 
                methodName, caller.getFileName(), caller.getLineNumber());
        }
    }
    
    /**
     * 记录方法退出（无返回值）
     */
    public static void exit(String methodName) {
        exit(methodName, null);
    }
}
