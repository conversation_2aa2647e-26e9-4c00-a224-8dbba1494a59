package com.mhome.logging;

import java.io.File;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 验证日志文件索引连续性的简单测试
 * 专门测试 thread-demo.log, thread-demo-1.log, thread-demo-2.log... 的连续性
 */
public class ContinuousIndexTest {
    
    private static final Logger logger = Logger.getLogger(ContinuousIndexTest.class);
    
    public static void main(String[] args) {
        System.out.println("=== 日志文件索引连续性测试 ===");
        System.out.println("目标: 生成 thread-demo.log, thread-demo-1.log, thread-demo-2.log, ...\n");
        
        try {
            // 清理之前的测试文件
            cleanupPreviousFiles();
            
            // 配置日志
            setupLogging();
            
            // 执行多线程测试
            runMultiThreadTest();
            
            // 验证结果
            verifyResults();
            
            System.out.println("\n=== 测试完成 ===");
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            LoggerConfig.shutdown();
        }
    }
    
    /**
     * 清理之前的测试文件
     */
    private static void cleanupPreviousFiles() {
        System.out.println("1. 清理之前的测试文件...");
        
        File testDir = new File("continuous-index-test");
        if (testDir.exists()) {
            File[] files = testDir.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.getName().startsWith("thread-demo") && file.getName().endsWith(".log")) {
                        file.delete();
                        System.out.println("   删除: " + file.getName());
                    }
                }
            }
        }
        
        System.out.println("   ✓ 清理完成");
    }
    
    /**
     * 配置日志设置
     */
    private static void setupLogging() {
        System.out.println("\n2. 配置日志设置...");
        
        LoggerConfig.initializeDefaults();
        LoggerConfig.setOutputTarget(LoggerConfig.OutputTarget.FILE);
        LoggerConfig.setLogDirectory("continuous-index-test");
        LoggerConfig.setLogFilePrefix("thread-demo");
        
        // 关键配置：启用文件大小滚动
        LoggerConfig.setDateRollingEnabled(false);
        LoggerConfig.setSizeRollingEnabled(true);
        LoggerConfig.setMaxFileSize(800); // 800字节，快速触发滚动
        LoggerConfig.setMaxLogFiles(8);   // 最多8个文件：thread-demo.log 到 thread-demo-7.log
        
        Logger.setGlobalLevel(Logger.Level.INFO);
        
        System.out.println("   ✓ 文件前缀: thread-demo");
        System.out.println("   ✓ 最大文件大小: 800字节");
        System.out.println("   ✓ 最大文件数: 8");
        System.out.println("   ✓ 期望文件: thread-demo.log, thread-demo-1.log, ..., thread-demo-7.log");
    }
    
    /**
     * 运行多线程测试
     */
    private static void runMultiThreadTest() {
        System.out.println("\n3. 运行多线程测试...");
        
        int threadCount = 5;
        int messagesPerThread = 50;
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        System.out.printf("   启动 %d 个线程，每个线程写入 %d 条消息\n", threadCount, messagesPerThread);
        
        for (int threadId = 1; threadId <= threadCount; threadId++) {
            final int id = threadId;
            executor.submit(() -> {
                try {
                    Logger threadLogger = Logger.getLogger("TestThread-" + id);
                    
                    for (int i = 1; i <= messagesPerThread; i++) {
                        threadLogger.info("线程%d消息%d: 这是用于测试文件索引连续性的消息，内容足够长以便快速达到文件大小限制并触发滚动", id, i);
                        
                        // 每10条消息后短暂休眠
                        if (i % 10 == 0) {
                            Thread.sleep(10);
                        }
                    }
                    
                    System.out.printf("   线程 %d 完成\n", id);
                    
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    latch.countDown();
                }
            });
        }
        
        try {
            latch.await(60, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        executor.shutdown();
        try {
            executor.awaitTermination(10, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        System.out.println("   ✓ 多线程测试完成");
    }
    
    /**
     * 验证结果
     */
    private static void verifyResults() {
        System.out.println("\n4. 验证结果...");
        
        File testDir = new File("continuous-index-test");
        if (!testDir.exists()) {
            System.err.println("   ✗ 测试目录不存在");
            return;
        }
        
        // 收集所有thread-demo文件
        File[] files = testDir.listFiles((dir, name) -> 
            name.startsWith("thread-demo") && name.endsWith(".log"));
        
        if (files == null || files.length == 0) {
            System.err.println("   ✗ 未找到任何日志文件");
            return;
        }
        
        System.out.printf("   找到 %d 个日志文件\n", files.length);
        
        // 提取并验证索引
        java.util.Set<Integer> actualIndices = new java.util.TreeSet<>();
        java.util.Map<Integer, String> indexToFileName = new java.util.HashMap<>();
        
        for (File file : files) {
            String name = file.getName();
            int index = extractIndex(name);
            if (index >= 0) {
                actualIndices.add(index);
                indexToFileName.put(index, name);
            }
        }
        
        // 显示实际文件
        System.out.println("   实际生成的文件:");
        for (Integer index : actualIndices) {
            String fileName = indexToFileName.get(index);
            File file = new File(testDir, fileName);
            long size = file.length();
            System.out.printf("     %s - %d字节\n", fileName, size);
        }
        
        // 验证连续性
        System.out.println("\n   连续性验证:");
        boolean isContinuous = true;
        Integer[] indices = actualIndices.toArray(new Integer[0]);
        
        for (int i = 0; i < indices.length; i++) {
            if (indices[i] != i) {
                System.out.printf("   ✗ 索引不连续: 位置%d期望索引%d，实际索引%d\n", i, i, indices[i]);
                isContinuous = false;
            }
        }
        
        if (isContinuous) {
            System.out.println("   ✓ 文件索引完全连续");
        } else {
            System.out.println("   ✗ 文件索引存在间隙");
        }
        
        // 验证命名规范
        System.out.println("\n   命名规范验证:");
        boolean namingCorrect = true;
        for (Integer index : actualIndices) {
            String actualName = indexToFileName.get(index);
            String expectedName = index == 0 ? "thread-demo.log" : "thread-demo-" + index + ".log";
            
            if (!actualName.equals(expectedName)) {
                System.out.printf("   ✗ 命名错误: 实际=%s, 期望=%s\n", actualName, expectedName);
                namingCorrect = false;
            }
        }
        
        if (namingCorrect) {
            System.out.println("   ✓ 文件命名规范正确");
        }
        
        // 总结
        System.out.println("\n   测试总结:");
        System.out.printf("   - 生成文件数: %d\n", files.length);
        System.out.printf("   - 索引范围: %d 到 %d\n", 
            actualIndices.isEmpty() ? 0 : actualIndices.iterator().next(),
            actualIndices.isEmpty() ? 0 : ((java.util.TreeSet<Integer>)actualIndices).last());
        System.out.printf("   - 连续性: %s\n", isContinuous ? "✓ 通过" : "✗ 失败");
        System.out.printf("   - 命名规范: %s\n", namingCorrect ? "✓ 通过" : "✗ 失败");
        
        if (isContinuous && namingCorrect) {
            System.out.println("\n   🎉 所有测试通过！文件滚动索引连续性正确！");
        } else {
            System.out.println("\n   ❌ 测试失败，需要修复文件滚动逻辑");
        }
    }
    
    /**
     * 从文件名提取索引
     */
    private static int extractIndex(String fileName) {
        if (fileName.equals("thread-demo.log")) {
            return 0;
        }
        
        if (fileName.startsWith("thread-demo-") && fileName.endsWith(".log")) {
            String indexPart = fileName.substring("thread-demo-".length(), fileName.length() - ".log".length());
            try {
                return Integer.parseInt(indexPart);
            } catch (NumberFormatException e) {
                return -1;
            }
        }
        
        return -1;
    }
}
