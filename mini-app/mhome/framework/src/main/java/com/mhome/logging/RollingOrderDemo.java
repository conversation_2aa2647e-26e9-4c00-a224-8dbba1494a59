package com.mhome.logging;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 演示修正后的日志文件滚动顺序
 * 展示0表示最新文件，数字越大表示越旧文件的滚动机制
 */
public class RollingOrderDemo {
    
    private static final Logger logger = Logger.getLogger(RollingOrderDemo.class);
    
    public static void main(String[] args) {
        System.out.println("=== 日志文件滚动顺序演示 ===");
        System.out.println("演示修正后的滚动机制：0=最新，数字越大越旧\n");
        
        try {
            // 设置日志配置
            setupLogging();
            
            // 演示滚动过程
            demonstrateRolling();
            
            // 显示最终结果
            showFinalResult();
            
            System.out.println("\n=== 演示完成 ===");
            
        } catch (Exception e) {
            System.err.println("演示过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        } finally {
            LoggerConfig.shutdown();
        }
    }
    
    /**
     * 设置日志配置
     */
    private static void setupLogging() {
        System.out.println("1. 配置日志设置...");
        
        LoggerConfig.initializeDefaults();
        LoggerConfig.setOutputTarget(LoggerConfig.OutputTarget.FILE);
        LoggerConfig.setLogDirectory("rolling-demo");
        LoggerConfig.setLogFilePrefix("demo");
        
        // 启用文件大小滚动
        LoggerConfig.setDateRollingEnabled(false);
        LoggerConfig.setSizeRollingEnabled(true);
        LoggerConfig.setMaxFileSize(800); // 800字节，用于快速演示
        LoggerConfig.setMaxLogFiles(4);   // 最多保留4个文件
        
        Logger.setGlobalLevel(Logger.Level.INFO);
        
        System.out.println("   ✓ 文件大小限制: 800字节");
        System.out.println("   ✓ 最大文件数: 4");
        System.out.println("   ✓ 文件前缀: demo");
    }
    
    /**
     * 演示滚动过程
     */
    private static void demonstrateRolling() {
        System.out.println("\n2. 开始生成日志，观察文件滚动过程...");
        
        // 生成多批日志，每批都有明确的时间标识
        for (int phase = 1; phase <= 8; phase++) {
            System.out.printf("\n   阶段 %d: 生成日志...\n", phase);
            
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
            
            // 每个阶段生成足够的日志来可能触发滚动
            for (int i = 1; i <= 15; i++) {
                logger.info("阶段%d-消息%d [%s] 这是用于演示文件滚动顺序的测试消息", 
                    phase, i, timestamp);
            }
            
            // 显示当前文件状态
            showCurrentFiles(phase);
            
            // 短暂休眠
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }
    
    /**
     * 显示当前文件状态
     */
    private static void showCurrentFiles(int phase) {
        File logDir = new File("rolling-demo");
        if (!logDir.exists()) {
            return;
        }
        
        File[] files = logDir.listFiles((dir, name) -> 
            name.startsWith("demo") && name.endsWith(".log"));
        
        if (files == null || files.length == 0) {
            return;
        }
        
        // 按文件名排序（按滚动顺序）
        java.util.Arrays.sort(files, (f1, f2) -> {
            int index1 = getFileIndex(f1.getName());
            int index2 = getFileIndex(f2.getName());
            return Integer.compare(index1, index2);
        });
        
        System.out.printf("     当前文件状态 (阶段%d后):\n", phase);
        for (File file : files) {
            long size = file.length();
            String sizeStr = String.format("%d B", size);
            
            // 读取文件第一行来显示内容概要
            String content = getFileFirstLine(file);
            System.out.printf("       %s - %s - %s\n", 
                file.getName(), sizeStr, content);
        }
    }
    
    /**
     * 获取文件索引
     */
    private static int getFileIndex(String fileName) {
        if (fileName.equals("demo.log")) {
            return 0;
        }
        if (fileName.startsWith("demo-") && fileName.endsWith(".log")) {
            String indexStr = fileName.substring(5, fileName.length() - 4);
            try {
                return Integer.parseInt(indexStr);
            } catch (NumberFormatException e) {
                return 0;
            }
        }
        return 0;
    }
    
    /**
     * 获取文件第一行内容
     */
    private static String getFileFirstLine(File file) {
        try {
            java.util.List<String> lines = Files.readAllLines(Paths.get(file.getPath()));
            if (!lines.isEmpty()) {
                String line = lines.get(0);
                // 提取阶段信息
                int stageIndex = line.indexOf("阶段");
                if (stageIndex >= 0) {
                    int endIndex = line.indexOf("-", stageIndex);
                    if (endIndex > stageIndex) {
                        return line.substring(stageIndex, endIndex);
                    }
                }
                return "内容: " + (line.length() > 30 ? line.substring(0, 30) + "..." : line);
            }
            return "(空文件)";
        } catch (Exception e) {
            return "(读取失败)";
        }
    }
    
    /**
     * 显示最终结果
     */
    private static void showFinalResult() {
        System.out.println("\n3. 最终文件状态分析:");
        
        File logDir = new File("rolling-demo");
        File[] files = logDir.listFiles((dir, name) -> 
            name.startsWith("demo") && name.endsWith(".log"));
        
        if (files == null || files.length == 0) {
            System.out.println("   未找到日志文件");
            return;
        }
        
        // 按索引排序
        java.util.Arrays.sort(files, (f1, f2) -> {
            int index1 = getFileIndex(f1.getName());
            int index2 = getFileIndex(f2.getName());
            return Integer.compare(index1, index2);
        });
        
        System.out.println("   文件列表（按新旧顺序）:");
        for (int i = 0; i < files.length; i++) {
            File file = files[i];
            String ageDesc = i == 0 ? "最新" : 
                           i == 1 ? "较新" : 
                           i == 2 ? "较旧" : "最旧";
            
            // 分析文件内容的时间范围
            String timeRange = analyzeFileTimeRange(file);
            
            System.out.printf("   %d. %s - %s - %s - %d字节\n", 
                i + 1, file.getName(), ageDesc, timeRange, file.length());
        }
        
        // 验证顺序是否正确
        System.out.println("\n   验证滚动顺序:");
        if (files.length >= 2) {
            String newestContent = getFileFirstLine(files[0]);
            String olderContent = getFileFirstLine(files[1]);
            
            System.out.printf("   最新文件内容: %s\n", newestContent);
            System.out.printf("   较旧文件内容: %s\n", olderContent);
            
            // 提取阶段号进行比较
            int newestStage = extractStageNumber(newestContent);
            int olderStage = extractStageNumber(olderContent);
            
            if (newestStage > olderStage) {
                System.out.println("   ✓ 滚动顺序正确: 索引0包含最新内容");
            } else if (newestStage == olderStage) {
                System.out.println("   ✓ 滚动顺序正确: 同阶段内容按预期分布");
            } else {
                System.out.println("   ✗ 滚动顺序可能有问题");
            }
        }
        
        System.out.println("\n   滚动机制说明:");
        System.out.println("   - demo.log: 始终是最新的日志文件");
        System.out.println("   - demo-1.log: 第二新的文件");
        System.out.println("   - demo-2.log: 第三新的文件");
        System.out.println("   - demo-3.log: 最旧的文件");
        System.out.println("   - 当demo.log达到大小限制时，所有文件向后滚动一位");
    }
    
    /**
     * 分析文件的时间范围
     */
    private static String analyzeFileTimeRange(File file) {
        try {
            java.util.List<String> lines = Files.readAllLines(Paths.get(file.getPath()));
            if (lines.isEmpty()) {
                return "空文件";
            }
            
            int firstStage = extractStageNumber(lines.get(0));
            int lastStage = extractStageNumber(lines.get(lines.size() - 1));
            
            if (firstStage > 0 && lastStage > 0) {
                if (firstStage == lastStage) {
                    return String.format("阶段%d", firstStage);
                } else {
                    return String.format("阶段%d-%d", firstStage, lastStage);
                }
            }
            
            return String.format("%d行", lines.size());
        } catch (Exception e) {
            return "分析失败";
        }
    }
    
    /**
     * 从内容中提取阶段号
     */
    private static int extractStageNumber(String content) {
        int stageIndex = content.indexOf("阶段");
        if (stageIndex >= 0) {
            int start = stageIndex + 2;
            int end = content.indexOf("-", start);
            if (end > start) {
                try {
                    return Integer.parseInt(content.substring(start, end));
                } catch (NumberFormatException e) {
                    return 0;
                }
            }
        }
        return 0;
    }
}
