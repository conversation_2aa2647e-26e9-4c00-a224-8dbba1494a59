package com.mhome.logging;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * 文件大小滚动功能测试
 * 验证日志工具的按文件大小滚动功能
 */
public class SizeRollingTest {
    
    private static final Logger logger = Logger.getLogger(SizeRollingTest.class);
    
    public static void main(String[] args) {
        System.out.println("=== 日志文件大小滚动功能测试 ===\n");
        
        try {
            // 测试基本的文件大小滚动
            testBasicSizeRolling();
            
            // 测试日期+大小混合滚动
            testDateAndSizeRolling();
            
            // 验证生成的日志文件
            verifyRolledFiles();
            
            System.out.println("\n=== 文件大小滚动测试完成 ===");
            
        } catch (Exception e) {
            System.err.println("测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 清理资源
            LoggerConfig.shutdown();
        }
    }
    
    /**
     * 测试基本的文件大小滚动功能
     */
    private static void testBasicSizeRolling() {
        System.out.println("1. 测试基本文件大小滚动功能...");
        
        // 初始化日志配置
        LoggerConfig.initializeDefaults();
        
        // 设置输出到文件
        LoggerConfig.setOutputTarget(LoggerConfig.OutputTarget.FILE);
        
        // 设置日志目录
        String logDir = "size-rolling-test";
        LoggerConfig.setLogDirectory(logDir);
        
        // 设置文件前缀
        LoggerConfig.setLogFilePrefix("size-test");
        
        // 禁用日期滚动，启用大小滚动
        LoggerConfig.setDateRollingEnabled(false);
        LoggerConfig.setSizeRollingEnabled(true);
        
        // 设置很小的文件大小限制（1KB）用于测试
        LoggerConfig.setMaxFileSize(1024); // 1KB
        
        // 设置最多保留5个文件
        LoggerConfig.setMaxLogFiles(5);
        
        // 设置全局日志级别
        Logger.setGlobalLevel(Logger.Level.DEBUG);
        
        // 生成大量日志来触发文件滚动
        for (int i = 1; i <= 100; i++) {
            logger.info("这是第 %d 条测试日志消息，用于测试文件大小滚动功能。这条消息比较长，目的是快速达到文件大小限制。", i);
            logger.debug("调试信息 %d: 当前时间戳 = %d", i, System.currentTimeMillis());
            
            if (i % 10 == 0) {
                System.out.printf("   已生成 %d 条日志消息\n", i);
            }
        }
        
        System.out.println("   ✓ 基本文件大小滚动测试完成");
    }
    
    /**
     * 测试日期+大小混合滚动
     */
    private static void testDateAndSizeRolling() {
        System.out.println("2. 测试日期+大小混合滚动功能...");
        
        // 设置新的配置
        LoggerConfig.setLogDirectory("mixed-rolling-test");
        LoggerConfig.setLogFilePrefix("mixed-test");
        
        // 启用日期和大小滚动
        LoggerConfig.setDateRollingEnabled(true);
        LoggerConfig.setSizeRollingEnabled(true);
        
        // 设置文件大小限制（2KB）
        LoggerConfig.setMaxFileSize(2048); // 2KB
        
        // 生成日志
        for (int i = 1; i <= 50; i++) {
            logger.info("混合滚动测试消息 %d: 这是一条用于测试日期和大小混合滚动的日志消息。消息内容较长以便快速达到文件大小限制。", i);
            logger.warn("警告消息 %d: 系统资源使用率较高，请注意监控。", i);
            
            if (i % 10 == 0) {
                System.out.printf("   已生成 %d 条混合滚动日志消息\n", i);
            }
        }
        
        System.out.println("   ✓ 日期+大小混合滚动测试完成");
    }
    
    /**
     * 验证生成的日志文件
     */
    private static void verifyRolledFiles() {
        System.out.println("3. 验证生成的日志文件...");
        
        // 验证基本大小滚动文件
        verifyFilesInDirectory("size-rolling-test", "size-test");
        
        // 验证混合滚动文件
        verifyFilesInDirectory("mixed-rolling-test", "mixed-test");
    }
    
    /**
     * 验证指定目录中的日志文件
     */
    private static void verifyFilesInDirectory(String directory, String prefix) {
        System.out.println("   验证目录: " + directory);
        
        File logDir = new File(directory);
        if (!logDir.exists()) {
            System.err.println("   ✗ 日志目录不存在: " + directory);
            return;
        }
        
        File[] logFiles = logDir.listFiles((dir, name) -> 
            name.startsWith(prefix) && name.endsWith(".log"));
        
        if (logFiles == null || logFiles.length == 0) {
            System.err.println("   ✗ 未找到日志文件");
            return;
        }
        
        System.out.printf("   ✓ 找到 %d 个日志文件:\n", logFiles.length);
        
        long totalSize = 0;
        for (File file : logFiles) {
            long fileSize = file.length();
            totalSize += fileSize;
            
            System.out.printf("     %s - %s\n", 
                file.getName(), 
                formatFileSize(fileSize));
            
            // 验证文件内容
            try {
                long lineCount = Files.lines(Paths.get(file.getPath())).count();
                System.out.printf("       包含 %d 行日志\n", lineCount);
            } catch (Exception e) {
                System.err.printf("       读取文件失败: %s\n", e.getMessage());
            }
        }
        
        System.out.printf("   ✓ 总文件大小: %s\n", formatFileSize(totalSize));
        System.out.println();
    }
    
    /**
     * 格式化文件大小显示
     */
    private static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }
    
    /**
     * 演示不同的文件大小配置
     */
    private static void demonstrateSizeConfigurations() {
        System.out.println("4. 演示不同的文件大小配置...");
        
        // 演示不同的大小设置方法
        System.out.println("   设置文件大小的不同方法:");
        
        // 方法1: 直接设置字节数
        LoggerConfig.setMaxFileSize(5 * 1024 * 1024); // 5MB
        System.out.println("   - 直接设置字节数: 5MB");
        
        // 方法2: 使用MB设置
        LoggerConfig.setMaxFileSizeMB(10); // 10MB
        System.out.println("   - 使用MB设置: 10MB");
        
        // 显示当前配置
        System.out.println("\n   当前配置信息:");
        System.out.println(LoggerConfig.getConfigInfo());
    }
    
    /**
     * 测试文件清理功能
     */
    private static void testFileCleanup() {
        System.out.println("5. 测试文件清理功能...");
        
        // 设置只保留3个文件
        LoggerConfig.setMaxLogFiles(3);
        
        // 生成更多日志触发清理
        for (int i = 1; i <= 30; i++) {
            logger.info("清理测试消息 %d: 这条消息用于测试文件清理功能。", i);
        }
        
        System.out.println("   ✓ 文件清理测试完成");
    }
}
