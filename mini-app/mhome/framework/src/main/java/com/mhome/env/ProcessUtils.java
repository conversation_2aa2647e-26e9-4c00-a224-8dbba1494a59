package com.mhome.env;

import java.io.File;
import java.lang.management.ManagementFactory;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class ProcessUtils {

    public static Process start(List<String> cmd){
        return start(cmd,new File(System.getProperty("user.dir")));
    }

    public static Process start(List<String> cmd,File workDir){
        try {
            ProcessBuilder pb = new ProcessBuilder(cmd);
            pb.directory(workDir);
            pb.inheritIO();
            return pb.start();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static Process restart(Class<?> mainClass){
        return restartWithExtraArgs(mainClass,"");
    }

    public static Process restartWithExtraArgs(Class<?> mainClass, String... args) {
        return restartWithJvmArgs(mainClass, null, args);
    }
        
    public static Process restartWithJvmArgs(Class<?> mainClass, String[] jvmArgs, String[] programArgs) {
        String javaHome = System.getProperty("java.home");
        String javaBin = javaHome + File.separator + "bin" + File.separator + "java";
        String classpath = System.getProperty("java.class.path");
        String className = mainClass.getName();
        List<String> orginalJvmArgs = ManagementFactory.getRuntimeMXBean().getInputArguments();
        List<String> list = new ArrayList<String>(){{
            add(javaBin);
            addAll(orginalJvmArgs);
            if (jvmArgs != null && jvmArgs.length > 0)  addAll(Arrays.asList(jvmArgs));
            add("-cp");
            add(classpath);
            add(className);
            if (programArgs != null && programArgs.length > 0) addAll(Arrays.asList(programArgs));
        }};
        list.add(javaBin);
        return start(list);
    }

   


}
