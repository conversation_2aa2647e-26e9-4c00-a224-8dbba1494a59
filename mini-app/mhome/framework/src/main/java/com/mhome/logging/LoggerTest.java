package com.mhome.logging;

import java.util.Random;

/**
 * 日志工具完整测试示例
 * 演示在实际业务场景中如何使用日志工具
 */
public class LoggerTest {
    
    private static final Logger logger = Logger.getLogger(LoggerTest.class);
    private static final Random random = new Random();
    
    public static void main(String[] args) {
        System.out.println("=== 日志工具完整测试 ===\n");
        
        // 初始化日志配置
        setupLogging();
        
        // 模拟业务场景
        simulateBusinessScenarios();
        
        // 显示性能报告
        showPerformanceReport();
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    /**
     * 设置日志配置
     */
    private static void setupLogging() {
        logger.info("设置日志配置...");
        
        // 初始化默认配置
        LoggerConfig.initializeDefaults();
        
        // 设置输出到控制台和文件
        LoggerConfig.setOutputTarget(LoggerConfig.OutputTarget.BOTH);
        
        // 设置全局日志级别
        Logger.setGlobalLevel(Logger.Level.DEBUG);
        
        // 为不同包设置不同级别
        LoggerConfig.setPackageLevel("com.mhome.logging", Logger.Level.TRACE);
        LoggerConfig.setPackageLevel("com.mhome.service", Logger.Level.INFO);
        
        logger.info("日志配置完成");
    }
    
    /**
     * 模拟业务场景
     */
    private static void simulateBusinessScenarios() {
        logger.info("开始模拟业务场景...");
        
        // 模拟用户服务
        UserService userService = new UserService();
        
        // 模拟多个用户操作
        for (int i = 1; i <= 5; i++) {
            try {
                User user = userService.createUser("user" + i, "user" + i + "@example.com");
                userService.updateUser(user);
                userService.deleteUser(user.getId());
            } catch (Exception e) {
                logger.error("处理用户 user%d 时发生错误", i, e);
            }
        }
        
        // 模拟数据处理服务
        DataProcessor processor = new DataProcessor();
        processor.processLargeDataSet();
        
        logger.info("业务场景模拟完成");
    }
    
    /**
     * 显示性能报告
     */
    private static void showPerformanceReport() {
        logger.info("=== 性能统计报告 ===");
        String report = PerformanceLogger.getPerformanceReport();
        System.out.println(report);
    }
    
    /**
     * 模拟用户实体
     */
    static class User {
        private final String id;
        private final String name;
        private final String email;
        
        public User(String id, String name, String email) {
            this.id = id;
            this.name = name;
            this.email = email;
        }
        
        public String getId() { return id; }
        public String getName() { return name; }
        public String getEmail() { return email; }
        
        @Override
        public String toString() {
            return String.format("User{id='%s', name='%s', email='%s'}", id, name, email);
        }
    }
    
    /**
     * 模拟用户服务
     */
    static class UserService {
        private final Logger serviceLogger = Logger.getLogger("com.mhome.service.UserService");
        
        public User createUser(String name, String email) {
            try (PerformanceLogger.PerformanceContext ctx = PerformanceLogger.start("createUser")) {
                PerformanceLogger.enter("createUser", name, email);
                
                serviceLogger.info("开始创建用户: name=%s, email=%s", name, email);
                
                // 模拟参数验证
                validateUserInput(name, email);
                
                // 模拟数据库操作
                simulateDbOperation("INSERT", 50, 200);
                
                String userId = "user_" + System.currentTimeMillis() + "_" + random.nextInt(1000);
                User user = new User(userId, name, email);
                
                serviceLogger.info("用户创建成功: %s", user);
                PerformanceLogger.exit("createUser", user);
                
                return user;
            }
        }
        
        public void updateUser(User user) {
            try (PerformanceLogger.PerformanceContext ctx = PerformanceLogger.start("updateUser")) {
                PerformanceLogger.enter("updateUser", user);
                
                serviceLogger.info("开始更新用户: %s", user);
                
                // 模拟业务逻辑
                if (random.nextBoolean()) {
                    serviceLogger.warn("用户 %s 的邮箱可能需要验证", user.getEmail());
                }
                
                // 模拟数据库操作
                simulateDbOperation("UPDATE", 30, 150);
                
                serviceLogger.info("用户更新成功: %s", user);
                PerformanceLogger.exit("updateUser");
            }
        }
        
        public void deleteUser(String userId) {
            try (PerformanceLogger.PerformanceContext ctx = PerformanceLogger.start("deleteUser")) {
                PerformanceLogger.enter("deleteUser", userId);
                
                serviceLogger.info("开始删除用户: %s", userId);
                
                // 模拟检查用户是否存在
                if (random.nextInt(10) == 0) {
                    throw new RuntimeException("用户不存在: " + userId);
                }
                
                // 模拟数据库操作
                simulateDbOperation("DELETE", 20, 100);
                
                serviceLogger.info("用户删除成功: %s", userId);
                PerformanceLogger.exit("deleteUser");
            }
        }
        
        private void validateUserInput(String name, String email) {
            PerformanceLogger.trace("验证用户输入参数");
            
            if (name == null || name.trim().isEmpty()) {
                throw new IllegalArgumentException("用户名不能为空");
            }
            
            if (email == null || !email.contains("@")) {
                throw new IllegalArgumentException("邮箱格式不正确");
            }
            
            serviceLogger.debug("用户输入验证通过: name=%s, email=%s", name, email);
        }
        
        private void simulateDbOperation(String operation, int minMs, int maxMs) {
            long startTime = System.nanoTime();
            
            try {
                int sleepTime = minMs + random.nextInt(maxMs - minMs);
                Thread.sleep(sleepTime);
                
                serviceLogger.debug("数据库%s操作完成，耗时: %d ms", operation, sleepTime);
            } catch (InterruptedException e) {
                serviceLogger.error("数据库操作被中断", e);
                Thread.currentThread().interrupt();
            }
            
            PerformanceLogger.logExecutionTime("DB_" + operation, startTime);
        }
    }
    
    /**
     * 模拟数据处理服务
     */
    static class DataProcessor {
        private final Logger processorLogger = Logger.getLogger("com.mhome.service.DataProcessor");
        
        public void processLargeDataSet() {
            try (PerformanceLogger.PerformanceContext ctx = PerformanceLogger.start("processLargeDataSet")) {
                processorLogger.info("开始处理大数据集...");
                
                // 模拟数据加载
                loadData();
                
                // 模拟数据处理
                processData();
                
                // 模拟数据保存
                saveData();
                
                processorLogger.info("大数据集处理完成");
            }
        }
        
        private void loadData() {
            try (PerformanceLogger.PerformanceContext ctx = PerformanceLogger.start("loadData")) {
                processorLogger.debug("加载数据...");
                
                // 模拟加载时间
                try {
                    Thread.sleep(100 + random.nextInt(200));
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                
                processorLogger.debug("数据加载完成，共加载 %d 条记录", 1000 + random.nextInt(5000));
            }
        }
        
        private void processData() {
            try (PerformanceLogger.PerformanceContext ctx = PerformanceLogger.start("processData")) {
                processorLogger.debug("处理数据...");
                
                // 模拟处理步骤
                for (int i = 1; i <= 3; i++) {
                    processStep(i);
                }
                
                processorLogger.debug("数据处理完成");
            }
        }
        
        private void processStep(int step) {
            try (PerformanceLogger.PerformanceContext ctx = PerformanceLogger.start("processStep" + step)) {
                processorLogger.trace("执行处理步骤 %d", step);
                
                try {
                    Thread.sleep(50 + random.nextInt(100));
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                
                if (step == 2 && random.nextInt(5) == 0) {
                    processorLogger.warn("处理步骤 %d 遇到警告，但继续执行", step);
                }
                
                processorLogger.trace("处理步骤 %d 完成", step);
            }
        }
        
        private void saveData() {
            try (PerformanceLogger.PerformanceContext ctx = PerformanceLogger.start("saveData")) {
                processorLogger.debug("保存处理结果...");
                
                try {
                    Thread.sleep(80 + random.nextInt(120));
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                
                processorLogger.debug("数据保存完成");
            }
        }
    }
}
