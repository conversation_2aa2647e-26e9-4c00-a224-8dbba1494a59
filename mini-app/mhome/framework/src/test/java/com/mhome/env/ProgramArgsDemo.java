package com.mhome.env;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 程序参数获取和使用演示
 */
public class ProgramArgsDemo {
    
    public static void main(String[] args) {
        // 重要：在程序开始时初始化参数
        ProcessUtils.initProgramArgs(args);
        
        System.out.println("=== 程序参数获取演示 ===\n");
        
        // 显示接收到的参数
        System.out.println("接收到的参数: " + Arrays.toString(args));
        
        // 演示各种参数获取方法
        demonstrateProgramArgsRetrieval();
        
        // 演示参数解析
        demonstrateProgramArgsParsing();
        
        // 演示重启功能
        if (args.length == 0 || !hasArg(args, "--restarted")) {
            demonstrateRestartWithArgs();
        } else {
            System.out.println("\n程序已重启！");
            showRestartInfo();
        }
    }
    
    /**
     * 演示程序参数获取
     */
    private static void demonstrateProgramArgsRetrieval() {
        System.out.println("\n=== 程序参数获取演示 ===");
        
        // 获取原始参数
        String[] originalArgs = ProcessUtils.getOriginalProgramArgs();
        System.out.println("原始参数: " + Arrays.toString(originalArgs));
        
        // 获取参数列表
        List<String> argsList = ProcessUtils.getProgramArgsList();
        System.out.println("参数列表: " + argsList);
        
        // 获取所有解析后的参数
        Map<String, String> allArgs = ProcessUtils.getAllProgramArgs();
        System.out.println("解析后的参数: " + allArgs);
        
        // 获取详细信息
        System.out.println("\n" + ProcessUtils.getProgramArgsInfo());
    }
    
    /**
     * 演示程序参数解析
     */
    private static void demonstrateProgramArgsParsing() {
        System.out.println("\n=== 程序参数解析演示 ===");
        
        // 获取具体参数值
        String host = ProcessUtils.getProgramArg("host", "localhost");
        String port = ProcessUtils.getProgramArg("port", "8080");
        boolean verbose = ProcessUtils.hasProgramArg("verbose");
        boolean debug = ProcessUtils.hasProgramArg("debug");
        String config = ProcessUtils.getProgramArg("config");
        
        System.out.println("解析结果:");
        System.out.println("  host: " + host);
        System.out.println("  port: " + port);
        System.out.println("  verbose: " + verbose);
        System.out.println("  debug: " + debug);
        System.out.println("  config: " + config);
        
        // 演示参数检查
        System.out.println("\n参数检查:");
        System.out.println("  是否有host参数: " + ProcessUtils.hasProgramArg("host"));
        System.out.println("  是否有verbose标志: " + ProcessUtils.hasProgramArg("verbose"));
        System.out.println("  是否有unknown参数: " + ProcessUtils.hasProgramArg("unknown"));
    }
    
    /**
     * 演示使用程序参数重启
     */
    private static void demonstrateRestartWithArgs() {
        System.out.println("\n=== 重启演示 ===");
        
        try {
            System.out.println("即将使用原始参数重启程序...");
            
            // 方式1: 使用原始参数重启
            if (ProcessUtils.getOriginalProgramArgs().length == 0) {
                System.out.println("当前无参数，添加一些参数重启...");
                Process process = ProcessUtils.restartWithExtraArgs(
                    ProgramArgsDemo.class,
                    "--host", "localhost",
                    "--port", "8080",
                    "--verbose",
                    "--restarted", "true",
                    "file1.txt", "file2.txt"
                );
                System.out.println("新进程已启动，PID: " + process.pid());
            } else {
                System.out.println("使用原始参数重启...");
                Process process = ProcessUtils.restartWithOriginalAndExtraArgs(
                    ProgramArgsDemo.class,
                    "--restarted", "true",
                    "--restart-time", String.valueOf(System.currentTimeMillis())
                );
                System.out.println("新进程已启动，PID: " + process.pid());
            }
            
            System.out.println("当前进程即将退出...");
            Thread.sleep(1000);
            System.exit(0);
            
        } catch (Exception e) {
            System.err.println("重启失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 显示重启信息
     */
    private static void showRestartInfo() {
        System.out.println("重启信息:");
        System.out.println("  重启时间: " + ProcessUtils.getProgramArg("restart-time"));
        System.out.println("  重启标志: " + ProcessUtils.getProgramArg("restarted"));
        
        // 显示所有参数
        System.out.println("  所有参数: " + ProcessUtils.getAllProgramArgs());
    }
    
    /**
     * 检查参数数组中是否包含某个参数
     */
    private static boolean hasArg(String[] args, String arg) {
        for (String a : args) {
            if (a.equals(arg)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 高级参数管理示例
     */
    public static class AdvancedArgsManager {
        
        /**
         * 配置更新重启示例
         */
        public static void restartWithNewConfig(String newConfigPath) {
            System.out.println("配置文件已更新，重启程序...");
            
            try {
                // 获取原始参数
                List<String> args = ProcessUtils.getProgramArgsList();
                
                // 更新配置文件参数
                boolean configUpdated = false;
                for (int i = 0; i < args.size(); i++) {
                    if ("--config".equals(args.get(i)) && i + 1 < args.size()) {
                        args.set(i + 1, newConfigPath);
                        configUpdated = true;
                        break;
                    }
                }
                
                // 如果原来没有配置参数，添加新的
                if (!configUpdated) {
                    args.add("--config");
                    args.add(newConfigPath);
                }
                
                // 添加重启原因
                args.add("--restart-reason");
                args.add("config-updated");
                
                // 使用当前JVM参数和新的程序参数重启
                List<String> jvmArgs = ProcessUtils.getFilteredJvmArgs();
                ProcessUtils.restartWithJvmArgs(
                    ProgramArgsDemo.class,
                    jvmArgs.toArray(new String[0]),
                    args.toArray(new String[0])
                );
                
                System.exit(0);
            } catch (Exception e) {
                System.err.println("配置更新重启失败: " + e.getMessage());
            }
        }
        
        /**
         * 参数验证和修正重启
         */
        public static void validateAndRestartIfNeeded() {
            String host = ProcessUtils.getProgramArg("host");
            String port = ProcessUtils.getProgramArg("port");
            
            boolean needRestart = false;
            List<String> newArgs = ProcessUtils.getProgramArgsList();
            
            // 验证host参数
            if (host == null || host.isEmpty()) {
                System.out.println("host参数缺失，设置默认值");
                newArgs.add("--host");
                newArgs.add("localhost");
                needRestart = true;
            }
            
            // 验证port参数
            if (port == null || !isValidPort(port)) {
                System.out.println("port参数无效，设置默认值");
                // 移除旧的port参数
                for (int i = 0; i < newArgs.size() - 1; i++) {
                    if ("--port".equals(newArgs.get(i))) {
                        newArgs.remove(i + 1); // 移除值
                        newArgs.remove(i);     // 移除键
                        break;
                    }
                }
                newArgs.add("--port");
                newArgs.add("8080");
                needRestart = true;
            }
            
            if (needRestart) {
                System.out.println("参数验证失败，使用修正后的参数重启...");
                newArgs.add("--restart-reason");
                newArgs.add("parameter-validation");
                
                ProcessUtils.restartWithCurrentJvmArgs(
                    ProgramArgsDemo.class,
                    newArgs.toArray(new String[0])
                );
                System.exit(0);
            } else {
                System.out.println("参数验证通过");
            }
        }
        
        private static boolean isValidPort(String port) {
            try {
                int p = Integer.parseInt(port);
                return p > 0 && p <= 65535;
            } catch (NumberFormatException e) {
                return false;
            }
        }
        
        /**
         * 动态参数调整重启
         */
        public static void adjustParametersAndRestart() {
            Map<String, String> currentArgs = ProcessUtils.getAllProgramArgs();
            
            // 根据运行时状态调整参数
            Runtime runtime = Runtime.getRuntime();
            long maxMemory = runtime.maxMemory();
            
            List<String> newArgs = ProcessUtils.getProgramArgsList();
            
            // 如果内存较大，启用高性能模式
            if (maxMemory > 2L * 1024 * 1024 * 1024) { // 大于2GB
                if (!currentArgs.containsKey("high-performance")) {
                    newArgs.add("--high-performance");
                    newArgs.add("true");
                }
            }
            
            // 根据CPU核心数调整线程数
            int processors = runtime.availableProcessors();
            String currentThreads = currentArgs.get("threads");
            int optimalThreads = Math.max(2, processors - 1);
            
            if (currentThreads == null || Integer.parseInt(currentThreads) != optimalThreads) {
                // 移除旧的threads参数
                for (int i = 0; i < newArgs.size() - 1; i++) {
                    if ("--threads".equals(newArgs.get(i))) {
                        newArgs.remove(i + 1);
                        newArgs.remove(i);
                        break;
                    }
                }
                newArgs.add("--threads");
                newArgs.add(String.valueOf(optimalThreads));
            }
            
            newArgs.add("--restart-reason");
            newArgs.add("parameter-optimization");
            
            System.out.println("使用优化后的参数重启: " + newArgs);
            ProcessUtils.restartWithCurrentJvmArgs(
                ProgramArgsDemo.class,
                newArgs.toArray(new String[0])
            );
            System.exit(0);
        }
    }
}
