package com.mhome.env;

/**
 * ProcessUtils 测试和使用示例
 */
public class ProcessUtilsTest {
    
    public static void main(String[] args) {
        System.out.println("=== ProcessUtils 测试 ===");
        System.out.println("接收到的参数: " + java.util.Arrays.toString(args));
        
        // 如果没有参数，演示重启功能
        if (args.length == 0) {
            demonstrateRestart();
        } else {
            // 有参数时，显示参数并正常运行
            System.out.println("程序正常运行，参数:");
            for (int i = 0; i < args.length; i++) {
                System.out.println("  args[" + i + "] = " + args[i]);
            }
            
            // 解析参数示例
            parseAndDisplayArgs(args);
        }
    }
    
    /**
     * 演示重启功能
     */
    private static void demonstrateRestart() {
        System.out.println("\n=== 演示程序重启功能 ===");
        
        try {
            System.out.println("1. 基本重启（无参数）");
            // ProcessUtils.restart(ProcessUtilsTest.class);
            
            System.out.println("2. 带程序参数重启");
            Process process = ProcessUtils.restartWithExtraArgs(
                ProcessUtilsTest.class, 
                "--host", "localhost", 
                "--port", "8080", 
                "--verbose",
                "file1.txt", "file2.txt"
            );
            
            System.out.println("重启进程已启动，PID: " + process.pid());
            
            // 等待一段时间让新进程启动
            Thread.sleep(2000);
            
            System.out.println("3. 带JVM参数和程序参数重启");
            String[] jvmArgs = {"-Xmx512m", "-Dfile.encoding=UTF-8", "-Duser.timezone=Asia/Shanghai"};
            String[] programArgs = {"--config", "/path/to/config", "--debug", "input.txt"};
            
            Process process2 = ProcessUtils.restartWithJvmArgs(
                ProcessUtilsTest.class, 
                jvmArgs, 
                programArgs
            );
            
            System.out.println("带JVM参数的重启进程已启动，PID: " + process2.pid());
            
            // 当前进程退出
            System.out.println("当前进程即将退出...");
            System.exit(0);
            
        } catch (Exception e) {
            System.err.println("重启失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 解析和显示参数
     */
    private static void parseAndDisplayArgs(String[] args) {
        System.out.println("\n=== 参数解析示例 ===");
        
        // 使用简单的参数解析
        SimpleArgsParser parser = new SimpleArgsParser();
        SimpleArgs result = parser.parse(args);
        
        System.out.println("解析结果:");
        System.out.println("  选项: " + result.getOptions());
        System.out.println("  位置参数: " + result.getPositionalArgs());
        
        // 演示具体参数访问
        System.out.println("\n具体参数:");
        System.out.println("  host: " + result.getOption("host", "localhost"));
        System.out.println("  port: " + result.getOption("port", "8080"));
        System.out.println("  verbose: " + result.hasFlag("verbose"));
        System.out.println("  debug: " + result.hasFlag("debug"));
        System.out.println("  config: " + result.getOption("config"));
        System.out.println("  文件列表: " + result.getPositionalArgs());
    }
    
    /**
     * 简单的参数解析器
     */
    static class SimpleArgsParser {
        public SimpleArgs parse(String[] args) {
            java.util.Map<String, String> options = new java.util.HashMap<>();
            java.util.Set<String> flags = new java.util.HashSet<>();
            java.util.List<String> positionalArgs = new java.util.ArrayList<>();
            
            for (int i = 0; i < args.length; i++) {
                String arg = args[i];
                
                if (arg.startsWith("--")) {
                    String option = arg.substring(2);
                    if (i + 1 < args.length && !args[i + 1].startsWith("-")) {
                        // 有值的选项
                        options.put(option, args[++i]);
                    } else {
                        // 标志选项
                        flags.add(option);
                    }
                } else if (arg.startsWith("-")) {
                    String option = arg.substring(1);
                    if (i + 1 < args.length && !args[i + 1].startsWith("-")) {
                        // 有值的选项
                        options.put(option, args[++i]);
                    } else {
                        // 标志选项
                        flags.add(option);
                    }
                } else {
                    // 位置参数
                    positionalArgs.add(arg);
                }
            }
            
            return new SimpleArgs(options, flags, positionalArgs);
        }
    }
    
    /**
     * 简单的参数结果
     */
    static class SimpleArgs {
        private final java.util.Map<String, String> options;
        private final java.util.Set<String> flags;
        private final java.util.List<String> positionalArgs;
        
        SimpleArgs(java.util.Map<String, String> options, java.util.Set<String> flags, 
                  java.util.List<String> positionalArgs) {
            this.options = options;
            this.flags = flags;
            this.positionalArgs = positionalArgs;
        }
        
        public String getOption(String key) {
            return options.get(key);
        }
        
        public String getOption(String key, String defaultValue) {
            return options.getOrDefault(key, defaultValue);
        }
        
        public boolean hasFlag(String key) {
            return flags.contains(key);
        }
        
        public java.util.Map<String, String> getOptions() {
            return new java.util.HashMap<>(options);
        }
        
        public java.util.Set<String> getFlags() {
            return new java.util.HashSet<>(flags);
        }
        
        public java.util.List<String> getPositionalArgs() {
            return new java.util.ArrayList<>(positionalArgs);
        }
        
        @Override
        public String toString() {
            return String.format("SimpleArgs{options=%s, flags=%s, positionalArgs=%s}", 
                               options, flags, positionalArgs);
        }
    }
    
    /**
     * 演示如何在实际应用中使用
     */
    public static class RestartExample {
        public static void restartWithNewConfig(String configPath) {
            try {
                // 重启程序并传递新的配置文件路径
                ProcessUtils.restartWithExtraArgs(
                    RestartExample.class,
                    "--config", configPath,
                    "--restart-reason", "config-change"
                );
                
                // 当前进程退出
                System.exit(0);
            } catch (Exception e) {
                System.err.println("重启失败: " + e.getMessage());
            }
        }
        
        public static void restartWithMoreMemory() {
            try {
                // 重启程序并分配更多内存
                String[] jvmArgs = {"-Xmx2g", "-XX:+UseG1GC"};
                String[] programArgs = {"--restart-reason", "memory-upgrade"};
                
                ProcessUtils.restartWithJvmArgs(
                    RestartExample.class,
                    jvmArgs,
                    programArgs
                );
                
                // 当前进程退出
                System.exit(0);
            } catch (Exception e) {
                System.err.println("重启失败: " + e.getMessage());
            }
        }
    }
}
