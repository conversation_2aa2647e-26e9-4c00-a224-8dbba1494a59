package com.mhome.env;

import java.util.Arrays;
import java.util.Map;

/**
 * 动态获取程序参数演示
 * 无需依赖main方法的args参数，直接从系统中获取
 */
public class DynamicArgsDemo {
    
    public static void main(String[] args) {
        System.out.println("=== 动态程序参数获取演示 ===\n");
        
        // 显示接收到的参数（用于对比）
        System.out.println("main方法接收到的参数: " + Arrays.toString(args));
        
        // 演示动态获取功能
        demonstrateDynamicArgsRetrieval();
        
        // 演示参数解析
        demonstrateDynamicArgsParsing();
        
        // 演示重启功能
        if (args.length == 0 || !hasArg(args, "--restarted")) {
            demonstrateRestartWithDynamicArgs();
        } else {
            System.out.println("\n程序已重启！");
            showDynamicRestartInfo();
        }
    }
    
    /**
     * 演示动态参数获取
     */
    private static void demonstrateDynamicArgsRetrieval() {
        System.out.println("\n=== 动态参数获取演示 ===");
        
        try {
            // 获取完整命令行
            String[] fullCommandLine = ProcessUtils.getDynamicCommandLineArgs();
            System.out.println("完整命令行: " + Arrays.toString(fullCommandLine));
            
            // 获取程序参数
            String[] programArgs = ProcessUtils.getDynamicProgramArgs();
            System.out.println("程序参数: " + Arrays.toString(programArgs));
            
            // 获取参数映射
            Map<String, String> argsMap = ProcessUtils.getDynamicProgramArgsMap();
            System.out.println("参数映射: " + argsMap);
            
            // 获取详细信息
            System.out.println("\n" + ProcessUtils.getDynamicProgramArgsInfo());
            
        } catch (Exception e) {
            System.err.println("动态获取参数失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 演示动态参数解析
     */
    private static void demonstrateDynamicArgsParsing() {
        System.out.println("\n=== 动态参数解析演示 ===");
        
        try {
            // 获取具体参数值
            String host = ProcessUtils.getDynamicProgramArg("host", "localhost");
            String port = ProcessUtils.getDynamicProgramArg("port", "8080");
            boolean verbose = ProcessUtils.hasDynamicProgramArg("verbose");
            boolean debug = ProcessUtils.hasDynamicProgramArg("debug");
            String config = ProcessUtils.getDynamicProgramArg("config");
            
            System.out.println("动态解析结果:");
            System.out.println("  host: " + host);
            System.out.println("  port: " + port);
            System.out.println("  verbose: " + verbose);
            System.out.println("  debug: " + debug);
            System.out.println("  config: " + config);
            
            // 演示参数检查
            System.out.println("\n参数检查:");
            System.out.println("  是否有host参数: " + ProcessUtils.hasDynamicProgramArg("host"));
            System.out.println("  是否有verbose标志: " + ProcessUtils.hasDynamicProgramArg("verbose"));
            System.out.println("  是否有unknown参数: " + ProcessUtils.hasDynamicProgramArg("unknown"));
            
        } catch (Exception e) {
            System.err.println("动态解析参数失败: " + e.getMessage());
        }
    }
    
    /**
     * 演示使用动态参数重启
     */
    private static void demonstrateRestartWithDynamicArgs() {
        System.out.println("\n=== 动态参数重启演示 ===");
        
        try {
            String[] currentDynamicArgs = ProcessUtils.getDynamicProgramArgs();
            System.out.println("当前动态获取的参数: " + Arrays.toString(currentDynamicArgs));
            
            if (currentDynamicArgs.length == 0) {
                System.out.println("当前无参数，添加一些参数重启...");
                ProcessUtils.restartWithExtraArgs(
                    DynamicArgsDemo.class,
                    "--host", "localhost",
                    "--port", "8080",
                    "--verbose",
                    "--restarted", "true",
                    "file1.txt", "file2.txt"
                );
            } else {
                System.out.println("使用动态获取的参数重启...");
                ProcessUtils.restartWithDynamicAndExtraArgs(
                    DynamicArgsDemo.class,
                    "--restarted", "true",
                    "--restart-time", String.valueOf(System.currentTimeMillis())
                );
            }
            
            System.out.println("新进程即将启动，当前进程退出...");
            Thread.sleep(1000);
            System.exit(0);
            
        } catch (Exception e) {
            System.err.println("动态重启失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 显示动态重启信息
     */
    private static void showDynamicRestartInfo() {
        System.out.println("动态重启信息:");
        System.out.println("  重启时间: " + ProcessUtils.getDynamicProgramArg("restart-time"));
        System.out.println("  重启标志: " + ProcessUtils.getDynamicProgramArg("restarted"));
        
        // 显示所有动态获取的参数
        System.out.println("  所有动态参数: " + ProcessUtils.getDynamicProgramArgsMap());
        
        // 显示完整进程信息
        System.out.println("\n" + ProcessUtils.getCompleteProcessInfo());
    }
    
    /**
     * 检查参数数组中是否包含某个参数
     */
    private static boolean hasArg(String[] args, String arg) {
        for (String a : args) {
            if (a.equals(arg)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 高级动态参数管理示例
     */
    public static class AdvancedDynamicArgsManager {
        
        /**
         * 动态参数监控和自动调整
         */
        public static void monitorAndAdjustArgs() {
            System.out.println("开始动态参数监控...");
            
            // 创建监控线程
            Thread monitorThread = new Thread(() -> {
                while (true) {
                    try {
                        // 每10秒检查一次参数
                        Thread.sleep(10000);
                        
                        Map<String, String> currentArgs = ProcessUtils.getDynamicProgramArgsMap();
                        System.out.println("当前动态参数: " + currentArgs);
                        
                        // 检查是否需要调整参数
                        if (needsParameterAdjustment(currentArgs)) {
                            adjustParametersAndRestart();
                            break;
                        }
                        
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            });
            
            monitorThread.setDaemon(true);
            monitorThread.start();
        }
        
        private static boolean needsParameterAdjustment(Map<String, String> args) {
            // 示例：检查是否需要调整参数
            String port = args.get("port");
            if (port != null) {
                try {
                    int portNum = Integer.parseInt(port);
                    // 如果端口号小于1024，需要调整
                    return portNum < 1024;
                } catch (NumberFormatException e) {
                    return true; // 端口号格式错误，需要调整
                }
            }
            return false;
        }
        
        private static void adjustParametersAndRestart() {
            System.out.println("检测到参数需要调整，准备重启...");
            
            try {
                String[] currentArgs = ProcessUtils.getDynamicProgramArgs();
                java.util.List<String> newArgs = new java.util.ArrayList<>();
                
                // 复制现有参数并调整
                for (int i = 0; i < currentArgs.length; i++) {
                    String arg = currentArgs[i];
                    if ("--port".equals(arg) && i + 1 < currentArgs.length) {
                        newArgs.add(arg);
                        newArgs.add("8080"); // 调整为8080端口
                        i++; // 跳过原来的端口值
                    } else {
                        newArgs.add(arg);
                    }
                }
                
                // 添加调整标记
                newArgs.add("--adjusted");
                newArgs.add("true");
                newArgs.add("--adjust-time");
                newArgs.add(String.valueOf(System.currentTimeMillis()));
                
                // 使用当前JVM参数和调整后的程序参数重启
                ProcessUtils.restartWithCurrentJvmArgs(
                    DynamicArgsDemo.class,
                    newArgs.toArray(new String[0])
                );
                
                System.exit(0);
            } catch (Exception e) {
                System.err.println("参数调整重启失败: " + e.getMessage());
            }
        }
        
        /**
         * 配置热更新示例
         */
        public static void handleConfigHotReload(String newConfigPath) {
            System.out.println("配置热更新，使用动态参数重启...");
            
            try {
                // 获取当前动态参数
                String[] currentArgs = ProcessUtils.getDynamicProgramArgs();
                java.util.List<String> newArgs = new java.util.ArrayList<>();
                
                // 更新配置文件参数
                boolean configUpdated = false;
                for (int i = 0; i < currentArgs.length; i++) {
                    String arg = currentArgs[i];
                    if ("--config".equals(arg) && i + 1 < currentArgs.length) {
                        newArgs.add(arg);
                        newArgs.add(newConfigPath); // 新的配置文件路径
                        i++; // 跳过原来的配置文件路径
                        configUpdated = true;
                    } else {
                        newArgs.add(arg);
                    }
                }
                
                // 如果原来没有配置参数，添加新的
                if (!configUpdated) {
                    newArgs.add("--config");
                    newArgs.add(newConfigPath);
                }
                
                // 添加热更新标记
                newArgs.add("--hot-reload");
                newArgs.add("true");
                newArgs.add("--reload-time");
                newArgs.add(String.valueOf(System.currentTimeMillis()));
                
                // 重启
                ProcessUtils.restartWithCurrentJvmAndDynamicArgs(DynamicArgsDemo.class);
                System.exit(0);
                
            } catch (Exception e) {
                System.err.println("配置热更新失败: " + e.getMessage());
            }
        }
        
        /**
         * 显示参数对比
         */
        public static void compareStaticAndDynamicArgs(String[] staticArgs) {
            System.out.println("\n=== 静态参数 vs 动态参数对比 ===");
            
            String[] dynamicArgs = ProcessUtils.getDynamicProgramArgs();
            Map<String, String> dynamicArgsMap = ProcessUtils.getDynamicProgramArgsMap();
            
            System.out.println("静态参数 (main方法): " + Arrays.toString(staticArgs));
            System.out.println("动态参数 (系统获取): " + Arrays.toString(dynamicArgs));
            System.out.println("动态参数映射: " + dynamicArgsMap);
            
            // 检查一致性
            boolean consistent = Arrays.equals(staticArgs, dynamicArgs);
            System.out.println("参数一致性: " + (consistent ? "一致" : "不一致"));
            
            if (!consistent) {
                System.out.println("注意：静态参数和动态参数不一致，可能是由于：");
                System.out.println("  1. 系统获取方法的限制");
                System.out.println("  2. 参数解析逻辑的差异");
                System.out.println("  3. 平台兼容性问题");
            }
        }
    }
}
