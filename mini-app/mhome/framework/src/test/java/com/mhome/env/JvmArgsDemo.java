package com.mhome.env;

import java.util.List;

/**
 * JVM参数获取和使用演示
 */
public class JvmArgsDemo {
    
    public static void main(String[] args) {
        System.out.println("=== JVM参数获取演示 ===\n");
        
        // 显示完整的JVM信息
        System.out.println(ProcessUtils.getJvmInfo());
        
        // 演示参数获取
        demonstrateJvmArgsRetrieval();
        
        // 演示重启功能
        if (args.length == 0) {
            demonstrateRestartWithJvmArgs();
        } else {
            System.out.println("\n程序已重启，接收到参数: " + java.util.Arrays.toString(args));
        }
    }
    
    /**
     * 演示JVM参数获取
     */
    private static void demonstrateJvmArgsRetrieval() {
        System.out.println("\n=== JVM参数获取演示 ===");
        
        // 获取所有JVM参数
        List<String> allJvmArgs = ProcessUtils.getCurrentJvmArgs();
        System.out.println("所有JVM参数 (" + allJvmArgs.size() + " 个):");
        for (int i = 0; i < allJvmArgs.size(); i++) {
            System.out.println("  [" + i + "] " + allJvmArgs.get(i));
        }
        
        // 获取过滤后的JVM参数
        List<String> filteredJvmArgs = ProcessUtils.getFilteredJvmArgs();
        System.out.println("\n过滤后的JVM参数 (" + filteredJvmArgs.size() + " 个):");
        for (int i = 0; i < filteredJvmArgs.size(); i++) {
            System.out.println("  [" + i + "] " + filteredJvmArgs.get(i));
        }
        
        // 分析JVM参数
        analyzeJvmArgs(allJvmArgs);
    }
    
    /**
     * 分析JVM参数
     */
    private static void analyzeJvmArgs(List<String> jvmArgs) {
        System.out.println("\n=== JVM参数分析 ===");
        
        String heapSize = "未设置";
        String gcType = "默认";
        boolean debugMode = false;
        boolean serverMode = false;
        
        for (String arg : jvmArgs) {
            if (arg.startsWith("-Xmx")) {
                heapSize = arg.substring(4);
            } else if (arg.startsWith("-XX:+Use") && arg.contains("GC")) {
                gcType = arg.substring(7); // 去掉 "-XX:+Use"
            } else if (arg.contains("debug") || arg.contains("jdwp")) {
                debugMode = true;
            } else if (arg.equals("-server")) {
                serverMode = true;
            }
        }
        
        System.out.println("最大堆内存: " + heapSize);
        System.out.println("GC类型: " + gcType);
        System.out.println("调试模式: " + (debugMode ? "是" : "否"));
        System.out.println("服务器模式: " + (serverMode ? "是" : "否"));
    }
    
    /**
     * 演示使用当前JVM参数重启
     */
    private static void demonstrateRestartWithJvmArgs() {
        System.out.println("\n=== 重启演示 ===");
        
        try {
            System.out.println("即将使用当前JVM参数重启程序...");
            
            // 使用当前JVM参数重启，并传递一些程序参数
            Process process = ProcessUtils.restartWithCurrentJvmArgs(
                JvmArgsDemo.class,
                "--restarted", "true",
                "--timestamp", String.valueOf(System.currentTimeMillis())
            );
            
            System.out.println("新进程已启动，PID: " + process.pid());
            System.out.println("当前进程即将退出...");
            
            // 等待一下让新进程启动
            Thread.sleep(1000);
            System.exit(0);
            
        } catch (Exception e) {
            System.err.println("重启失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 演示自定义JVM参数重启
     */
    public static void restartWithCustomJvmArgs() {
        System.out.println("使用自定义JVM参数重启...");
        
        try {
            // 获取当前JVM参数作为基础
            List<String> currentArgs = ProcessUtils.getFilteredJvmArgs();
            
            // 添加或修改一些参数
            List<String> newJvmArgs = new java.util.ArrayList<>(currentArgs);
            
            // 增加堆内存
            boolean hasMaxHeap = false;
            for (int i = 0; i < newJvmArgs.size(); i++) {
                if (newJvmArgs.get(i).startsWith("-Xmx")) {
                    newJvmArgs.set(i, "-Xmx2g"); // 设置为2GB
                    hasMaxHeap = true;
                    break;
                }
            }
            if (!hasMaxHeap) {
                newJvmArgs.add("-Xmx2g");
            }
            
            // 添加GC参数
            newJvmArgs.add("-XX:+UseG1GC");
            newJvmArgs.add("-XX:+PrintGCDetails");
            
            // 添加系统属性
            newJvmArgs.add("-Dfile.encoding=UTF-8");
            newJvmArgs.add("-Duser.timezone=Asia/Shanghai");
            
            System.out.println("新的JVM参数:");
            for (String arg : newJvmArgs) {
                System.out.println("  " + arg);
            }
            
            // 重启
            String[] programArgs = {"--custom-restart", "true"};
            Process process = ProcessUtils.restartWithJvmArgs(
                JvmArgsDemo.class,
                newJvmArgs.toArray(new String[0]),
                programArgs
            );
            
            System.out.println("自定义参数重启成功，PID: " + process.pid());
            System.exit(0);
            
        } catch (Exception e) {
            System.err.println("自定义重启失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 内存监控和自动重启示例
     */
    public static class MemoryMonitor {
        private static final long MAX_MEMORY_USAGE = 1024 * 1024 * 1024; // 1GB
        
        public static void startMonitoring() {
            Thread monitorThread = new Thread(() -> {
                while (true) {
                    try {
                        Runtime runtime = Runtime.getRuntime();
                        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
                        long maxMemory = runtime.maxMemory();
                        
                        double usagePercent = (double) usedMemory / maxMemory * 100;
                        
                        System.out.printf("内存使用: %d MB / %d MB (%.1f%%)\n",
                            usedMemory / 1024 / 1024,
                            maxMemory / 1024 / 1024,
                            usagePercent);
                        
                        // 如果内存使用超过90%，考虑重启
                        if (usagePercent > 90) {
                            System.out.println("内存使用过高，准备重启...");
                            restartWithMoreMemory();
                            break;
                        }
                        
                        Thread.sleep(5000); // 每5秒检查一次
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            });
            
            monitorThread.setDaemon(true);
            monitorThread.start();
        }
        
        private static void restartWithMoreMemory() {
            try {
                List<String> jvmArgs = ProcessUtils.getFilteredJvmArgs();
                
                // 增加堆内存
                for (int i = 0; i < jvmArgs.size(); i++) {
                    if (jvmArgs.get(i).startsWith("-Xmx")) {
                        // 将当前内存翻倍
                        String currentMax = jvmArgs.get(i).substring(4);
                        long currentBytes = parseMemorySize(currentMax);
                        long newBytes = currentBytes * 2;
                        jvmArgs.set(i, "-Xmx" + formatMemorySize(newBytes));
                        break;
                    }
                }
                
                System.out.println("使用更大内存重启: " + jvmArgs);
                
                ProcessUtils.restartWithJvmArgs(
                    JvmArgsDemo.class,
                    jvmArgs.toArray(new String[0]),
                    new String[]{"--restart-reason", "memory-upgrade"}
                );
                
                System.exit(0);
            } catch (Exception e) {
                System.err.println("内存升级重启失败: " + e.getMessage());
            }
        }
        
        private static long parseMemorySize(String size) {
            // 简单的内存大小解析
            if (size.endsWith("g") || size.endsWith("G")) {
                return Long.parseLong(size.substring(0, size.length() - 1)) * 1024 * 1024 * 1024;
            } else if (size.endsWith("m") || size.endsWith("M")) {
                return Long.parseLong(size.substring(0, size.length() - 1)) * 1024 * 1024;
            } else if (size.endsWith("k") || size.endsWith("K")) {
                return Long.parseLong(size.substring(0, size.length() - 1)) * 1024;
            } else {
                return Long.parseLong(size);
            }
        }
        
        private static String formatMemorySize(long bytes) {
            if (bytes >= 1024 * 1024 * 1024) {
                return (bytes / (1024 * 1024 * 1024)) + "g";
            } else if (bytes >= 1024 * 1024) {
                return (bytes / (1024 * 1024)) + "m";
            } else if (bytes >= 1024) {
                return (bytes / 1024) + "k";
            } else {
                return String.valueOf(bytes);
            }
        }
    }
}
