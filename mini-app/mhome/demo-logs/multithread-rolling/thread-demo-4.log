2025-07-11 17:37:25.877 [pool-1-thread-1] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-1] - 线程 1 消息 7: 多线程环境下的日志滚动测试。这条消息来自线程 1，消息编号 7，时间戳 1752226645877
2025-07-11 17:37:25.882 [pool-1-thread-3] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-3] - 线程 3 消息 7: 多线程环境下的日志滚动测试。这条消息来自线程 3，消息编号 7，时间戳 1752226645882
2025-07-11 17:37:25.882 [pool-1-thread-2] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-2] - 线程 2 消息 7: 多线程环境下的日志滚动测试。这条消息来自线程 2，消息编号 7，时间戳 1752226645882
2025-07-11 17:37:25.890 [pool-1-thread-1] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-1] - 线程 1 消息 8: 多线程环境下的日志滚动测试。这条消息来自线程 1，消息编号 8，时间戳 1752226645890
2025-07-11 17:37:25.894 [pool-1-thread-3] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-3] - 线程 3 消息 8: 多线程环境下的日志滚动测试。这条消息来自线程 3，消息编号 8，时间戳 1752226645894
2025-07-11 17:37:25.894 [pool-1-thread-2] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-2] - 线程 2 消息 8: 多线程环境下的日志滚动测试。这条消息来自线程 2，消息编号 8，时间戳 1752226645894
2025-07-11 17:37:25.901 [pool-1-thread-1] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-1] - 线程 1 消息 9: 多线程环境下的日志滚动测试。这条消息来自线程 1，消息编号 9，时间戳 1752226645901
2025-07-11 17:37:25.907 [pool-1-thread-3] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-3] - 线程 3 消息 9: 多线程环境下的日志滚动测试。这条消息来自线程 3，消息编号 9，时间戳 1752226645907
2025-07-11 17:37:25.907 [pool-1-thread-2] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-2] - 线程 2 消息 9: 多线程环境下的日志滚动测试。这条消息来自线程 2，消息编号 9，时间戳 1752226645907
2025-07-11 17:37:25.914 [pool-1-thread-1] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-1] - 线程 1 消息 10: 多线程环境下的日志滚动测试。这条消息来自线程 1，消息编号 10，时间戳 1752226645913
2025-07-11 17:37:25.919 [pool-1-thread-2] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-2] - 线程 2 消息 10: 多线程环境下的日志滚动测试。这条消息来自线程 2，消息编号 10，时间戳 1752226645919
2025-07-11 17:37:25.919 [pool-1-thread-3] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-3] - 线程 3 消息 10: 多线程环境下的日志滚动测试。这条消息来自线程 3，消息编号 10，时间戳 1752226645919
2025-07-11 17:37:25.926 [pool-1-thread-1] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-1] - 线程 1 消息 11: 多线程环境下的日志滚动测试。这条消息来自线程 1，消息编号 11，时间戳 1752226645926
2025-07-11 17:37:25.930 [pool-1-thread-3] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-3] - 线程 3 消息 11: 多线程环境下的日志滚动测试。这条消息来自线程 3，消息编号 11，时间戳 1752226645930
2025-07-11 17:37:25.930 [pool-1-thread-2] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-2] - 线程 2 消息 11: 多线程环境下的日志滚动测试。这条消息来自线程 2，消息编号 11，时间戳 1752226645930
2025-07-11 17:37:25.937 [pool-1-thread-1] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-1] - 线程 1 消息 12: 多线程环境下的日志滚动测试。这条消息来自线程 1，消息编号 12，时间戳 1752226645937
2025-07-11 17:37:25.941 [pool-1-thread-3] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-3] - 线程 3 消息 12: 多线程环境下的日志滚动测试。这条消息来自线程 3，消息编号 12，时间戳 1752226645940
2025-07-11 17:37:25.941 [pool-1-thread-2] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-2] - 线程 2 消息 12: 多线程环境下的日志滚动测试。这条消息来自线程 2，消息编号 12，时间戳 1752226645940
