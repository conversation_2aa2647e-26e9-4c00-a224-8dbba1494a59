2025-07-11 17:37:26.011 [pool-1-thread-2] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-2] - 线程 2 消息 18: 多线程环境下的日志滚动测试。这条消息来自线程 2，消息编号 18，时间戳 1752226646011
2025-07-11 17:37:26.019 [pool-1-thread-3] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-3] - 线程 3 消息 19: 多线程环境下的日志滚动测试。这条消息来自线程 3，消息编号 19，时间戳 1752226646019
2025-07-11 17:37:26.020 [pool-1-thread-1] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-1] - 线程 1 消息 19: 多线程环境下的日志滚动测试。这条消息来自线程 1，消息编号 19，时间戳 1752226646020
2025-07-11 17:37:26.025 [pool-1-thread-2] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-2] - 线程 2 消息 19: 多线程环境下的日志滚动测试。这条消息来自线程 2，消息编号 19，时间戳 1752226646025
2025-07-11 17:37:26.032 [pool-1-thread-1] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-1] - 线程 1 消息 20: 多线程环境下的日志滚动测试。这条消息来自线程 1，消息编号 20，时间戳 1752226646032
2025-07-11 17:37:26.032 [pool-1-thread-3] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-3] - 线程 3 消息 20: 多线程环境下的日志滚动测试。这条消息来自线程 3，消息编号 20，时间戳 1752226646032
2025-07-11 17:37:26.037 [pool-1-thread-2] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-2] - 线程 2 消息 20: 多线程环境下的日志滚动测试。这条消息来自线程 2，消息编号 20，时间戳 1752226646037
2025-07-11 17:37:26.043 [pool-1-thread-1] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-1] - 线程 1 消息 21: 多线程环境下的日志滚动测试。这条消息来自线程 1，消息编号 21，时间戳 1752226646043
2025-07-11 17:37:26.043 [pool-1-thread-3] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-3] - 线程 3 消息 21: 多线程环境下的日志滚动测试。这条消息来自线程 3，消息编号 21，时间戳 1752226646043
2025-07-11 17:37:26.047 [pool-1-thread-2] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-2] - 线程 2 消息 21: 多线程环境下的日志滚动测试。这条消息来自线程 2，消息编号 21，时间戳 1752226646047
2025-07-11 17:37:26.054 [pool-1-thread-3] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-3] - 线程 3 消息 22: 多线程环境下的日志滚动测试。这条消息来自线程 3，消息编号 22，时间戳 1752226646054
2025-07-11 17:37:26.054 [pool-1-thread-1] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-1] - 线程 1 消息 22: 多线程环境下的日志滚动测试。这条消息来自线程 1，消息编号 22，时间戳 1752226646054
2025-07-11 17:37:26.059 [pool-1-thread-2] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-2] - 线程 2 消息 22: 多线程环境下的日志滚动测试。这条消息来自线程 2，消息编号 22，时间戳 1752226646058
2025-07-11 17:37:26.067 [pool-1-thread-3] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-3] - 线程 3 消息 23: 多线程环境下的日志滚动测试。这条消息来自线程 3，消息编号 23，时间戳 1752226646066
2025-07-11 17:37:26.067 [pool-1-thread-1] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-1] - 线程 1 消息 23: 多线程环境下的日志滚动测试。这条消息来自线程 1，消息编号 23，时间戳 1752226646066
2025-07-11 17:37:26.070 [pool-1-thread-2] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-2] - 线程 2 消息 23: 多线程环境下的日志滚动测试。这条消息来自线程 2，消息编号 23，时间戳 1752226646070
2025-07-11 17:37:26.079 [pool-1-thread-1] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-1] - 线程 1 消息 24: 多线程环境下的日志滚动测试。这条消息来自线程 1，消息编号 24，时间戳 1752226646079
2025-07-11 17:37:26.079 [pool-1-thread-3] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-3] - 线程 3 消息 24: 多线程环境下的日志滚动测试。这条消息来自线程 3，消息编号 24，时间戳 1752226646079
