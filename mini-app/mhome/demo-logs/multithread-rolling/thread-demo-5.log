2025-07-11 17:37:25.811 [pool-1-thread-1] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-1] - 线程 1 消息 1: 多线程环境下的日志滚动测试。这条消息来自线程 1，消息编号 1，时间戳 1752226645811
2025-07-11 17:37:25.811 [pool-1-thread-3] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-3] - 线程 3 消息 1: 多线程环境下的日志滚动测试。这条消息来自线程 3，消息编号 1，时间戳 1752226645811
2025-07-11 17:37:25.811 [pool-1-thread-2] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-2] - 线程 2 消息 1: 多线程环境下的日志滚动测试。这条消息来自线程 2，消息编号 1，时间戳 1752226645811
2025-07-11 17:37:25.823 [pool-1-thread-3] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-3] - 线程 3 消息 2: 多线程环境下的日志滚动测试。这条消息来自线程 3，消息编号 2，时间戳 1752226645822
2025-07-11 17:37:25.823 [pool-1-thread-2] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-2] - 线程 2 消息 2: 多线程环境下的日志滚动测试。这条消息来自线程 2，消息编号 2，时间戳 1752226645822
2025-07-11 17:37:25.823 [pool-1-thread-1] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-1] - 线程 1 消息 2: 多线程环境下的日志滚动测试。这条消息来自线程 1，消息编号 2，时间戳 1752226645822
2025-07-11 17:37:25.834 [pool-1-thread-2] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-2] - 线程 2 消息 3: 多线程环境下的日志滚动测试。这条消息来自线程 2，消息编号 3，时间戳 1752226645833
2025-07-11 17:37:25.834 [pool-1-thread-1] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-1] - 线程 1 消息 3: 多线程环境下的日志滚动测试。这条消息来自线程 1，消息编号 3，时间戳 1752226645833
2025-07-11 17:37:25.834 [pool-1-thread-3] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-3] - 线程 3 消息 3: 多线程环境下的日志滚动测试。这条消息来自线程 3，消息编号 3，时间戳 1752226645833
2025-07-11 17:37:25.845 [pool-1-thread-3] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-3] - 线程 3 消息 4: 多线程环境下的日志滚动测试。这条消息来自线程 3，消息编号 4，时间戳 1752226645845
2025-07-11 17:37:25.845 [pool-1-thread-2] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-2] - 线程 2 消息 4: 多线程环境下的日志滚动测试。这条消息来自线程 2，消息编号 4，时间戳 1752226645845
2025-07-11 17:37:25.845 [pool-1-thread-1] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-1] - 线程 1 消息 4: 多线程环境下的日志滚动测试。这条消息来自线程 1，消息编号 4，时间戳 1752226645845
2025-07-11 17:37:25.855 [pool-1-thread-1] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-1] - 线程 1 消息 5: 多线程环境下的日志滚动测试。这条消息来自线程 1，消息编号 5，时间戳 1752226645855
2025-07-11 17:37:25.856 [pool-1-thread-3] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-3] - 线程 3 消息 5: 多线程环境下的日志滚动测试。这条消息来自线程 3，消息编号 5，时间戳 1752226645856
2025-07-11 17:37:25.856 [pool-1-thread-2] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-2] - 线程 2 消息 5: 多线程环境下的日志滚动测试。这条消息来自线程 2，消息编号 5，时间戳 1752226645856
2025-07-11 17:37:25.866 [pool-1-thread-1] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-1] - 线程 1 消息 6: 多线程环境下的日志滚动测试。这条消息来自线程 1，消息编号 6，时间戳 1752226645865
2025-07-11 17:37:25.869 [pool-1-thread-3] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-3] - 线程 3 消息 6: 多线程环境下的日志滚动测试。这条消息来自线程 3，消息编号 6，时间戳 1752226645869
2025-07-11 17:37:25.869 [pool-1-thread-2] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-2] - 线程 2 消息 6: 多线程环境下的日志滚动测试。这条消息来自线程 2，消息编号 6，时间戳 1752226645869
