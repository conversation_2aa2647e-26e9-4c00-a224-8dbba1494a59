2025-07-11 17:37:25.947 [pool-1-thread-1] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-1] - 线程 1 消息 13: 多线程环境下的日志滚动测试。这条消息来自线程 1，消息编号 13，时间戳 1752226645947
2025-07-11 17:37:25.952 [pool-1-thread-3] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-3] - 线程 3 消息 13: 多线程环境下的日志滚动测试。这条消息来自线程 3，消息编号 13，时间戳 1752226645952
2025-07-11 17:37:25.953 [pool-1-thread-2] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-2] - 线程 2 消息 13: 多线程环境下的日志滚动测试。这条消息来自线程 2，消息编号 13，时间戳 1752226645953
2025-07-11 17:37:25.960 [pool-1-thread-1] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-1] - 线程 1 消息 14: 多线程环境下的日志滚动测试。这条消息来自线程 1，消息编号 14，时间戳 1752226645960
2025-07-11 17:37:25.963 [pool-1-thread-3] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-3] - 线程 3 消息 14: 多线程环境下的日志滚动测试。这条消息来自线程 3，消息编号 14，时间戳 1752226645963
2025-07-11 17:37:25.964 [pool-1-thread-2] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-2] - 线程 2 消息 14: 多线程环境下的日志滚动测试。这条消息来自线程 2，消息编号 14，时间戳 1752226645964
2025-07-11 17:37:25.973 [pool-1-thread-1] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-1] - 线程 1 消息 15: 多线程环境下的日志滚动测试。这条消息来自线程 1，消息编号 15，时间戳 1752226645972
2025-07-11 17:37:25.973 [pool-1-thread-3] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-3] - 线程 3 消息 15: 多线程环境下的日志滚动测试。这条消息来自线程 3，消息编号 15，时间戳 1752226645973
2025-07-11 17:37:25.976 [pool-1-thread-2] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-2] - 线程 2 消息 15: 多线程环境下的日志滚动测试。这条消息来自线程 2，消息编号 15，时间戳 1752226645976
2025-07-11 17:37:25.984 [pool-1-thread-3] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-3] - 线程 3 消息 16: 多线程环境下的日志滚动测试。这条消息来自线程 3，消息编号 16，时间戳 1752226645984
2025-07-11 17:37:25.985 [pool-1-thread-1] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-1] - 线程 1 消息 16: 多线程环境下的日志滚动测试。这条消息来自线程 1，消息编号 16，时间戳 1752226645985
2025-07-11 17:37:25.989 [pool-1-thread-2] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-2] - 线程 2 消息 16: 多线程环境下的日志滚动测试。这条消息来自线程 2，消息编号 16，时间戳 1752226645989
2025-07-11 17:37:25.996 [pool-1-thread-3] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-3] - 线程 3 消息 17: 多线程环境下的日志滚动测试。这条消息来自线程 3，消息编号 17，时间戳 1752226645996
2025-07-11 17:37:25.997 [pool-1-thread-1] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-1] - 线程 1 消息 17: 多线程环境下的日志滚动测试。这条消息来自线程 1，消息编号 17，时间戳 1752226645997
2025-07-11 17:37:26.001 [pool-1-thread-2] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-2] - 线程 2 消息 17: 多线程环境下的日志滚动测试。这条消息来自线程 2，消息编号 17，时间戳 1752226646000
2025-07-11 17:37:26.008 [pool-1-thread-3] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-3] - 线程 3 消息 18: 多线程环境下的日志滚动测试。这条消息来自线程 3，消息编号 18，时间戳 1752226646008
2025-07-11 17:37:26.008 [pool-1-thread-1] INFO  com.mhome.logging.RollingDemo.lambda$0(RollingDemo.java:165) [Thread-1] - 线程 1 消息 18: 多线程环境下的日志滚动测试。这条消息来自线程 1，消息编号 18，时间戳 1752226646008
