2025-07-11 17:37:25.742 [main] INFO  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:128) - 混合滚动策略演示消息 51: 这条消息用于演示同时按日期和文件大小进行滚动的功能。当文件达到指定大小时会创建新文件，同时保持日期信息。时间戳: 1752226645742
2025-07-11 17:37:25.744 [main] WARN  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:130) - 警告消息 51: 系统资源使用情况需要关注
2025-07-11 17:37:25.744 [main] INFO  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:128) - 混合滚动策略演示消息 52: 这条消息用于演示同时按日期和文件大小进行滚动的功能。当文件达到指定大小时会创建新文件，同时保持日期信息。时间戳: 1752226645744
2025-07-11 17:37:25.745 [main] WARN  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:130) - 警告消息 52: 系统资源使用情况需要关注
2025-07-11 17:37:25.745 [main] INFO  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:128) - 混合滚动策略演示消息 53: 这条消息用于演示同时按日期和文件大小进行滚动的功能。当文件达到指定大小时会创建新文件，同时保持日期信息。时间戳: 1752226645745
2025-07-11 17:37:25.745 [main] WARN  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:130) - 警告消息 53: 系统资源使用情况需要关注
2025-07-11 17:37:25.745 [main] INFO  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:128) - 混合滚动策略演示消息 54: 这条消息用于演示同时按日期和文件大小进行滚动的功能。当文件达到指定大小时会创建新文件，同时保持日期信息。时间戳: 1752226645745
2025-07-11 17:37:25.745 [main] WARN  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:130) - 警告消息 54: 系统资源使用情况需要关注
2025-07-11 17:37:25.745 [main] INFO  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:128) - 混合滚动策略演示消息 55: 这条消息用于演示同时按日期和文件大小进行滚动的功能。当文件达到指定大小时会创建新文件，同时保持日期信息。时间戳: 1752226645745
2025-07-11 17:37:25.746 [main] WARN  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:130) - 警告消息 55: 系统资源使用情况需要关注
2025-07-11 17:37:25.746 [main] INFO  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:128) - 混合滚动策略演示消息 56: 这条消息用于演示同时按日期和文件大小进行滚动的功能。当文件达到指定大小时会创建新文件，同时保持日期信息。时间戳: 1752226645746
2025-07-11 17:37:25.746 [main] WARN  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:130) - 警告消息 56: 系统资源使用情况需要关注
2025-07-11 17:37:25.746 [main] INFO  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:128) - 混合滚动策略演示消息 57: 这条消息用于演示同时按日期和文件大小进行滚动的功能。当文件达到指定大小时会创建新文件，同时保持日期信息。时间戳: 1752226645746
