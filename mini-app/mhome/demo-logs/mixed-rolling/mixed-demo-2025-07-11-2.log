2025-07-11 17:37:25.753 [main] INFO  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:128) - 混合滚动策略演示消息 64: 这条消息用于演示同时按日期和文件大小进行滚动的功能。当文件达到指定大小时会创建新文件，同时保持日期信息。时间戳: 1752226645753
2025-07-11 17:37:25.793 [main] WARN  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:130) - 警告消息 64: 系统资源使用情况需要关注
2025-07-11 17:37:25.793 [main] INFO  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:128) - 混合滚动策略演示消息 65: 这条消息用于演示同时按日期和文件大小进行滚动的功能。当文件达到指定大小时会创建新文件，同时保持日期信息。时间戳: 1752226645793
2025-07-11 17:37:25.793 [main] WARN  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:130) - 警告消息 65: 系统资源使用情况需要关注
2025-07-11 17:37:25.793 [main] INFO  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:128) - 混合滚动策略演示消息 66: 这条消息用于演示同时按日期和文件大小进行滚动的功能。当文件达到指定大小时会创建新文件，同时保持日期信息。时间戳: 1752226645793
2025-07-11 17:37:25.793 [main] WARN  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:130) - 警告消息 66: 系统资源使用情况需要关注
2025-07-11 17:37:25.793 [main] INFO  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:128) - 混合滚动策略演示消息 67: 这条消息用于演示同时按日期和文件大小进行滚动的功能。当文件达到指定大小时会创建新文件，同时保持日期信息。时间戳: 1752226645793
2025-07-11 17:37:25.793 [main] WARN  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:130) - 警告消息 67: 系统资源使用情况需要关注
2025-07-11 17:37:25.793 [main] INFO  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:128) - 混合滚动策略演示消息 68: 这条消息用于演示同时按日期和文件大小进行滚动的功能。当文件达到指定大小时会创建新文件，同时保持日期信息。时间戳: 1752226645793
2025-07-11 17:37:25.794 [main] WARN  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:130) - 警告消息 68: 系统资源使用情况需要关注
2025-07-11 17:37:25.794 [main] INFO  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:128) - 混合滚动策略演示消息 69: 这条消息用于演示同时按日期和文件大小进行滚动的功能。当文件达到指定大小时会创建新文件，同时保持日期信息。时间戳: 1752226645794
2025-07-11 17:37:25.794 [main] WARN  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:130) - 警告消息 69: 系统资源使用情况需要关注
2025-07-11 17:37:25.794 [main] INFO  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:128) - 混合滚动策略演示消息 70: 这条消息用于演示同时按日期和文件大小进行滚动的功能。当文件达到指定大小时会创建新文件，同时保持日期信息。时间戳: 1752226645794
