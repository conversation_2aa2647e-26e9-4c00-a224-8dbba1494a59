2025-07-11 17:37:25.736 [main] WARN  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:130) - 警告消息 44: 系统资源使用情况需要关注
2025-07-11 17:37:25.739 [main] INFO  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:128) - 混合滚动策略演示消息 45: 这条消息用于演示同时按日期和文件大小进行滚动的功能。当文件达到指定大小时会创建新文件，同时保持日期信息。时间戳: 1752226645739
2025-07-11 17:37:25.739 [main] WARN  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:130) - 警告消息 45: 系统资源使用情况需要关注
2025-07-11 17:37:25.739 [main] INFO  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:128) - 混合滚动策略演示消息 46: 这条消息用于演示同时按日期和文件大小进行滚动的功能。当文件达到指定大小时会创建新文件，同时保持日期信息。时间戳: 1752226645739
2025-07-11 17:37:25.740 [main] WARN  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:130) - 警告消息 46: 系统资源使用情况需要关注
2025-07-11 17:37:25.741 [main] INFO  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:128) - 混合滚动策略演示消息 47: 这条消息用于演示同时按日期和文件大小进行滚动的功能。当文件达到指定大小时会创建新文件，同时保持日期信息。时间戳: 1752226645741
2025-07-11 17:37:25.741 [main] WARN  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:130) - 警告消息 47: 系统资源使用情况需要关注
2025-07-11 17:37:25.741 [main] INFO  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:128) - 混合滚动策略演示消息 48: 这条消息用于演示同时按日期和文件大小进行滚动的功能。当文件达到指定大小时会创建新文件，同时保持日期信息。时间戳: 1752226645741
2025-07-11 17:37:25.741 [main] WARN  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:130) - 警告消息 48: 系统资源使用情况需要关注
2025-07-11 17:37:25.742 [main] INFO  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:128) - 混合滚动策略演示消息 49: 这条消息用于演示同时按日期和文件大小进行滚动的功能。当文件达到指定大小时会创建新文件，同时保持日期信息。时间戳: 1752226645742
2025-07-11 17:37:25.742 [main] WARN  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:130) - 警告消息 49: 系统资源使用情况需要关注
2025-07-11 17:37:25.742 [main] INFO  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:128) - 混合滚动策略演示消息 50: 这条消息用于演示同时按日期和文件大小进行滚动的功能。当文件达到指定大小时会创建新文件，同时保持日期信息。时间戳: 1752226645742
2025-07-11 17:37:25.742 [main] WARN  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:130) - 警告消息 50: 系统资源使用情况需要关注
