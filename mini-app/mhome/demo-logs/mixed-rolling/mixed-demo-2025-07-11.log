2025-07-11 17:37:25.800 [main] INFO  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:128) - 混合滚动策略演示消息 77: 这条消息用于演示同时按日期和文件大小进行滚动的功能。当文件达到指定大小时会创建新文件，同时保持日期信息。时间戳: 1752226645800
2025-07-11 17:37:25.803 [main] WARN  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:130) - 警告消息 77: 系统资源使用情况需要关注
2025-07-11 17:37:25.804 [main] INFO  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:128) - 混合滚动策略演示消息 78: 这条消息用于演示同时按日期和文件大小进行滚动的功能。当文件达到指定大小时会创建新文件，同时保持日期信息。时间戳: 1752226645804
2025-07-11 17:37:25.804 [main] WARN  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:130) - 警告消息 78: 系统资源使用情况需要关注
2025-07-11 17:37:25.804 [main] INFO  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:128) - 混合滚动策略演示消息 79: 这条消息用于演示同时按日期和文件大小进行滚动的功能。当文件达到指定大小时会创建新文件，同时保持日期信息。时间戳: 1752226645804
2025-07-11 17:37:25.805 [main] WARN  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:130) - 警告消息 79: 系统资源使用情况需要关注
2025-07-11 17:37:25.805 [main] INFO  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:128) - 混合滚动策略演示消息 80: 这条消息用于演示同时按日期和文件大小进行滚动的功能。当文件达到指定大小时会创建新文件，同时保持日期信息。时间戳: 1752226645805
2025-07-11 17:37:25.805 [main] WARN  com.mhome.logging.RollingDemo.demonstrateMixedRolling(RollingDemo.java:130) - 警告消息 80: 系统资源使用情况需要关注
